AF_Cluster/data_sep2022

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Cursor IDE
.cursor/*
!.cursor/rules/
.cursorignore
.cursorindexingignore
# node_modules/
# package-lock.json

# Jupyter Notebook
.ipynb_checkpoints

# Backup files
*.bak
*~
*.swp
*.swo

# Project specific
deprecated/
project.mdc*
# Exclude all of AF_Cluster except scripts directory
AF_Cluster/
!AF_Cluster/scripts/
# Specific directories to exclude in case they're accidentally matched by the exception above
AF_Cluster/msas/
AF_Cluster/data_sep2022/
AF_Cluster/pca

# Logs
*.log

# Added by <PERSON> Task Master
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/

# Added by Claude Task Master
node_modules/

# Test output directories
test_*_output/
msa/test_*_output/