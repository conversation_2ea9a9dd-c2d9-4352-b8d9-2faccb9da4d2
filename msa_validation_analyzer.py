#!/usr/bin/env python3
"""
Unified MSA Validation Analyzer for AlphaFold3 Pipeline

This script performs comprehensive MSA validation by combining:
1. Cross-validation between MSAsub_{nn} and Cluster_*/msasub_{nn} locations
2. Full MSA contamination detection using original MSA files
3. Sequence-level comparison for consistency and contamination identification
4. Unified reporting with actionable recommendations

Key Features:
- Unified workflow for both cross-validation and contamination detection
- Efficient sequence-based comparison using set operations
- Parallel processing for large-scale analysis
- Comprehensive reporting with clear recommendations
- Directory structure auto-detection and validation

Author: AI Assistant
Date: 2024
"""

import os, sys, re, glob, json, argparse
import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional, Any
from collections import defaultdict

# Add utils and logger to path
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir not in sys.path:
    sys.path.append(script_dir)

import logging
from types import MethodType
from logger_config import create_hierarchical_logger
from utils.os_utils import read_input
from utils.parallel_utils import parallel_process


# ================================
# Core Utility Functions
# ================================

def extract_sequences_from_a3m(
    file_path: str  # Path to A3M file
) -> Set[str]:
    """
    Extract unique protein sequences from an A3M file.
    
    Parameters
    ----------
    file_path (str):
        Path to the A3M file.
        
    Returns
    -------
    Set[str]:
        Set of unique protein sequences (cleaned and normalized).
    """
    sequences = set()
    
    try:
        lines = read_input(file_path)
        current_sequence = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith('>'):
                if current_sequence:
                    clean_seq = current_sequence.replace('-', '').replace('.', '').upper()
                    if clean_seq and len(clean_seq) > 10:  # Filter very short sequences
                        sequences.add(clean_seq)
                    current_sequence = ""
            else:
                current_sequence += line
        
        # Add last sequence
        if current_sequence:
            clean_seq = current_sequence.replace('-', '').replace('.', '').upper()
            if clean_seq and len(clean_seq) > 10:
                sequences.add(clean_seq)
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return sequences


def extract_sequences_from_json_msa(
    msa_content: str  # MSA content string from JSON
) -> Set[str]:
    """
    Extract unique protein sequences from MSA content in JSON format.
    
    Parameters
    ----------
    msa_content (str):
        MSA content as string from JSON file.
        
    Returns
    -------
    Set[str]:
        Set of unique protein sequences (cleaned and normalized).
    """
    sequences = set()
    
    if not msa_content or not isinstance(msa_content, str):
        return sequences
    
    try:
        lines = msa_content.strip().split('\n')
        current_sequence = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith('>'):
                if current_sequence:
                    clean_seq = current_sequence.replace('-', '').replace('.', '').upper()
                    if clean_seq and len(clean_seq) > 10:
                        sequences.add(clean_seq)
                    current_sequence = ""
            else:
                current_sequence += line
        
        # Add last sequence
        if current_sequence:
            clean_seq = current_sequence.replace('-', '').replace('.', '').upper()
            if clean_seq and len(clean_seq) > 10:
                sequences.add(clean_seq)
                
    except Exception as e:
        print(f"Error parsing MSA content: {e}")
        
    return sequences


def load_json_msa_data(
    json_file_path: str  # Path to JSON file
) -> Dict[str, Any]:
    """
    Load MSA data from AlphaFold3 JSON input file.
    
    Parameters
    ----------
    json_file_path (str):
        Path to the JSON file containing MSA data.
        
    Returns
    -------
    Dict[str, Any]:
        Dictionary containing MSA data for each chain.
    """
    msa_data = {
        'file_path': json_file_path,
        'chains': {},
        'total_sequences': 0,
        'load_success': False
    }
    
    try:
        with open(json_file_path, 'r') as f:
            data = json.load(f)
        
        if 'sequences' not in data:
            return msa_data
        
        for idx, seq_info in enumerate(data['sequences']):
            if 'protein' not in seq_info:
                continue
                
            protein_info = seq_info['protein']
            chain_id = protein_info.get('id', f'chain_{idx}')
            
            # Extract unpaired and paired MSA
            unpaired_msa = protein_info.get('unpairedMsa', '')
            paired_msa = protein_info.get('pairedMsa', '')
            
            # Extract sequences from MSA content
            unpaired_sequences = extract_sequences_from_json_msa(unpaired_msa)
            paired_sequences = extract_sequences_from_json_msa(paired_msa)
            
            msa_data['chains'][chain_id] = {
                'sequence': protein_info.get('sequence', ''),
                'unpaired_sequences': unpaired_sequences,
                'paired_sequences': paired_sequences,
                'total_sequences': len(unpaired_sequences) + len(paired_sequences)
            }
            
            msa_data['total_sequences'] += len(unpaired_sequences) + len(paired_sequences)
        
        msa_data['load_success'] = True
        
    except Exception as e:
        print(f"Error loading JSON file {json_file_path}: {e}")
        msa_data['error'] = str(e)
    
    return msa_data


def calculate_jaccard_similarity(
    set1: Set[str],  # First set of sequences
    set2: Set[str]   # Second set of sequences
) -> float:
    """
    Calculate Jaccard similarity between two sets of sequences.
    
    Parameters
    ----------
    set1 (Set[str]):
        First set of sequences.
    set2 (Set[str]):
        Second set of sequences.
        
    Returns
    -------
    float:
        Jaccard similarity score (0.0 to 1.0).
    """
    if not set1 and not set2:
        return 1.0  # Both empty
    
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    return intersection / union if union > 0 else 0.0


# ================================
# Directory and File Management
# ================================

def find_original_msa_files(
    system_directory: str,  # System root directory
    logger  # Logger instance
) -> Dict[str, str]:
    """
    Find original MSA files in the MSA_JKHmmer directory.
    
    Parameters
    ----------
    system_directory (str):
        System root directory containing MSA_JKHmmer subdirectory.
    logger:
        Logger instance.
        
    Returns
    -------
    Dict[str, str]:
        Dictionary mapping chain IDs to their original MSA file paths.
    """
    msa_jkhmmer_dir = os.path.join(system_directory, "MSA_JKHmmer")
    logger.sub_info(f"Searching for original MSA files in: {msa_jkhmmer_dir}")
    
    msa_mapping = {}
    
    if not os.path.exists(msa_jkhmmer_dir):
        logger.warning(f"MSA_JKHmmer directory does not exist: {msa_jkhmmer_dir}")
        return msa_mapping
    
    # Find A3M files with pattern: {system_name}_{chain_id}.a3m
    a3m_files = glob.glob(os.path.join(msa_jkhmmer_dir, "*.a3m"))
    
    if a3m_files:
        logger.sub_info(f"Found {len(a3m_files)} A3M files in MSA_JKHmmer directory")
        
        for a3m_file in a3m_files:
            filename = os.path.basename(a3m_file)
            # Extract chain ID from filename (e.g., Asec_695E1E2_clvg_101.a3m -> 101)
            if '_' in filename:
                parts = filename.replace('.a3m', '').split('_')
                if len(parts) >= 2:
                    chain_id = parts[-1]  # Last part is chain ID (101, 102, etc.)
                    msa_mapping[chain_id] = a3m_file
                    logger.sub_info(f"  Chain {chain_id}: {a3m_file}")
    
    if not msa_mapping:
        logger.warning("No original MSA files found with expected naming pattern")
    
    return msa_mapping


def find_validation_pairs(
    system_directory: str,  # System root directory
    cluster_subdir: str,    # Cluster subdirectory name (e.g., "Cluster_wo_tmpl")
    logger  # Logger instance
) -> List[Tuple[str, str, str]]:
    """
    Find MSAsub and corresponding cluster JSON files for validation.
    
    Parameters
    ----------
    system_directory (str):
        System root directory.
    cluster_subdir (str):
        Cluster subdirectory name.
    logger:
        Logger instance.
        
    Returns
    -------
    List[Tuple[str, str, str]]:
        List of tuples containing (clustering_number, msasub_json_path, cluster_json_path).
    """
    validation_pairs = []
    
    # Find MSAsub directories
    msasub_pattern = os.path.join(system_directory, "MSA_JKHmmer", "MSAsub_*")
    msasub_dirs = glob.glob(msasub_pattern)
    
    logger.sub_info(f"Found {len(msasub_dirs)} MSAsub directories")
    
    for msasub_dir in msasub_dirs:
        # Extract clustering number
        msasub_name = os.path.basename(msasub_dir)
        clustering_number = msasub_name.replace('MSAsub_', '')
        
        # Find corresponding cluster directory
        cluster_pattern = os.path.join(system_directory, "AF3", cluster_subdir, f"msasub_{clustering_number}")
        
        if os.path.exists(cluster_pattern):
            # Find JSON files in both directories
            msasub_json_files = glob.glob(os.path.join(msasub_dir, "*.json"))
            cluster_json_files = glob.glob(os.path.join(cluster_pattern, "*.json"))
            
            if msasub_json_files and cluster_json_files:
                msasub_json = msasub_json_files[0]
                cluster_json = cluster_json_files[0]
                validation_pairs.append((clustering_number, msasub_json, cluster_json))
                logger.sub_info(f"Added pair {clustering_number}: MSAsub <-> Cluster")
            else:
                logger.sub_info(f"Missing JSON files for clustering number {clustering_number}")
        else:
            logger.sub_info(f"No corresponding cluster directory for {clustering_number}")
    
    return validation_pairs


# ================================
# Analysis Functions
# ================================

def analyze_pair_worker(
    args: Tuple[str, str, str, Dict[str, str], float, float, str]  # Worker arguments
) -> Tuple[str, Dict[str, Any]]:
    """
    Worker function for parallel analysis of MSAsub-Cluster pairs.
    
    Parameters
    ----------
    args (Tuple):
        Arguments containing clustering_number, json files, original MSA mapping, thresholds, log_level.
        
    Returns
    -------
    Tuple[str, Dict[str, Any]]:
        Tuple containing (clustering_number, analysis_result).
    """
    clustering_number, msasub_json, cluster_json, original_msa_mapping, consistency_threshold, contamination_threshold, log_level = args
    
    # Create worker logger
    logger = logging.getLogger(f"worker_{os.getpid()}")
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, log_level, logging.INFO))
    
    # Fallback for custom log levels
    if not hasattr(logger, "sub_info"):
        logger.sub_info = MethodType(lambda self, msg, *a, **k: self.info(msg, *a, **k), logger)
    
    analysis_result = {
        'clustering_number': clustering_number,
        'msasub_json': msasub_json,
        'cluster_json': cluster_json,
        'cross_validation': {},
        'contamination_detection': {},
        'recommendation': 'KEEP'
    }
    
    # Load MSA data from both JSON files
    msasub_data = load_json_msa_data(msasub_json)
    cluster_data = load_json_msa_data(cluster_json)
    
    # Cross-validation analysis
    if msasub_data['load_success'] and cluster_data['load_success']:
        chain_similarities = []
        chain_details = {}
        
        # Compare common chains
        common_chains = set(msasub_data['chains'].keys()).intersection(
            set(cluster_data['chains'].keys())
        )
        
        for chain_id in common_chains:
            msasub_seqs = msasub_data['chains'][chain_id]['unpaired_sequences'].union(
                msasub_data['chains'][chain_id]['paired_sequences']
            )
            cluster_seqs = cluster_data['chains'][chain_id]['unpaired_sequences'].union(
                cluster_data['chains'][chain_id]['paired_sequences']
            )
            
            similarity = calculate_jaccard_similarity(msasub_seqs, cluster_seqs)
            chain_similarities.append(similarity)
            chain_details[chain_id] = {
                'similarity': similarity,
                'msasub_count': len(msasub_seqs),
                'cluster_count': len(cluster_seqs),
                'is_consistent': similarity >= consistency_threshold
            }
        
        overall_similarity = np.mean(chain_similarities) if chain_similarities else 0.0
        analysis_result['cross_validation'] = {
            'is_consistent': overall_similarity >= consistency_threshold,
            'overall_similarity': overall_similarity,
            'chain_details': chain_details
        }
    else:
        analysis_result['cross_validation'] = {
            'is_consistent': False,
            'error': 'Failed to load JSON files'
        }
    
    # Contamination detection (using MSAsub data)
    if msasub_data['load_success'] and original_msa_mapping:
        contamination_details = {}
        is_contaminated = False
        
        for chain_id, chain_data in msasub_data['chains'].items():
            # Find corresponding original MSA file
            original_msa_file = None
            for msa_chain_id, msa_file in original_msa_mapping.items():
                if chain_id == msa_chain_id or chain_id.replace('chain', '') == msa_chain_id:
                    original_msa_file = msa_file
                    break
            
            if original_msa_file and os.path.exists(original_msa_file):
                # Extract sequences from original MSA
                original_sequences = extract_sequences_from_a3m(original_msa_file)
                json_sequences = chain_data['unpaired_sequences'].union(chain_data['paired_sequences'])
                
                if original_sequences:
                    similarity_ratio = calculate_jaccard_similarity(original_sequences, json_sequences)
                    chain_is_contaminated = similarity_ratio >= contamination_threshold
                    
                    contamination_details[chain_id] = {
                        'is_contaminated': chain_is_contaminated,
                        'similarity_ratio': similarity_ratio,
                        'original_count': len(original_sequences),
                        'json_count': len(json_sequences)
                    }
                    
                    if chain_is_contaminated:
                        is_contaminated = True
        
        analysis_result['contamination_detection'] = {
            'is_contaminated': is_contaminated,
            'chain_details': contamination_details
        }
    else:
        analysis_result['contamination_detection'] = {
            'is_contaminated': False,
            'reason': 'No original MSA mapping available'
        }
    
    # Generate recommendation
    cross_val_consistent = analysis_result['cross_validation'].get('is_consistent', True)
    contamination_detected = analysis_result['contamination_detection'].get('is_contaminated', False)
    
    if not cross_val_consistent or contamination_detected:
        analysis_result['recommendation'] = 'REPROCESS'
        logger.sub_info(f"Clustering {clustering_number}: REPROCESS recommended")
    else:
        logger.sub_info(f"Clustering {clustering_number}: KEEP - validation passed")
    
    return clustering_number, analysis_result


def generate_unified_report(
    analysis_results: Dict[str, Dict[str, Any]],  # Analysis results
    output_file: str,  # Output report file
    logger  # Logger instance
) -> None:
    """
    Generate unified validation report.
    
    Parameters
    ----------
    analysis_results (Dict[str, Dict[str, Any]]):
        Analysis results for all clustering numbers.
    output_file (str):
        Path to output report file.
    logger:
        Logger instance.
        
    Returns
    -------
    None
    """
    report_data = []
    
    for clustering_number, analysis in analysis_results.items():
        cross_val = analysis.get('cross_validation', {})
        contamination = analysis.get('contamination_detection', {})
        
        row = {
            'clustering_number': clustering_number,
            'cross_validation_consistent': cross_val.get('is_consistent', False),
            'cross_validation_similarity': cross_val.get('overall_similarity', 0.0),
            'contamination_detected': contamination.get('is_contaminated', False),
            'num_contaminated_chains': len([
                chain for chain, details in contamination.get('chain_details', {}).items()
                if details.get('is_contaminated', False)
            ]),
            'recommendation': analysis.get('recommendation', 'KEEP'),
            'msasub_json': analysis.get('msasub_json', ''),
            'cluster_json': analysis.get('cluster_json', '')
        }
        report_data.append(row)
    
    # Create DataFrame and save
    df = pd.DataFrame(report_data)
    df = df.sort_values('clustering_number')
    df.to_csv(output_file, index=False, sep='\t')
    
    # Generate summary
    total_count = len(df)
    inconsistent_count = (~df['cross_validation_consistent']).sum()
    contaminated_count = df['contamination_detected'].sum()
    reprocess_count = (df['recommendation'] == 'REPROCESS').sum()
    
    logger.main_info(f"Generated unified validation report: {output_file}")
    logger.sub_info(f"Total clustering pairs analyzed: {total_count}")
    logger.sub_info(f"Cross-validation inconsistent: {inconsistent_count}")
    logger.sub_info(f"Contamination detected: {contaminated_count}")
    logger.sub_info(f"Require reprocessing: {reprocess_count}")
    logger.sub_info(f"Ready to keep: {total_count - reprocess_count}")


# ================================
# Main Function
# ================================

def main():
    """
    Main function for unified MSA validation analysis.
    """
    parser = argparse.ArgumentParser(description="Unified MSA Validation Analyzer")
    parser.add_argument(
        'system_directory', type=str,
        help='System root directory (e.g., /path/to/Asec_695E1E2_clvg)'
    )
    parser.add_argument(
        '--cluster-subdir', type=str, default='Cluster_wo_tmpl',
        help='Cluster subdirectory name (default: Cluster_wo_tmpl)'
    )
    parser.add_argument(
        '--consistency-threshold', type=float, default=0.95,
        help='Similarity threshold for cross-validation consistency (default: 0.95)'
    )
    parser.add_argument(
        '--contamination-threshold', type=float, default=0.95,
        help='Similarity threshold for contamination detection (default: 0.95)'
    )
    parser.add_argument(
        '--output-report', type=str, default='msa_unified_validation_report.tsv',
        help='Output file for validation report (default: msa_unified_validation_report.tsv)'
    )
    parser.add_argument(
        '--log-level', type=str, default='MAIN_INFO',
        choices=['DEBUG', 'INFO', 'MAIN_INFO', 'SUB_INFO', 'WARNING', 'ERROR'],
        help='Logging level (default: MAIN_INFO)'
    )
    parser.add_argument(
        '--n-workers', type=int, default=0,
        help='Number of parallel workers (0 for auto-detection, default: 0)'
    )
    
    args = parser.parse_args()
    
    # Setup hierarchical logging
    logger = create_hierarchical_logger(
        target_dir=args.system_directory,
        filename='msa_unified_validation.log',
        console_level=getattr(__import__('logging'), args.log_level, 25)
    )
    
    # Validate system directory
    if not os.path.exists(args.system_directory):
        logger.error(f"System directory does not exist: {args.system_directory}")
        sys.exit(1)
    
    logger.main_info("Starting unified MSA validation analysis")
    logger.sub_info(f"System directory: {args.system_directory}")
    logger.sub_info(f"Cluster subdirectory: {args.cluster_subdir}")
    logger.sub_info(f"Consistency threshold: {args.consistency_threshold}")
    logger.sub_info(f"Contamination threshold: {args.contamination_threshold}")
    
    # Find original MSA files
    logger.main_info("Finding original MSA files")
    original_msa_mapping = find_original_msa_files(args.system_directory, logger)
    
    # Find validation pairs
    logger.main_info("Finding MSAsub-Cluster pairs for validation")
    validation_pairs = find_validation_pairs(args.system_directory, args.cluster_subdir, logger)
    
    if not validation_pairs:
        logger.error("No validation pairs found")
        sys.exit(1)
    
    logger.sub_info(f"Found {len(validation_pairs)} validation pairs")
    
    # Determine number of workers
    import multiprocessing
    if args.n_workers <= 0:
        n_workers = max(1, multiprocessing.cpu_count() - 1)
        logger.sub_info(f"Using {n_workers} auto-detected workers")
    else:
        n_workers = min(args.n_workers, len(validation_pairs))
        logger.sub_info(f"Using {n_workers} specified workers")
    
    # Perform parallel analysis
    logger.main_info("Performing unified validation analysis (parallel processing)")
    
    # Prepare arguments for parallel processing
    worker_args = [
        (clustering_number, msasub_json, cluster_json, original_msa_mapping, 
         args.consistency_threshold, args.contamination_threshold, args.log_level)
        for clustering_number, msasub_json, cluster_json in validation_pairs
    ]
    
    # Use parallel processing
    results = parallel_process(
        items=worker_args,
        func=analyze_pair_worker,
        n_workers=n_workers,
        use_threads=True,  # I/O bound task
        show_progress=True,
        desc="Validating MSA pairs",
        filter_none=True
    )
    
    # Convert results to dictionary
    analysis_results = {}
    for clustering_number, result in results:
        analysis_results[clustering_number] = result
    
    # Generate unified report
    logger.main_info("Generating unified validation report")
    generate_unified_report(analysis_results, args.output_report, logger)
    
    # Final summary
    logger.main_info("Unified MSA validation analysis completed")
    logger.sub_info(f"Results saved to: {args.output_report}")


if __name__ == "__main__":
    main() 