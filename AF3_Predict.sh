#!/bin/bash

#set -e
proj_p=/home/<USER>/PROject/APP-complex/Initial_Input/src
config_p=${proj_p}/config
source MODELING_SYSTEMS.config


echo -e "[INFO ] SYSTEMS\n${systems[@]}"

for sys in "${systems[@]}"; do
    echo "[INFO ] Processing system: ${sys}"
    
    if ! cd "${proj_p}/../${sys}/" 2>/dev/null; then
        echo "[ERROR] Directory for ${sys} not found. Skipping"
        continue
    fi
    AF3_dir=$(realpath "${PWD}")
    echo "  INFO: Working in directory: ${AF3_dir}"

    
    #scripts=(AF3W_wo-tmpl.sh  AF3C_wo-tmpl.sh AF3W_w-tmpl.sh AF3C_w-tmpl.sh)
    scripts=(AF3C_wo-tmpl.sh AF3W_w-tmpl.sh AF3C_w-tmpl.sh)    
    for script in ${scripts[@]}; do
        cp ${proj_p}/bash_scripts/${script} ${PWD}
        bash ${script} > ${script/.sh/.log} 2>&1 #&
        echo ' --------------------------------------'
    done
done
