general:
  output_base_dir: './test_clustering_results'
  chain_poly_type: 'protein'
  max_depth: 500  # Limit MSA to 500 sequences for extended testing
  verbose: true

logging:
  level: 'INFO'
  format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
  date_format: '%Y-%m-%d %H:%M:%S'

encoding_methods:
  default: ['onehot', 'MSAtransformer']

clustering_methods:
  default: ['dbscan', 'hierarchical']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 6.0
      min_samples: 3
    optimization:
      min_eps: 3.0
      max_eps: 15.0
      eps_step: 1.0
      min_samples: [3, 5]
  hierarchical:
    defaults:
      linkage: 'ward'
    optimization:
      min_cluster_size: 5

output_settings:
  save_cluster_a3m_files: true
  save_analysis_results: true
  create_summary_report: true
  cluster_filename_format: '{filename_base}_{cluster_id:03d}.a3m'
  subdirectories:
    clusters: 'clusters'
    analysis: 'analysis'
    visualizations: 'visualizations'

analysis_settings:
  generate_composition_analysis: true
  calculate_quality_metrics: true

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'
  cluster_sequences: true
  create_pca: true
  create_tsne: true
  encoding_method: 'onehot'
  fallback_to_text_summary: true 