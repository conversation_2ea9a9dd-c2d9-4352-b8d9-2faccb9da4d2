#!/bin/bash
#SBATCH -J __JOB_NAME__
#SBATCH -o __OUT_NAME__.out

#SBATCH -p g4090_short
#SBATCH --qos=normal
#SBATCH --mem=0
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1


src="/home/<USER>/PROject/APP-complex/Initial_Input/src"
source ${src}/bash_scripts/source.sh

echo "[INFO ] Activate conda environment"
conda activate Draph

## Configuration
# Path definition
MSA_sub="${src}/AF_Cluster" # MSA clustering source code path
proj_p=$(realpath ${PWD}) # system project path
output_dir=AF3/Cluster_wo_tmpl && mkdir -p ${output_dir} # output directory
input_fa=./input.fa # input fasta file
msa_dir=./MSA_JKHmmer # MSA JKhmmer result directory
use_paired_cluster=FALSE

# [TODO] MSAsub_ output_prefix로 받게 수정
#n_cluster=
#output_prefix=


echo "[INFO ] Configure system and generate input.json file"
system_name=$(head -n1 ${input_fa} | sed 's/>//g')
# Check if input.json exists using the function from bash_utils
check_input_json "${proj_p}" "${input_fa}" "${system_name}" 
if [[ $? -ne 0 ]]; then
    exit 1
fi

echo "[INFO ] Run AlphaFold3 with msa-only (run_inference=false) for generating MSA"
# Check if MSA files exist using the function from bash_utils
check_msa_files "${proj_p}/${msa_dir}" "${system_name}" "_original"
if [[ $? -ne 0 ]]; then
    exit 1
fi

echo "[INFO ] Count the number of chains in ${input_fa}"
count_chains "${proj_p}/${input_fa}"
n_chains=$?


echo "[INFO ] Split ${system_name}.a3m MSA file depending on the chain id"
cd ${proj_p}/${msa_dir}
for ((i=101; i<=100+n_chains; i++)); do
    output_file="${system_name}_${i}.a3m"
    truncate -s 0 "${output_file}"
    if [[ $? -eq 0 ]]; then
        echo "  INFO: Initialized file ${output_file} file"
    else
        echo "  ERROR: Failed to initialize file ${output_file}"
    fi
done
run_python_cmd ${src}/extract_JKHmmer_MSA.py \
    --msa_dir ${proj_p}/${msa_dir} \
    --input_json ${proj_p}/${msa_dir}/${system_name,,}_data_original.json \
    --output_prefix ${system_name}


echo "[INFO ] Cluster MSA and generate cluster-subsampled MSA files"
cd ${proj_p}/${msa_dir}
chain_len=$(awk 'NR > 1 {gsub(":", ""); if ($0 ~ /^[A-Z]+$/) {print length($0)}}' ${proj_p}/${input_fa})
resid_ranges=()
start=1
for num in ${chain_len[@]}; do
    end=$(( start + num - 1 ))
    resid_ranges+=("$start,$end")
    echo "  INFO: added range $start,$end to resid_ranges"
done
echo "  INFO: resid_ranges in given fasta system: ${resid_ranges[@]}"

unpaired_cnt=0
for a3m_f in $(find . -maxdepth 1 -name "${system_name}_*.a3m" | sort -V); do
    if [[ ! -s "$a3m_f" ]]; then # Skip empty files
        echo "  INFO: Skipping empty file $a3m_f"
        continue
    fi

    keyword=$(echo ${a3m_f} | awk -F".a3m" '{print $1}')
    if [[ "$keyword" == *"paired"* ]]; then
        chain_num="${keyword##*${system_name}_}"
        addtive_cmd=""
    else
        chain_num="${keyword##*${system_name}}"
        chain_num="${chain_num//_/}"

        if [[ -z "${resid_ranges[$unpaired_cnt]}" ]]; then
            echo "[ERROR] resid_ranges array index out of bounds at ${unpaired_cnt}"
            exit 1
        fi
        index=${resid_ranges[$unpaired_cnt]}
        IFS=',' read -r start_idx end_idx <<< "$index"

        if [[ ${start_idx} -gt ${end_idx} ]]; then
            echo "[ERROR] start_idx is greater than end_idx at ${unpaired_cnt}"
            exit 1
        fi
        addtive_cmd="--unpaired --start_idx ${start_idx} --end_idx ${end_idx}"
        let unpaired_cnt=${unpaired_cnt}+1
    fi

    chain_dir=${proj_p}/${msa_dir}/chain${chain_num}
    mkdir -p "${chain_dir}"
    if [[ ! -f ${chain_dir}/${keyword}_000.a3m ]]; then
        if [[ ${keyword} == *"paired"* ]] && [[ ${use_paired_cluster} == "FALSE" ]]; then
            echo "  INFO: use_paired_cluster is set to FALSE"
            echo "  INFO: copying ${a3m_f} to ${proj_p}/${msa_dir}/chain${chain_num}"
            cp ${a3m_f} ${chain_dir}
        else
            echo "  INFO: there is no ${chain_dir}/${keyword}_000.a3m"
            # Original command (now uncommented)
            echo "   > running MSA clustering for ${a3m_f} MSA"
            python ${MSA_sub}/scripts/ClusterMSA.py ${keyword} \
                --gap_cutoff 0.25 -i ${a3m_f} ${addtive_cmd} \
                -o ${chain_dir} > _AFCluster_${chain_num}.log #2>&1

            if [[ ${?} -ne 0 ]]; then
                echo "   > Error: ${a3m_f} failed to clustering for subsampling"
                echo "   > copying ${a3m_f} to ${proj_p}/${msa_dir}/chain${chain_num}"
                cp ${a3m_f} ${chain_dir}
            fi
        fi
    else
        echo "  INFO: Cluster-subsampled MSA files already exist for ${a3m_f}"
    fi
    
    # Delete the original MSA file if there are more than 10 files in the directory
    num_files=$(find "${chain_dir}/chain${chain_num}" -maxdepth 1 -type f -name '*.a3m' | wc -l)
    if [[ ${num_files} -gt 10 ]] && [[ ${keyword} != *"paired"* ]]; then
        echo "  INFO: there are already ${num_files} files in ${chain_dir}/chain${chain_num}"
        rm -f "${chain_dir}/chain${chain_num}/${keyword}.a3m"  
    fi
    echo '  --------------------------------'
done

echo "[INFO ] Combinate the subsampled MSA files"
if [[ ${flag} != "True" ]]; then
    printf "  INFO: there are no some of combinated a3m files\n"
    run_python_cmd ${src}/combinate_a3m_json.py \
        "${proj_p}/${msa_dir}" \
        "--input_fasta=input.fa" \
        "--output_prefix=MSAsub" \
        "--msa_file=${system_name}.a3m" \
        "--use_paired"
else
    printf "\n  INFO: there are already combinated subsample MSA files\n"
fi


echo "[INFO ] Predicte Structure with AlphaFold2"
cd ${proj_p}
elements=( $(ls ${msa_dir} | grep MSAsub_ | sort -V) )
total_elements=${#elements[@]}
echo "  INFO: there are ${total_elements} subsampled MSA.a3m files"
batch_size=100
echo "  INFO: batch size: ${batch_size}"
num_batches=$(( (total_elements + batch_size - 1) / batch_size ))
echo "  INFO: # of batches: ${num_batches}"

mkdir -p ${proj_p}/slurm_script; cd ${proj_p}/slurm_script
for (( batch=0; batch<num_batches; batch++ )); do
    # Calculate start and end index for the current batch
    start=$(( batch * batch_size ))
    end=$(( start + batch_size - 1 ))

    # Ensure end index does not exceed the total number of elements
    if [ $end -ge $total_elements ]; then
        end=$(( total_elements - 1 ))
    fi

    batch_elements=("${elements[@]:$start:$((end-start+1))}")
    echo "  INFO: processing batch $((batch+1)) (elements $((start+1)) to $((end+1)))"

    partition=`slurm_partition | head -n1`
    cat <<EOF > ${proj_p}/slurm_script/AF3C_wo-tmpl_${batch}.sh
#!/bin/bash
#SBATCH -J ${system_name}_AF3C_wo-tmpl_${batch}
#SBATCH -o ${proj_p}/slurm_script/${system_name}_AF3C_wo-tmpl_${batch}.out
#SBATCH -p g4090_short,g3090_short,a5000_short
#SBATCH --exclude=gpu7

#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1
#SBATCH --qos=normal

source /home/<USER>/miniforge3/etc/profile.d/conda.sh
# conda activate /home/<USER>/.conda/envs/alphafold3/
which python
which ptxas
hostname

proj_p=${proj_p}
msa_dir=${msa_dir}
output_dir=${output_dir}
batch_elements=( ${batch_elements[@]} )

for a3m_dir in \${batch_elements[@]}; do
    msa_number=\$(echo \${a3m_dir} | awk -F'_' '{print \$2}')
    msa_prefix=\${proj_p}/${msa_dir}/\${a3m_dir}
    json_f=\$(ls \${msa_prefix} | grep msa | grep json | grep -v 'tmpl')

    out_prefix=\${proj_p}/\${output_dir}/
    mkdir -p \${out_prefix} && cd \${out_prefix}

    file_count=\$(find \${out_prefix}/msasub_\${msa_number} -name model.cif 2>/dev/null | wc -l)
    if [[ \${file_count} -ne 25 ]]; then
        echo "  INFO: predicting structure with \${json_f}"
        /appl/anaconda3/envs/AlphaFold3/bin/python -u /appl/git-repo/AlphaFold3/run_alphafold.py \\
            --run_inference=true --run_data_pipeline=false \\
            --json_path \${msa_prefix}/\${json_f} --output_dir . \\
            > AF3C_wo-tmpl_\${a3m_dir}.log 2>&1
    else
        echo "  INFO: there are already structure prediction results with \${json_f}"
    fi
done
EOF
    chmod 755 ${proj_p}/slurm_script/AF3C_wo-tmpl_${batch}.sh

    # Check whether the corresponding jobs is running
    squeue -u byun -o "%.13i %.12P %.80j %.5u %.8T %.10M" > sqme.log
    flag=`cat sqme.log | grep ${system_name}_AF3C_wo-tmpl_${batch} | awk '{print $5}'`
    if [[ ${flag} == 'PENDING' ]]; then
        echo "  INFO: PENDING"
    elif [[ ${flag} == 'RUNNING' ]]; then
        echo "  INFO: RUNNING"
    else
        echo -ne "  INFO: sbatch ${system_name}_MSAss-${batch} "
        sbatch ${proj_p}/slurm_script/AF3C_wo-tmpl_${batch}.sh
    fi
    rm -rf sqme.log
    #rm ${proj_p}/slurm_script/AF3C_wo-tmpl_${batch}.sh
    echo "  INFO: Batch $((batch+1)) completed."
    echo "----------------------------"
done
