#!/bin/bash

# Check if input.json exists and display system name information
check_input_json() {
    local proj_p="$1"
    local input_fa="$2"
    local system_name="$3"
    local required_script="$4"  # Script to run if input.json is missing

    echo """  Recommend:
    Please make simplify your system name at the beginning of ${input_fa} file.
    system_name is used for prefix of output file name and directory name.
"""
    if [[ ! -f ${proj_p}/input.json ]]; then
        echo """[WARNING]: there is no input.json file
You need to first run AlphaFold3 with msa-only (run_inference=false)! Please run ${required_script} first
"""
        return 1
    else
        echo "  INFO: system name written on ${proj_p}/${input_fa}: ${system_name}"
        return 0
    fi
}

# Check if MSA files exist and display appropriate messages
check_msa_files() {
    local msa_dir="$1"
    local system_name="$2"
    local data_json_suffix="$3"  # Optional suffix for data json file (e.g., "_original")

    if [[ ! -f ${msa_dir}/${system_name}.a3m ]] && [[ ! -f ${msa_dir}/${system_name,,}_data${data_json_suffix}.json ]]; then
        echo "  INFO: there is no MSA Jackhmmer result file."
        echo "[Warning] You need to first run AlphaFold3 with msa-only (run_inference=false)! Please run AF3W_wo-tmpl.sh first"
        return 1
    else
        echo "  INFO: there is already MSA results on ${system_name}"
        return 0
    fi
}

# Count the number of chains in the input fasta file
count_chains() {
    local input_fa="$1"

    # Count non-empty, non-header lines in the fasta file
    local n_chains=$(grep -v '>' "${input_fa}" | tr -d " " | wc -l)
    echo "  INFO: there are ${n_chains} chains in ${input_fa}"

    return ${n_chains}
}

# Check SLURM job status and take appropriate action
check_slurm_job() {
    local job_name="$1"       # Job name to check
    local script_path="$2"    # Path to the script to submit if job is not running
    local submit_job="$3"     # Whether to submit the job if not running (true/false)
    local temp_log="sqme.log" # Temporary file for squeue output

    # Get current user
    local user=$(whoami)

    # Check if job is running
    squeue -u ${user} -o "%.13i %.12P %.80j %.5u %.8T %.10M" > ${temp_log}
    local job_status=$(cat ${temp_log} | grep ${job_name} | awk '{print $5}')

    # Process based on job status
    if [[ ${job_status} == 'PENDING' ]]; then
        echo "  INFO: Job ${job_name} is PENDING"
        local result=1
    elif [[ ${job_status} == 'RUNNING' ]]; then
        echo "  INFO: Job ${job_name} is RUNNING"
        local result=2
    elif [[ -n "${job_status}" ]]; then
        # Other states like COMPLETING, STOPPED, etc.
        echo "  INFO: Job ${job_name} is in state: ${job_status}"
        local result=3
    else
        echo "  INFO: Job ${job_name} is not running"
        if [[ "${submit_job}" == "true" && -f "${script_path}" ]]; then
            echo -ne "  INFO: Submitting job ${job_name} "
            sbatch ${script_path}
            local result=4
        else
            local result=0
        fi
    fi

    # Clean up
    rm -f ${temp_log}

    return ${result}
}

run_python_cmd() {
    local cmd="$1"
    local args=("${@:2}")
    local current_param=""
    local script_name=$(basename "$cmd")

    # Print command with nice formatting
    echo "   > cmd: ${cmd} \\"

    # Process args to group parameters with their values
    for arg in "${args[@]}"; do
        if [[ "$arg" == --* ]]; then
            # This is a new parameter
            if [[ -n "$current_param" ]]; then
                # Output the previous parameter group
                echo "        $current_param \\"
            fi
            current_param="$arg"
        else
            # This is a value for the current parameter
            current_param="$current_param $arg"
        fi
    done

    # Output the last parameter group
    if [[ -n "$current_param" ]]; then
        echo "        $current_param \\"
    fi

    echo ""
    python ${cmd} "${args[@]}" # Execute the command

    # Check if command executed successfully
    if [ $? -eq 0 ]; then
        echo "[INFO ] Successfully completed ${script_name}"
    else
        echo """[WARNING] ${script_name} execution failed with errors!
        Please check the error message and the log file.
        """
        exit 1
    fi
    echo ""
}