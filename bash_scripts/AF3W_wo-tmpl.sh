#!/bin/bash
#SBATCH -J __SYSTEM___AF3W_wo-tmpl
#SBATCH -o __SYSTEM___AF3W_wo-tmpl.out

#SBATCH -p skylake_short,rome_short
#SBATCH --exclude=cpu[1-2],cpu10

#SBATCH --qos=normal
#SBATCH --mem=0
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8

src="/home/<USER>/PROject/APP-complex/Initial_Input/src"
source ${src}/bash_scripts/source.sh

echo "[INFO ] Activate conda environment"
conda activate Draph


# Path definition
proj_p=$(realpath ${PWD})
output_dir=AF3/Full_wo_tmpl && mkdir -p ${output_dir}
input_fa=./input.fa
msa_dir=MSA_JKHmmer && mkdir -p ${msa_dir}

echo "[INFO ] Generate input json file (input.json) for AF3"
system_name=$(head -n1 ${input_fa} | sed 's/>//g')
echo "  INFO: system name written on ${input_fa}: ${system_name}"
echo "  Recommend: Please make simplify your system name at the beginning of ${input_fa} file"
echo "  INFO: generating input.json file"
# [TODO] run_python_cmd 함수로 대체 (25.4.18)
cmd="python ${src}/gen_input_json.py --input_fasta_file ${proj_p}/${input_fa} --model_seeds ${all_seeds[@]} --output_json_file ${proj_p}/input.json"
echo "   > cmd: ${cmd}" && ${cmd}


echo "[INFO ] Run AlphaFold3 with msa-only (run_inference=false) for generating MSA"

out_prefix=${proj_p}/${output_dir}
json_f=$(ls ${proj_p}/input.json | grep -v '~')
flag=( $(find ${out_prefix}/${system_name,,} -name ${system_name,,}_data.json 2>/dev/null) )
if [[ ${#flag[@]} -ne 1 ]]; then
    echo "  INFO: flag == "${flag}". Perform JKhmmer for MSA."
    /appl/anaconda3/envs/AlphaFold3/bin/python -u /appl/git-repo/AlphaFold3/run_alphafold.py \
        --run_inference=false \
        --run_data_pipeline=true \
        --json_path ${json_f} \
        --output_dir ${out_prefix}
else
    echo "  INFO: data_pipeline flag is True. there is already MSA results of ${json_f}"
fi

echo "[INFO ] Copy initial json file to MSA path (${msa_dir})"
if [[ ! -f ${proj_p}/${msa_dir}/${system_name,,}_data_original.json ]]; then
    echo "  cp ${out_prefix}/${system_name}/${system_name}_data.json ${proj_p}/${msa_dir}/${system_name,,}_data_original.json"
    timestamp=$(date +%Y%m%d_%H%M%S)
    cp ${out_prefix}/${system_name,,}/${system_name,,}_data.json ${proj_p}/${msa_dir}/${system_name,,}_data_original.json
    cp ${out_prefix}/${system_name,,}/${system_name,,}_data.json ${proj_p}/${msa_dir}/${system_name,,}_data_original_${timestamp}.json    
    echo "  INFO: created original data file backup"
else
    echo "  INFO: Original data file backup already exists, skipping copy"
fi


echo "[INFO ] Collect existing seeds from the output directory"
AF3W_output_dir="${out_prefix}/${system_name,,}"
echo "  INFO: checking for existing seeds in ${AF3W_output_dir}"

existing_seeds=()
if [[ -d "${AF3W_output_dir}" ]]; then
    # Find all seed directories and extract seed numbers
    existing_seeds=(
        $(ls "${AF3W_output_dir}" 2>/dev/null |
        grep -E '^seed-[0-9]+' |
        sed -E 's/seed-([0-9]+)_.*/\1/' | sort -u)
    )
    echo "  INFO: found ${#existing_seeds[@]} existing seeds: ${existing_seeds[*]}"
fi

# Extract missing seeds
missing_seeds=()
for s in "${all_seeds[@]}"; do
    # Check if seed exists in the existing_seeds array
    if [[ ! " ${existing_seeds[*]} " =~ " $s " ]]; then
        missing_seeds+=($s)
    fi
done
echo "  INFO: missing ${#missing_seeds[@]} seeds: ${missing_seeds[*]}"

# If there are missing seeds, create a new JSON file with only those seeds
if [[ ${#missing_seeds[@]} -gt 0 ]]; then
    echo "  INFO: Creating JSON file with missing seeds only"

    # Create a temporary JSON file with missing seeds
    resume_json=${out_prefix}/${system_name,,}/${system_name,,}_data.json
    
    # Convert missing_seeds array to JSON format
    seeds_json=$(printf '%s\n' "${missing_seeds[@]}" | jq -R . | jq -s .)
        
    # Use jq to update the modelSeeds field in the JSON
    # Update the JSON file
    jq --argjson seeds "$seeds_json" '.modelSeeds = $seeds' < "${proj_p}/${msa_dir}/${system_name,,}_data_original.json"  \
        > ${resume_json}
    echo "  INFO: Created ${resume_json} with seeds: ${missing_seeds[*]}"
else
    echo "  INFO: No missing seeds found. All seeds have been processed."
fi


echo "[INFO ] Run AlphaFold3 structure prediction with run_data_pipeline=false and run_inference=true"
mkdir -p ${proj_p}/slurm_script && cd ${proj_p}/slurm_script
cat <<EOF > ${proj_p}/slurm_script/${system_name}_AF3W_wo-tmpl.sh
#!/bin/bash
#SBATCH -J ${system_name}_AF3W_wo-tmpl
#SBATCH -o ${proj_p}/slurm_script/${system_name}_AF3W_wo-tmpl.out
#SBATCH -p g3090_short,g4090_short

#SBATCH --ntasks=1
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=4
#SBATCH --qos=normal

source /home/<USER>/apps/cuda/cuda-12.6/.src_cuda-12.6
source /home/<USER>/miniconda3/etc/profile.d/conda.sh
conda activate /home/<USER>/.conda/envs/alphafold3/

which python

proj_p=${proj_p}
output_dir=${output_dir}
out_prefix=\${proj_p}/\${output_dir}

if [[ ! -f \${proj_p}/\${msa_dir}/\${system_name,,}.a3m ]]; then
    conda activate /appl/anaconda3/envs/ColabFold/
    echo "  INFO there is no MSA result file"
    cmd="colabfold_batch \${proj_p}/${input_fa} \${proj_p}/${msa_dir} --msa-only"
    echo "   > cmd: \${cmd}" && \${cmd}
else
    echo "  INFO: there is already MSA results on ${system_name}"
fi

file_count=\$(find \${out_prefix}/${system_name,,} -name model.cif 2>/dev/null | wc -l)
if [[ \${file_count} -ne 100 ]]; then
    json_f=\$(ls \${out_prefix}/${system_name,,} | grep "_data.json" | grep -v '~')
    echo "  INFO: predicting structure with \${json_f}"

    /appl/anaconda3/envs/AlphaFold3/bin/python -u /appl/git-repo/AlphaFold3/run_alphafold.py \\
        --run_inference=true --run_data_pipeline=false \\
        --json_path \${out_prefix}/${system_name,,}/\${json_f} --output_dir \${out_prefix}
else
    echo "  INFO: there are already AF3 structure prediction results of \${json_f}"
fi
EOF

# Check whether the corresponding jobs is running
squeue -u byun -o "%.13i %.12P %.80j %.5u %.8T %.10M" > sqme.log
flag=`cat sqme.log | grep ${system_name}_AF3W_wo-tmpl | awk '{print $5}'`
if [[ ${flag} == 'PENDING' ]]; then
    echo "  INFO: PENDING"
elif [[ ${flag} == 'RUNNING' ]]; then
    echo "  INFO: RUNNING"
else
    echo -ne "  INFO: sbatch ${system_name}"
    sbatch ${proj_p}/slurm_script/${system_name}_AF3W_wo-tmpl.sh
fi
rm -rf sqme.log
