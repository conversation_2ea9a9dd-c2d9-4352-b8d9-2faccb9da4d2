#!/bin/bash

echo "[INFO ] LOAD MODULES - conda, slurm_parition"
# Allow override via environment variable
USER_HOME=${USER_HOME:-/home/<USER>
# Check if conda setup exists before sourcing
if [[ -f "${USER_HOME}/miniforge3/etc/profile.d/conda.sh" ]]; then
    source "${USER_HOME}/miniforge3/etc/profile.d/conda.sh"
else
    echo "[ERROR] Conda setup not found at ${USER_HOME}/miniforge3/etc/profile.d/conda.sh"
    exit 1
fi
# Check if slurm partition config exists before sourcing
if [[ -f "${USER_HOME}/slurmd_cmd/.slurm_parition" ]]; then
    source "${USER_HOME}/slurmd_cmd/.slurm_parition"
else
    echo "[ERROR] Slurm partition config not found at ${USER_HOME}/slurmd_cmd/.slurm_parition"
    exit 1
fi

src="${USER_HOME}/PROject/APP-complex/Initial_Input/src"
if [[ -f "${src}/bash_scripts/bash_utils" ]]; then
    source "${src}/bash_scripts/bash_utils"
else
    echo "[ERROR] Bash utils not found at ${src}/bash_scripts/bash_utils"
    exit 1
fi
export OMP_NUM_THREADS=5
export PATH=${byun_HOME}/apps/jq/:${PATH}


echo "[INFO ] Define all seeds to be run"
all_seeds=(
    0 1 10 100 1000 10000 100000 1000000
    2 4 8 16 32 64 128 256 512 1024 2048 4096 
) # [TODO] 이후 gen_input_json.py 코드에 쓰기위해 앞으로 이동
echo "  INFO: Total seeds to run: ${#all_seeds[@]}"
