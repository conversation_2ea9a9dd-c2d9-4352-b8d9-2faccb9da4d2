#!/bin/bash
#SBATCH -J __SYSTEM___AF3W_w-tmpl
#SBATCH -o __SYSTEM___AF3W_w-tmpl.out

##SBATCH -p g4090_short
#SBATCH -p 6000ada_short
#SBATCH --qos=normal
#SBATCH --mem=0
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1


src="/home/<USER>/PROject/APP-complex/Initial_Input/src"
source ${src}/bash_scripts/source.sh

echo "[INFO ] Activate conda environment"
conda activate Draph

# Path definition 
MSA_sub="/home/<USER>/PROject/APP-complex/Initial_Input/AF_Cluster"
proj_p=$(realpath ${PWD})
output_dir=AF3/Full_w_tmpl && mkdir -p ${output_dir}
input_fa=./input.fa
msa_dir=MSA_JKHmmer
template_path="${proj_p}/template/8esv_A_tmpl.cif"  # Set your template path here


echo "[INFO ] Run AlphaFold3 with msa-only (run_inference=false) for generating MSA"
system_name=$(head -n1 ${input_fa} | sed 's/>//g')
check_input_json "${proj_p}" "${input_fa}" "${system_name}" 
if [[ $? -ne 0 ]]; then
    exit 1
fi

echo "[INFO ] Generate json file for whole MSA with custom template structure."
cd ${proj_p}/${msa_dir}
echo ${PWD}
chain_len=$(awk 'NR > 1 {gsub(":", ""); if ($0 ~ /^[A-Z]+$/) {print length($0)}}' ${proj_p}/${input_fa})
resid_ranges=()
start=1
for num in ${chain_len[@]}; do
    end=$(( start + num - 1 ))
    resid_ranges+=("$start,$end")
    echo "  INFO: Added range $start,$end to resid_ranges"
done
echo "  INFO: resid_ranges in given fasta system: ${resid_ranges[@]}"


echo "[INFO ] Preparing MSA files for each chain"
mkdir -p ${proj_p}/${msa_dir}_tmpl # Create temporary directory
cp ${proj_p}/${msa_dir}/*a3m ${proj_p}/${msa_dir}_tmpl

unpaired_cnt=0
for a3m_f in $(find . -maxdepth 1 -name "${system_name}_*.a3m" | sort -V); do
    keyword=$(echo ${a3m_f} | awk -F".a3m" '{print $1}')
    if [[ "$keyword" == *"paired"* ]]; then
        chain_num="${keyword##*${system_name}_}"
        addtive_cmd=""
    else
        chain_num="${keyword##*${system_name}}"
        chain_num="${chain_num//_/}"

        index=${resid_ranges[$unpaired_cnt]}
        IFS=',' read -r start_idx end_idx <<< "$index"
        addtive_cmd="--unpaired --start_idx ${start_idx} --end_idx ${end_idx}"
        let unpaired_cnt=${unpaired_cnt}+1
    fi
    
    echo "  INFO: processing MSA for chain ${chain_num}"
    chain_dir=${proj_p}/${msa_dir}
    mkdir -p ${chain_dir}_tmpl/chain${chain_num}
    
    ## - deprecated -- ##
    # Convert sequences to uppercase
    # awk '{
    #   if ($0 ~ /^>/) {
    #     print $0
    #   } else {
    #     print toupper($0)
    #   }
    # }' ${chain_dir}/${keyword}.a3m > ${chain_dir}_tmpl/chain${chain_num}/${keyword}.a3m
    # ----------------- ##
    cp ${chain_dir}/${keyword}.a3m ${chain_dir}_tmpl/chain${chain_num}/${keyword}.a3m
    echo "  INFO: created ${chain_dir}_tmpl/chain${chain_num}/${keyword}.a3m"
done


echo "[INFO ] Collect existing seeds from the output directory"
AF3W_output_dir="${proj_p}/${output_dir}/msasub_0"
echo "  INFO: checking for existing seeds in ${AF3W_output_dir}"

existing_seeds=()
if [[ -d "${AF3W_output_dir}" ]]; then
    # Find all seed directories and extract seed numbers
    existing_seeds=(
        $(ls "${AF3W_output_dir}" 2>/dev/null |
        grep -E '^seed-[0-9]+' |
        sed -E 's/seed-([0-9]+)_.*/\1/' | sort -u)
    )
    echo "  INFO: found ${#existing_seeds[@]} existing seeds: ${existing_seeds[*]}"
fi

# Extract missing seeds
missing_seeds=()
for s in "${all_seeds[@]}"; do
    # Check if seed exists in the existing_seeds array
    if [[ ! " ${existing_seeds[*]} " =~ " $s " ]]; then
        missing_seeds+=($s)
    fi
done
echo "  INFO: missing ${#missing_seeds[@]} seeds: ${missing_seeds[*]}"

echo "[INFO ] Combine the MSA files with template"
cd ${proj_p}/${msa_dir}_tmpl

printf "  INFO: Generating combined MSA with template\n"
if [[ ${#missing_seeds[@]} -gt 0 ]]; then
    run_python_cmd ${src}/combinate_a3m_json.py \
        "${proj_p}/${msa_dir}_tmpl" \
        "--input_fasta=${input_fa}" \
        "--output_prefix=${system_name}_tmpl" \
        "--msa_file=${system_name}.a3m" \
        "--use_paired" "--use_template" \
        "--custom_template_path" "${template_path}" \
        "--n_clusters=1" \
        "--model_seeds" "${missing_seeds[@]}"
else
    run_python_cmd ${src}/combinate_a3m_json.py \
        "${proj_p}/${msa_dir}_tmpl" \
        "--input_fasta=${input_fa}" \
        "--output_prefix=${system_name}_tmpl" \
        "--msa_file=${system_name}.a3m" \
        "--use_paired" "--use_template" \
        "--custom_template_path" "${template_path}" \
        "--n_clusters=1" 
fi

partition=`slurm_partition | head -n1` 

cat <<EOF > ${proj_p}/slurm_script/AF3W_w-tmpl.sh
#!/bin/bash
#SBATCH -J ${system_name}_AF3W_w-tmpl
#SBATCH -o ${proj_p}/slurm_script/${system_name}_AF3W_w-tmpl.out
#SBATCH -p g3090_short,g4090_short

#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --gres=gpu:1
#SBATCH --qos=normal

source /home/<USER>/miniforge3/etc/profile.d/conda.sh
conda activate /home/<USER>/.conda/envs/alphafold3/
which python

proj_p=${proj_p}
output_dir=${output_dir}

json_f=\$(find \${proj_p}/${msa_dir}_tmpl/${system_name}_tmpl_0 -name '*_tmpl.json' | grep 'tmpl' | grep -v '~')
out_prefix=\${proj_p}/\${output_dir}/
mkdir -p \${out_prefix}

file_count=\$(find \${out_prefix}/ -name model.cif 2>/dev/null | wc -l)
if [[ \${file_count} -ne 100 ]]; then
    echo "  INFO: predicting structure with \${json_f}"
    /appl/anaconda3/envs/AlphaFold3/bin/python -u /appl/git-repo/AlphaFold3/run_alphafold.py \\
        --run_inference=true --run_data_pipeline=false \\
        --json_path \${json_f} --output_dir \${out_prefix}
else
    echo "  INFO: there are already structure prediction results with \${json_f}"
fi
EOF
chmod 755 ${proj_p}/slurm_script/AF3W_w-tmpl.sh

# Check whether the corresponding jobs is running
squeue -u byun -o "%.13i %.12P %.80j %.5u %.8T %.10M" > sqme.log
flag=`cat sqme.log | grep ${system_name}_AF3W_w-tmpl | awk '{print $5}'`
if [[ ${flag} == 'PENDING' ]]; then
    echo "  INFO: PENDING"
elif [[ ${flag} == 'RUNNING' ]]; then
    echo "  INFO: RUNNING"
else
    echo -ne "  INFO: Submit job for ${system_name}: "
    sbatch ${proj_p}/slurm_script/AF3W_w-tmpl.sh
fi
rm -rf sqme.log
echo "  INFO: Job submission completed."
echo "----------------------------"

