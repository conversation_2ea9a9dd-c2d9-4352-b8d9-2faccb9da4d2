MSA Clustering Pipeline - Summary Report
==================================================

Input File: /home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m
Number of Sequences: 500
Average Sequence Length: 91.0
Chain Type: protein

Methods Tested:
  Encoding: MSAtransformer
  Clustering: dbscan, hierarchical

Results Summary:
------------------------------

MSATRANSFORMER_DBSCAN:
  Algorithm: dbscan
  Clusters: 15
  Noise Points: 221 (44.3%)
  Runtime: 41.64 seconds
  Output: kaib_clustering_results/KaiB_101_MSAtransformer_dbscan

MSATRANSFORMER_HIERARCHICAL:
  Algorithm: hierarchical
  Clusters: 8
  Noise Points: 0 (0.0%)
  Runtime: 77.93 seconds
  Output: kaib_clustering_results/KaiB_101_MSAtransformer_hierarchical
