import os, sys
import json
import argparse
from typing import List, <PERSON><PERSON>
from collections import defaultdict

def explore_json(data, indent=0):
    """
    JSON 구조를 탐색하여 시각적으로 출력하는 함수.
    """
    if isinstance(data, dict):
        print('  ' * indent + '{  # Dictionary')
        for key, value in data.items():
            print('  ' * (indent + 1) + f'"{key}": ', end="")
            explore_json(value, indent + 2)
        print('  ' * indent + '}')
    elif isinstance(data, list):
        print('  ' * indent + '[  # List')
        for item in data[:3]:  # 첫 3개만 출력 (너무 긴 경우 생략)
            explore_json(item, indent + 1)
        if len(data) > 3:
            print('  ' * (indent + 1) + '...')  # 생략 표시
        print('  ' * indent + ']')
    else:
        print(f'{repr(data)}  # {type(data).__name__}')


def process_msa_sequence(msa_list: List[str]) -> Tuple[List[str], bool]:
    """
    MSA 시퀀스 리스트를 처리하는 함수
     - 소문자 검사
     - 소문자를 대문자로 변환
    
    Args:
        msa_list: List[str]
        
    Returns:
        Tuple[List[str], bool]: (변환된 MSA 리스트, 소문자가 있었는지 여부)
    """
    has_lowercase = False
    processed_msa = []
    
    for seq in msa_list:        
        if seq.startswith('>'): # header lines
            processed_msa.append(seq)
            continue
        elif (not seq.startswith('>')) and any(c.islower() for c in seq): # lowercase letters in MSA
            has_lowercase = True
            processed_msa.append(seq)
        else: # uppercase letters in MSA
            processed_msa.append(seq)
    
    return "\n".join(processed_msa), has_lowercase


def main():
    parser = argparse.ArgumentParser(description="Parse JSON and process MSA data.")
    parser.add_argument("--msa_dir", type=str, required=True, help="Directory for storing MSA output files.")
    parser.add_argument("--input_json", type=str, required=True, help="Input JSON file path.")
    parser.add_argument("--output_prefix", type=str, required=True, help="Output prefix for generated files.")
    args = parser.parse_args()
    
    print(f"[INFO ] Change to MSA directory: {args.msa_dir}")
    os.chdir(args.msa_dir)
    
    print(f"[INFO ] Read JSON file: {args.input_json}")
    with open(args.input_json, 'r', encoding='utf-8') as file:
        json_data = json.load(file)
    
    print(f"[INFO ] Extract and process paired and unpaired MSA from JSON file")
    paired_msa_d = defaultdict(list)
    unpaired_msa_d = defaultdict(list)
    lowercase_found = defaultdict(list)

    for data in json_data['sequences']:
        id = data['protein'].get('id', None)
        if 'protein' in data:
            # Extract and process pairedMsa
            paired_msa = data['protein'].get('pairedMsa', None).split('\n')       
            if paired_msa is not None:
                processed_msa, has_lowercase = process_msa_sequence(paired_msa)
                if has_lowercase:
                    lowercase_found[id].append('paired')
                    print(f"  INFO: converting lowercase to uppercase in paired MSA for chain {id}")
                paired_msa_d[id].append(processed_msa)
            else: 
                print(f'  INFO: chain {id} has no paired MSA results')

            # Extract and process unpairedMsa
            unpaired_msa = data['protein'].get('unpairedMsa', None).split('\n')
            if unpaired_msa is not None:
                processed_msa, has_lowercase = process_msa_sequence(unpaired_msa)
                if has_lowercase:
                    lowercase_found[id].append('unpaired')
                    print(f"  INFO: converting lowercase to uppercase in unpaired MSA for chain {id}")
                unpaired_msa_d[id].append(processed_msa)
            else: 
                print(f'  INFO: chain {id} has no unpaired MSA results')
        else:
            raise ValueError(f'chain {id} has no protein data') # 이후에 다른 entity 추가 시 수정 필요

    if lowercase_found: # Report the result of lowercase conversion
        print("\n[INFO ] Lowercase letters were found and converted to uppercase in:")
        for chain_id, msa_types in lowercase_found.items():
            print(f"  Chain {chain_id}:")
            for msa_type in msa_types:
                print(f"    - {msa_type} MSA")
        print() # dummy empty line

    # Mapping for ID to output number
    mapping = {chr(65 + i): 101 + i for i in range(26)}  # 65는 'A'의 ASCII 값

    print(f"[INFO ] Write paired MSA files")
    for idx, (id_, msa) in enumerate(paired_msa_d.items()):
        num = mapping[id_]
        output_file = f"{args.output_prefix}_{num}_paired.a3m"
        with open(output_file, 'w', encoding='utf-8') as paired_file:
            paired_file.write(''.join(map(str, msa[0])))  # msa[0]는 리스트의 첫 번째 MSA
    
    print(f"[INFO ] Write unpaired MSA files")
    for idx, (id_, msa) in enumerate(unpaired_msa_d.items()):
        num = mapping[id_]
        output_file = f"{args.output_prefix}_{num}.a3m"
        with open(output_file, 'w', encoding='utf-8') as unpaired_file:
            unpaired_file.write(''.join(map(str, msa[0])))  # msa[0]는 리스트의 첫 번째 MSA

if __name__ == "__main__":
    main()
