#!/usr/bin/env python3
"""
Enhanced MSA Clustering Test Framework

This script provides a comprehensive testing framework for the MSA package,
with YAML-based configuration and support for multiple clustering algorithms.
Built upon the existing MSA module infrastructure.

Usage:
    conda activate Draph
    python msa_clustering_test_simple.py [--config test_config.yaml] [--test-esm] [--test-clustering]
"""

import os
import sys
import logging
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
import argparse

# Add the src directory to Python path for imports
sys.path.insert(0, '/home/<USER>/PROject/APP-complex/Initial_Input/src')

# Import MSA package components
from msa import (
    # Core components
    MSACore, load_msa, save_msa,
    
    # Specific analyzers
    MSAStatistics, MSAMetrics,
    
    # Processing components
    MSAPreprocessor,
    
    # I/O components
    MSAReader, MSAWriter,
    
    # Encoding components
    SequenceEncoder, EncodingMethod,
    
    # Clustering components
    ClusteringManager
)

# Import configuration classes
from msa.processing.preprocessor import PreprocessingConfig
from msa.encoding.sequence_encoder import EncodingConfig


@dataclass
class TestConfig:
    """Configuration class for MSA testing framework."""

    # General settings
    input_file: str
    output_dir: str = "msa_enhanced_test_output"
    max_sequences: int = 3000
    verbose: bool = False

    # Module enable/disable flags
    test_esm: bool = False
    test_clustering: bool = False
    test_visualization: bool = True

    # Preprocessing configuration
    preprocessing: Dict[str, Any] = field(default_factory=lambda: {
        'max_gap_content': 0.4,
        'identity_threshold': 1.0,
        'remove_lowercase': False,
        'deduplicate': True
    })

    # Encoding configuration
    encoding: Dict[str, Any] = field(default_factory=lambda: {
        'methods': ['onehot'],
        'esm_enabled': False,
        'esm_batch_size': 256,
        'esm_max_length': 1000
    })

    # Clustering configuration
    clustering: Dict[str, Any] = field(default_factory=lambda: {
        'algorithms': ['dbscan'],
        'enabled': False,
        'dbscan': {
            'eps': 8.0,
            'min_samples': 5,
            'gap_cutoff': 0.4
        },
        'hierarchical': {
            'n_clusters': None,
            'linkage': 'ward',
            'distance_threshold': None
        },
        'hdbscan': {
            'min_cluster_size': 15,
            'min_samples': None,
            'cluster_selection_method': 'eom'
        }
    })

    # Visualization configuration
    visualization: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'presets': ['quick'],
        'create_clustering_viz': True,
        'create_dimensional_reduction': False
    })

    @classmethod
    def from_yaml(cls, config_path: str) -> 'TestConfig':
        """Load configuration from YAML file."""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)

        return cls(**config_data)

    @classmethod
    def get_default_config(cls, input_file: str) -> 'TestConfig':
        """Get default configuration."""
        return cls(input_file=input_file)

    def save_to_yaml(self, output_path: str) -> None:
        """Save configuration to YAML file."""
        import yaml
        from dataclasses import asdict

        with open(output_path, 'w') as f:
            yaml.dump(asdict(self), f, default_flow_style=False, indent=2)


def setup_logging(verbose: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('msa_clustering_test_simple.log')
        ]
    )


class EnhancedMSATester:
    """
    Enhanced MSA testing framework with YAML configuration support.
    """

    def __init__(self, config: TestConfig):
        """
        Initialize the enhanced MSA tester with configuration.

        Parameters
        ----------
        config : TestConfig
            Configuration object containing all test parameters
        """
        self.config = config
        self.input_file = Path(config.input_file)
        self.output_dir = Path(config.output_dir)

        # Legacy compatibility properties
        self.verbose = config.verbose
        self.test_esm = config.test_esm
        self.test_clustering = config.test_clustering
        self.max_sequences = config.max_sequences
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.msa: Optional[MSACore] = None
        self.preprocessed_msa: Optional[MSACore] = None
        self.analysis_results: Dict[str, Any] = {}
        self.encoding_results: Dict[str, Any] = {}
        self.clustering_results: Dict[str, Any] = {}

        # Setup logging
        setup_logging(self.verbose)
        self.logger = logging.getLogger(self.__class__.__name__)

        self.logger.info(f"Initialized enhanced MSA tester")
        self.logger.info(f"Input file: {self.input_file}")
        self.logger.info(f"Output directory: {self.output_dir}")
        self.logger.info(f"Max sequences for testing: {self.max_sequences}")
        self.logger.info(f"ESM testing: {'enabled' if self.test_esm else 'disabled'}")
        self.logger.info(f"Clustering testing: {'enabled' if self.test_clustering else 'disabled'}")
    
    def step1_load_msa(self) -> None:
        """Step 1: Load MSA from file."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 1: Loading MSA from file")
        self.logger.info("=" * 60)
        
        try:
            # Load MSA using the convenience function (preserve lowercase)
            full_msa = load_msa(str(self.input_file), remove_lowercase=False)
            
            # Limit to max_sequences for faster testing/debugging
            if len(full_msa) > self.max_sequences:
                self.logger.info(f"Limiting MSA from {len(full_msa)} to {self.max_sequences} sequences for testing")
                selected_sequences = full_msa.sequences[:self.max_sequences]
                from msa.core.msa_core import MSACore
                self.msa = MSACore(
                    sequences=selected_sequences,
                    chain_poly_type=full_msa.chain_poly_type
                )
            else:
                self.msa = full_msa
            
            self.logger.info(f"Successfully loaded MSA with {len(self.msa)} sequences")
            self.logger.info(f"Maximum sequence length: {self.msa.max_length}")
            self.logger.info(f"Minimum sequence length: {self.msa.min_length}")
            
            # Log basic statistics
            basic_stats = {
                'n_sequences': len(self.msa),
                'max_length': self.msa.max_length,
                'min_length': self.msa.min_length,
                'query_sequence': str(self.msa.query_sequence.sequence)[:100] + "..." if self.msa.query_sequence else None
            }
            
            self.logger.info(f"Basic MSA statistics: {basic_stats}")
            
        except Exception as e:
            self.logger.error(f"Failed to load MSA: {e}")
            raise
    
    def step2_preprocessing(self) -> MSACore:
        """Step 2: Test MSA preprocessing with configuration-based settings."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 2: Testing MSA Preprocessing (Configuration-Based)")
        self.logger.info("=" * 60)

        if self.msa is None:
            raise ValueError("MSA must be loaded before preprocessing")

        try:
            # Initialize preprocessor with configuration settings
            preprocessing_config = self.config.preprocessing
            config = PreprocessingConfig(
                max_gap_content=preprocessing_config['max_gap_content'],
                identity_threshold=preprocessing_config['identity_threshold'],
                remove_lowercase=preprocessing_config['remove_lowercase'],
                deduplicate=preprocessing_config['deduplicate']
            )
            preprocessor = MSAPreprocessor(config=config)

            # Log configuration settings
            self.logger.info("Preprocessing configuration:")
            self.logger.info(f"  - Gap content threshold: {preprocessing_config['max_gap_content']*100:.0f}%")
            self.logger.info(f"  - Identity threshold: {preprocessing_config['identity_threshold']*100:.0f}%")
            self.logger.info(f"  - Remove lowercase: {preprocessing_config['remove_lowercase']}")
            self.logger.info(f"  - Deduplicate: {preprocessing_config['deduplicate']}")

            # Apply preprocessing with configuration settings
            self.preprocessed_msa = preprocessor.preprocess(self.msa)

            # Log results
            original_count = len(self.msa.sequences)
            processed_count = len(self.preprocessed_msa.sequences)
            removed_count = original_count - processed_count

            self.logger.info(f"Preprocessing Results:")
            self.logger.info(f"  Original sequences: {original_count}")
            self.logger.info(f"  Processed sequences: {processed_count}")
            self.logger.info(f"  Sequences removed: {removed_count} ({removed_count/original_count*100:.1f}%)")

            # Check for lowercase preservation
            lowercase_preserved = any(any(c.islower() for c in seq) for seq in self.preprocessed_msa.sequence_strings)
            self.logger.info(f"  Lowercase characters preserved: {'YES' if lowercase_preserved else 'NO'}")

            return self.preprocessed_msa

        except Exception as e:
            self.logger.error(f"Preprocessing failed: {str(e)}")
            raise
    
    def step3_sequence_encoding(self) -> Dict[str, Any]:
        """Step 3: Test encoding methods based on configuration."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 3: Testing Sequence Encoding (Configuration-Based)")
        self.logger.info("=" * 60)

        if self.preprocessed_msa is None:
            raise ValueError("Preprocessed MSA must be available before encoding")

        encoding_results = {}
        encoding_config = self.config.encoding

        try:
            # Initialize sequence encoder
            encoder = SequenceEncoder()

            # Get sequences for encoding
            sequences = self.preprocessed_msa.sequence_strings
            max_seq_len = self.preprocessed_msa.max_length

            # Log encoding configuration
            self.logger.info("Encoding configuration:")
            self.logger.info(f"  - Methods: {encoding_config['methods']}")
            self.logger.info(f"  - ESM enabled: {encoding_config['esm_enabled']}")

            # Test configured encoding methods
            for method in encoding_config['methods']:
                if method == 'onehot':
                    self.logger.info("Testing One-Hot encoding...")
                    onehot_features = encoder.encode(
                        sequences=sequences,
                        method=EncodingMethod.ONEHOT,
                        max_seq_len=max_seq_len
                    )
                    encoding_results['onehot'] = {
                        'features': onehot_features,
                        'shape': onehot_features.shape,
                        'method': 'onehot'
                    }
                    self.logger.info(f"One-Hot encoding shape: {onehot_features.shape}")

                elif method == 'esm' and encoding_config['esm_enabled']:
                    self.logger.info("Testing ESM embedding...")
                    try:
                        # Use configuration parameters for ESM
                        test_sequences = sequences
                        esm_max_length = encoding_config['esm_max_length']
                        esm_batch_size = encoding_config['esm_batch_size']

                        # Limit sequences if too many
                        if len(sequences) > 2000:
                            self.logger.info(f"Using subset of {min(1000, len(sequences))} sequences for ESM test")
                            test_sequences = sequences[:min(1000, len(sequences))]

                        esm_features = encoder.encode(
                            sequences=test_sequences,
                            method=EncodingMethod.MSA_TRANSFORMER,
                            max_seq_len=min(max_seq_len, esm_max_length),
                            batch_size=esm_batch_size,
                            remove_lowercase=False,
                            remove_special_chars=True
                        )
                        encoding_results['esm'] = {
                            'features': esm_features,
                            'shape': esm_features.shape,
                            'method': 'esm',
                            'n_sequences_tested': len(test_sequences)
                        }
                        self.logger.info(f"ESM embedding shape: {esm_features.shape}")

                    except Exception as e:
                        self.logger.warning(f"ESM embedding test failed: {e}")
                        encoding_results['esm'] = {'error': str(e)}

            self.encoding_results = encoding_results
            return encoding_results

        except Exception as e:
            self.logger.error(f"Failed to encode sequences: {e}")
            raise
    
    def step4_basic_analysis(self) -> None:
        """Step 4: Perform basic statistical analysis."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 4: Basic Statistical Analysis")
        self.logger.info("=" * 60)
        
        try:
            # Basic statistics
            stats_analyzer = MSAStatistics()
            self.analysis_results['statistics'] = stats_analyzer.calculate_comprehensive_stats(self.preprocessed_msa)
            
            # Quality metrics
            metrics_analyzer = MSAMetrics()
            self.analysis_results['metrics'] = metrics_analyzer.calculate_comprehensive_metrics(self.preprocessed_msa)
            
            # Log analysis results
            self.logger.info("Statistical analysis completed:")
            
            # Basic statistics
            if 'statistics' in self.analysis_results:
                stats = self.analysis_results['statistics']
                self.logger.info(f"  Shannon diversity: {stats.shannon_diversity:.3f}")
                self.logger.info(f"  Simpson diversity: {stats.simpson_diversity:.3f}")
                self.logger.info(f"  Effective sequences: {stats.effective_sequences:.1f}")
                self.logger.info(f"  Mean gap content: {stats.mean_gap_content:.1%}")
            
            # Quality metrics
            if 'metrics' in self.analysis_results:
                metrics = self.analysis_results['metrics']
                self.logger.info(f"  Overall quality score: {metrics.overall_quality_score:.3f}")
                self.logger.info(f"  Mean coverage: {metrics.mean_coverage:.1%}")
                self.logger.info(f"  Mean conservation: {metrics.mean_conservation:.3f}")
                
        except Exception as e:
            self.logger.error(f"Failed to perform statistical analysis: {e}")
            raise
    
    def step5_clustering_analysis(self) -> Dict[str, Any]:
        """Step 5: Test clustering with configuration-based multi-algorithm support."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 5: Testing MSA Clustering (Multi-Algorithm Support)")
        self.logger.info("=" * 60)

        clustering_config = self.config.clustering
        if not self.test_clustering or not clustering_config['enabled']:
            self.logger.info("Clustering test skipped (enable in configuration or use --test-clustering)")
            return {}

        if self.preprocessed_msa is None:
            raise ValueError("Preprocessed MSA must be available before clustering")

        clustering_results = {}

        try:
            # Initialize clustering manager
            manager = ClusteringManager(self.preprocessed_msa)

            # Log clustering configuration
            self.logger.info("Clustering configuration:")
            self.logger.info(f"  - Algorithms: {clustering_config['algorithms']}")
            self.logger.info(f"  - Gap cutoff: {clustering_config['dbscan']['gap_cutoff']}")

            # Test each configured algorithm
            for algorithm in clustering_config['algorithms']:
                self.logger.info(f"\nTesting {algorithm.upper()} clustering...")

                try:
                    if algorithm == 'dbscan':
                        params = clustering_config['dbscan']
                        result = manager.cluster(
                            algorithm='dbscan',
                            gap_cutoff=params['gap_cutoff'],
                            encoding_method='onehot',
                            eps=params['eps'],
                            min_samples=params['min_samples']
                        )

                    elif algorithm == 'hierarchical':
                        params = clustering_config['hierarchical']
                        result = manager.cluster(
                            algorithm='hierarchical',
                            gap_cutoff=clustering_config['dbscan']['gap_cutoff'],  # Use same gap cutoff
                            encoding_method='onehot',
                            n_clusters=params['n_clusters'],
                            linkage=params['linkage'],
                            distance_threshold=params['distance_threshold']
                        )

                    elif algorithm == 'hdbscan':
                        params = clustering_config['hdbscan']
                        result = manager.cluster(
                            algorithm='hdbscan',
                            gap_cutoff=clustering_config['dbscan']['gap_cutoff'],  # Use same gap cutoff
                            encoding_method='onehot',
                            min_cluster_size=params['min_cluster_size'],
                            min_samples=params['min_samples'],
                            cluster_selection_method=params['cluster_selection_method']
                        )

                    else:
                        self.logger.warning(f"Unknown algorithm: {algorithm}")
                        continue

                    # Store results
                    clustering_results[algorithm] = result

                    # Log clustering results
                    n_clusters = len(set(result.cluster_labels)) - (1 if -1 in result.cluster_labels else 0)
                    n_noise = list(result.cluster_labels).count(-1)
                    self.logger.info(f"✅ {algorithm.upper()} clustering completed:")
                    self.logger.info(f"  - Clusters found: {n_clusters}")
                    self.logger.info(f"  - Noise points: {n_noise}")

                    # Log silhouette score if available in metadata
                    if 'silhouette_score' in result.metadata:
                        self.logger.info(f"  - Silhouette score: {result.metadata['silhouette_score']:.3f}")
                    else:
                        self.logger.info(f"  - Silhouette score: Not available")

                except Exception as e:
                    self.logger.error(f"❌ {algorithm.upper()} clustering failed: {e}")
                    clustering_results[algorithm] = {'error': str(e)}

            self.clustering_results = clustering_results
            return clustering_results

        except Exception as e:
            self.logger.error(f"Clustering analysis failed: {str(e)}")
            self.logger.error(f"Error type: {type(e).__name__}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            clustering_results['error'] = str(e)
            return clustering_results
            
        except Exception as e:
            self.logger.error(f"Clustering analysis failed: {str(e)}")
            self.logger.error(f"Error type: {type(e).__name__}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            clustering_results['error'] = str(e)
        
        return clustering_results
    
    def step6_visualization_testing(self) -> None:
        """Step 6: Test enhanced visualization functionality."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 6: Testing Enhanced Visualization")
        self.logger.info("=" * 60)
        
        try:
            # Import new visualization system
            from msa.visualization import VisualizationManager, visualize_msa_basic, visualize_msa_comprehensive
            from msa.visualization import PresetConfigs
            
            # Create visualization output directory
            viz_output_dir = self.output_dir / "visualizations"
            viz_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Test 1: Basic visualization with VisualizationManager
            self.logger.info("Testing VisualizationManager...")
            viz_manager = VisualizationManager()
            viz_manager.visualize_basic_msa(self.preprocessed_msa, str(viz_output_dir), "basic_")
            self.logger.info("✅ Basic MSA visualizations created")
            
            # Test 2: Statistical analysis
            self.logger.info("Testing statistical visualizations...")
            viz_manager.visualize_msa_statistics(self.preprocessed_msa, str(viz_output_dir), "stats_")
            self.logger.info("✅ Statistical visualizations created")
            
            # Test 3: Convenience function for basic visualization
            self.logger.info("Testing convenience functions...")
            visualize_msa_basic(self.preprocessed_msa, str(viz_output_dir), "convenience_basic_")
            self.logger.info("✅ Convenience function basic visualization created")
            
            # Test 4: Different configuration presets
            self.logger.info("Testing different configuration presets...")
            for preset_name in ['publication', 'presentation', 'web', 'quick']:
                try:
                    preset_config = getattr(PresetConfigs, preset_name)()
                    preset_manager = VisualizationManager(preset_config)
                    preset_output = viz_output_dir / preset_name
                    preset_output.mkdir(exist_ok=True)
                    preset_manager.visualize_basic_msa(self.preprocessed_msa, str(preset_output), f"{preset_name}_")
                    self.logger.info(f"✅ {preset_name} preset visualization created")
                except Exception as e:
                    self.logger.warning(f"⚠️ {preset_name} preset failed: {e}")
            
            # Test 5: Clustering visualization (if clustering was performed)
            if self.test_clustering and hasattr(self, 'clustering_results') and self.clustering_results:
                self.logger.info("Testing clustering visualizations...")
                try:
                    from msa.visualization import ClusteringVisualizer, visualize_msa_clustering
                    
                    # Get the first clustering result for testing
                    first_result = next(iter(self.clustering_results.values()))
                    if 'error' not in first_result:  # Only if clustering was successful
                        clustering_viz = ClusteringVisualizer()
                        
                        # Create clustering visualizations
                        clustering_viz.visualize_comprehensive_clustering(
                            self.preprocessed_msa, first_result, str(viz_output_dir), "clustering_"
                        )
                        self.logger.info("✅ Clustering visualizations created")
                        
                        # Test convenience function
                        visualize_msa_clustering(self.preprocessed_msa, first_result, str(viz_output_dir), "convenience_clustering_")
                        self.logger.info("✅ Clustering convenience function completed")
                        
                        # Test comprehensive analysis
                        visualize_msa_comprehensive(self.preprocessed_msa, str(viz_output_dir), first_result, "comprehensive_")
                        self.logger.info("✅ Comprehensive analysis visualization created")
                    else:
                        self.logger.warning("⚠️ Skipping clustering visualization (clustering failed)")
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ Clustering visualization failed: {e}")
            
            self.logger.info(f"✅ Visualization tests completed! Check output in: {viz_output_dir}")
            
        except Exception as e:
            self.logger.error(f"❌ Visualization test failed: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def step7_save_results(self) -> None:
        """Step 7: Save results."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 7: Saving Results")
        self.logger.info("=" * 60)
        
        try:
            # Save preprocessed MSA
            preprocessed_file = self.output_dir / "preprocessed_msa.fasta"
            save_msa(self.preprocessed_msa, str(preprocessed_file))
            self.logger.info(f"Preprocessed MSA saved to: {preprocessed_file}")
            
            # Generate comprehensive report
            report_file = self.output_dir / "enhanced_analysis_report.txt"
            self._generate_enhanced_report(report_file)
            
            self.logger.info(f"Enhanced report saved to: {report_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            raise
    
    def _generate_enhanced_report(self, report_file: Path) -> None:
        """Generate an enhanced analysis report."""
        with open(report_file, 'w') as f:
            f.write("Enhanced MSA Analysis Report\n")
            f.write("=" * 35 + "\n\n")
            
            f.write(f"Input File: {self.input_file}\n")
            f.write(f"Test Configuration:\n")
            f.write(f"  ESM Testing: {'enabled' if self.test_esm else 'disabled'}\n")
            f.write(f"  Clustering Testing: {'enabled' if self.test_clustering else 'disabled'}\n\n")
            
            # Basic MSA information
            f.write("MSA Information:\n")
            f.write(f"  Original sequences: {len(self.msa)}\n")
            f.write(f"  Preprocessed sequences: {len(self.preprocessed_msa)}\n")
            f.write(f"  Maximum length: {self.preprocessed_msa.max_length}\n")
            f.write(f"  Minimum length: {self.preprocessed_msa.min_length}\n\n")
            
            # Statistical analysis results
            if self.analysis_results:
                f.write("Statistical Analysis:\n")
                
                if 'statistics' in self.analysis_results:
                    stats = self.analysis_results['statistics']
                    f.write(f"  Shannon diversity: {stats.shannon_diversity:.3f}\n")
                    f.write(f"  Simpson diversity: {stats.simpson_diversity:.3f}\n")
                    f.write(f"  Effective sequences: {stats.effective_sequences:.1f}\n")
                
                if 'metrics' in self.analysis_results:
                    metrics = self.analysis_results['metrics']
                    f.write(f"  Overall quality score: {metrics.overall_quality_score:.3f}\n")
                    f.write(f"  Mean coverage: {metrics.mean_coverage:.1%}\n")
                
                f.write("\n")
            
            # Encoding results
            if self.encoding_results:
                f.write("Encoding Results:\n")
                for method, result in self.encoding_results.items():
                    if 'error' in result:
                        f.write(f"  {method.upper()}: Failed - {result['error']}\n")
                    else:
                        f.write(f"  {method.upper()}: Success - Shape {result['shape']}\n")
                        if 'n_sequences_tested' in result:
                            f.write(f"    Sequences tested: {result['n_sequences_tested']}\n")
                f.write("\n")
            
            # Clustering results
            if self.clustering_results:
                f.write("Clustering Results:\n")
                for algorithm, result in self.clustering_results.items():
                    if 'error' in result:
                        f.write(f"  {algorithm.upper()}: Failed - {result['error']}\n")
                    else:
                        f.write(f"  {algorithm.upper()}: Success\n")
                        f.write(f"    Clusters: {result.get('n_clusters', 'N/A')}\n")
                        f.write(f"    Noise points: {result.get('n_noise', 'N/A')}\n")
                        if 'n_sequences_tested' in result:
                            f.write(f"    Sequences tested: {result['n_sequences_tested']}\n")
                f.write("\n")
            
            f.write("Enhanced analysis completed successfully!\n")
    
    def run_enhanced_workflow(self) -> None:
        """Run the enhanced MSA analysis workflow."""
        self.logger.info("Starting enhanced MSA analysis workflow...")
        
        try:
            # Execute enhanced steps
            self.step1_load_msa()
            self.step2_preprocessing()
            self.step3_sequence_encoding()
            self.step4_basic_analysis()
            self.step5_clustering_analysis()
            self.step6_visualization_testing()  # New visualization testing step
            self.step7_save_results()
            
            self.logger.info("=" * 60)
            self.logger.info("ENHANCED WORKFLOW COMPLETED SUCCESSFULLY!")
            self.logger.info("=" * 60)
            self.logger.info(f"All results saved to: {self.output_dir}")
            
            # Print summary
            self._print_summary()
            
        except Exception as e:
            self.logger.error(f"Enhanced workflow failed: {e}")
            raise
    
    def _print_summary(self) -> None:
        """Print a summary of test results."""
        print("\n" + "=" * 50)
        print("ENHANCED TEST SUMMARY")
        print("=" * 50)
        
        print(f"✓ MSA loaded: {len(self.msa)} sequences")
        print(f"✓ Preprocessed: {len(self.preprocessed_msa)} sequences")
        
        # Encoding summary
        if self.encoding_results:
            print(f"✓ One-Hot encoding: {self.encoding_results.get('onehot', {}).get('shape', 'Failed')}")
            if 'esm' in self.encoding_results:
                if 'error' in self.encoding_results['esm']:
                    print(f"✗ ESM embedding: Failed")
                else:
                    print(f"✓ ESM embedding: {self.encoding_results['esm'].get('shape', 'Success')}")
        
        # Clustering summary
        if self.clustering_results:
            for algorithm, result in self.clustering_results.items():
                if 'error' in result:
                    print(f"✗ {algorithm.upper()} clustering: Failed")
                else:
                    print(f"✓ {algorithm.upper()} clustering: {result.get('n_clusters', 0)} clusters")
        
        print("=" * 50)


def main():
    """Main function to run the enhanced MSA test with YAML configuration."""
    parser = argparse.ArgumentParser(
        description="Enhanced MSA analysis test framework with YAML configuration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Run with default configuration
    python msa_clustering_test_simple.py

    # Run with custom configuration file
    python msa_clustering_test_simple.py --config my_test_config.yaml

    # Override specific settings
    python msa_clustering_test_simple.py --test-esm --test-clustering --verbose
        """
    )

    parser.add_argument(
        "--config",
        type=str,
        default="msa_test_config.yaml",
        help="Path to YAML configuration file (default: msa_test_config.yaml)"
    )

    parser.add_argument(
        "--input",
        type=str,
        help="Input MSA file path (overrides config)"
    )

    parser.add_argument(
        "--output",
        type=str,
        help="Output directory (overrides config)"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging (overrides config)"
    )

    parser.add_argument(
        "--test-esm",
        action="store_true",
        help="Enable ESM embedding tests (overrides config)"
    )

    parser.add_argument(
        "--test-clustering",
        action="store_true",
        help="Enable clustering tests (overrides config)"
    )

    parser.add_argument(
        "--max-sequences",
        type=int,
        help="Maximum number of sequences to use for testing (overrides config)"
    )

    args = parser.parse_args()

    try:
        # Load configuration from YAML file
        if Path(args.config).exists():
            config = TestConfig.from_yaml(args.config)
            print(f"Loaded configuration from: {args.config}")
        else:
            print(f"Configuration file {args.config} not found. Using default configuration.")
            # Use default input file if no config file exists
            default_input = args.input or "/home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m"
            config = TestConfig.get_default_config(default_input)

        # Override configuration with command line arguments
        if args.input:
            config.input_file = args.input
        if args.output:
            config.output_dir = args.output
        if args.verbose:
            config.verbose = True
        if args.test_esm:
            config.test_esm = True
            config.encoding['esm_enabled'] = True
        if args.test_clustering:
            config.test_clustering = True
            config.clustering['enabled'] = True
        if args.max_sequences:
            config.max_sequences = args.max_sequences

        # Check if input file exists
        if not Path(config.input_file).exists():
            print(f"Error: Input file does not exist: {config.input_file}")
            sys.exit(1)

        # Create and run tester
        tester = EnhancedMSATester(config)

        # Run standard enhanced workflow
        tester.run_enhanced_workflow()
        print(f"\nEnhanced test completed successfully! Results saved to: {config.output_dir}")

    except Exception as e:
        print(f"\nTest failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 