#!/usr/bin/env python3
"""
Simplified MSA Clustering Test Script

This script tests the basic functionality of the refactored MSA package,
focusing on components that are working correctly. Now includes improved
ESM embedding and clustering tests.

Usage:
    conda activate Draph
    python msa_clustering_test_simple.py [--test-esm] [--test-clustering]
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, List
import argparse

# Add the src directory to Python path for imports
sys.path.insert(0, '/home/<USER>/PROject/APP-complex/Initial_Input/src')

# Import MSA package components
from msa import (
    # Core components
    MSACore, load_msa, save_msa,
    
    # Specific analyzers
    MSAStatistics, MSAMetrics,
    
    # Processing components
    MSAPreprocessor,
    
    # I/O components
    MSAReader, MSAWriter,
    
    # Encoding components
    SequenceEncoder, EncodingMethod,
    
    # Clustering components
    ClusteringManager
)

# Import configuration classes
from msa.processing.preprocessor import PreprocessingConfig
from msa.encoding.sequence_encoder import EncodingConfig


def setup_logging(verbose: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('msa_clustering_test_simple.log')
        ]
    )


class EnhancedMSATester:
    """
    Enhanced MSA testing class with ESM embedding and clustering tests.
    """
    
    def __init__(self, 
                 input_file: str,
                 output_dir: str = "msa_enhanced_test_output",
                 verbose: bool = False,
                 test_esm: bool = False,
                 test_clustering: bool = False,
                 max_sequences: int = 3000):
        """
        Initialize the enhanced MSA tester.
        
        Parameters
        ----------
        input_file : str
            Path to input MSA file (e.g., KaiB_101.a3m)
        output_dir : str
            Output directory for results
        verbose : bool
            Enable verbose logging
        test_esm : bool
            Enable ESM embedding tests
        test_clustering : bool
            Enable clustering tests
        max_sequences : int
            Maximum number of sequences to use for testing (default: 3000)
        """
        self.input_file = Path(input_file)
        self.output_dir = Path(output_dir)
        self.verbose = verbose
        self.test_esm = test_esm
        self.test_clustering = test_clustering
        self.max_sequences = max_sequences
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.msa: MSACore = None
        self.preprocessed_msa: MSACore = None
        self.analysis_results: Dict[str, Any] = {}
        self.encoding_results: Dict[str, Any] = {}
        self.clustering_results: Dict[str, Any] = {}
        
        # Setup logging
        setup_logging(verbose)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.logger.info(f"Initialized enhanced MSA tester")
        self.logger.info(f"Input file: {self.input_file}")
        self.logger.info(f"Output directory: {self.output_dir}")
        self.logger.info(f"Max sequences for testing: {self.max_sequences}")
        self.logger.info(f"ESM testing: {'enabled' if test_esm else 'disabled'}")
        self.logger.info(f"Clustering testing: {'enabled' if test_clustering else 'disabled'}")
    
    def step1_load_msa(self) -> None:
        """Step 1: Load MSA from file."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 1: Loading MSA from file")
        self.logger.info("=" * 60)
        
        try:
            # Load MSA using the convenience function (preserve lowercase)
            full_msa = load_msa(str(self.input_file), remove_lowercase=False)
            
            # Limit to max_sequences for faster testing/debugging
            if len(full_msa) > self.max_sequences:
                self.logger.info(f"Limiting MSA from {len(full_msa)} to {self.max_sequences} sequences for testing")
                selected_sequences = full_msa.sequences[:self.max_sequences]
                from msa.core.msa_core import MSACore
                self.msa = MSACore(
                    sequences=selected_sequences,
                    chain_poly_type=full_msa.chain_poly_type
                )
            else:
                self.msa = full_msa
            
            self.logger.info(f"Successfully loaded MSA with {len(self.msa)} sequences")
            self.logger.info(f"Maximum sequence length: {self.msa.max_length}")
            self.logger.info(f"Minimum sequence length: {self.msa.min_length}")
            
            # Log basic statistics
            basic_stats = {
                'n_sequences': len(self.msa),
                'max_length': self.msa.max_length,
                'min_length': self.msa.min_length,
                'query_sequence': str(self.msa.query_sequence.sequence)[:100] + "..." if self.msa.query_sequence else None
            }
            
            self.logger.info(f"Basic MSA statistics: {basic_stats}")
            
        except Exception as e:
            self.logger.error(f"Failed to load MSA: {e}")
            raise
    
    def step2_preprocessing(self) -> MSACore:
        """Step 2: Test MSA preprocessing with updated settings."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 2: Testing MSA Preprocessing (Updated Settings)")
        self.logger.info("=" * 60)
        
        try:
            # Initialize preprocessor with updated requirements
            config = PreprocessingConfig(
                max_gap_content=0.4,        # 40% gap threshold
                identity_threshold=1.0,     # 100% identity for deduplication
                remove_lowercase=False,     # Preserve lowercase
                deduplicate=True
                # Length filtering permanently disabled - parameters removed
            )
            preprocessor = MSAPreprocessor(config=config)
            
            # Log original settings for comparison
            self.logger.info("Updated preprocessing settings:")
            self.logger.info("  - Gap content threshold: 40% (was 80%)")
            self.logger.info("  - Identity threshold: 100% (was 95%)")
            self.logger.info("  - Length filtering: DISABLED (as required)")
            self.logger.info("  - Lowercase preservation: ENABLED (for clustering workflow)")
            
            # Apply preprocessing with new settings
            self.preprocessed_msa = preprocessor.preprocess(self.msa)
            
            # Log results
            original_count = len(self.msa.sequences)
            processed_count = len(self.preprocessed_msa.sequences)
            removed_count = original_count - processed_count
            
            self.logger.info(f"Preprocessing Results:")
            self.logger.info(f"  Original sequences: {original_count}")
            self.logger.info(f"  Processed sequences: {processed_count}")
            self.logger.info(f"  Sequences removed: {removed_count} ({removed_count/original_count*100:.1f}%)")
            
            # Check for lowercase preservation
            lowercase_preserved = any(any(c.islower() for c in seq) for seq in self.preprocessed_msa.sequence_strings)
            self.logger.info(f"  Lowercase characters preserved: {'YES' if lowercase_preserved else 'NO'}")
            
            return self.preprocessed_msa
            
        except Exception as e:
            self.logger.error(f"Preprocessing failed: {str(e)}")
            raise
    
    def step3_sequence_encoding(self) -> Dict[str, Any]:
        """Step 3: Test encoding methods including improved ESM if requested."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 3: Testing Sequence Encoding")
        self.logger.info("=" * 60)
        
        encoding_results = {}
        
        try:
            # Initialize sequence encoder
            encoder = SequenceEncoder()
            
            # Get sequences for encoding
            sequences = self.preprocessed_msa.sequence_strings
            max_seq_len = self.preprocessed_msa.max_length
            
            # Test One-Hot encoding (always test this)
            self.logger.info("Testing One-Hot encoding...")
            onehot_features = encoder.encode(
                sequences=sequences,
                method=EncodingMethod.ONEHOT,
                max_seq_len=max_seq_len
            )
            encoding_results['onehot'] = {
                'features': onehot_features,
                'shape': onehot_features.shape,
                'method': 'onehot'
            }
            self.logger.info(f"One-Hot encoding shape: {onehot_features.shape}")
            
            # Test ESM embedding if requested
            if self.test_esm:
                self.logger.info("Testing improved ESM embedding...")
                try:
                    # Use a smaller subset for ESM testing if MSA is very large
                    test_sequences = sequences
                    if len(sequences) > 2000:
                        self.logger.info(f"Using subset of {min(1000, len(sequences))} sequences for ESM test")
                        test_sequences = sequences[:min(1000, len(sequences))]
                    
                    esm_features = encoder.encode(
                        sequences=test_sequences,
                        method=EncodingMethod.MSA_TRANSFORMER,
                        max_seq_len=min(max_seq_len, 1000),  # Limit sequence length
                        batch_size=256,  # Conservative batch size
                        remove_lowercase=False,
                        remove_special_chars=True
                    )
                    encoding_results['esm'] = {
                        'features': esm_features,
                        'shape': esm_features.shape,
                        'method': 'esm',
                        'n_sequences_tested': len(test_sequences)
                    }
                    self.logger.info(f"ESM embedding shape: {esm_features.shape}")
                    
                except Exception as e:
                    self.logger.warning(f"ESM embedding test failed: {e}")
                    encoding_results['esm'] = {'error': str(e)}
            
            self.encoding_results = encoding_results
            return encoding_results
            
        except Exception as e:
            self.logger.error(f"Failed to encode sequences: {e}")
            raise
    
    def step4_basic_analysis(self) -> None:
        """Step 4: Perform basic statistical analysis."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 4: Basic Statistical Analysis")
        self.logger.info("=" * 60)
        
        try:
            # Basic statistics
            stats_analyzer = MSAStatistics()
            self.analysis_results['statistics'] = stats_analyzer.calculate_comprehensive_stats(self.preprocessed_msa)
            
            # Quality metrics
            metrics_analyzer = MSAMetrics()
            self.analysis_results['metrics'] = metrics_analyzer.calculate_comprehensive_metrics(self.preprocessed_msa)
            
            # Log analysis results
            self.logger.info("Statistical analysis completed:")
            
            # Basic statistics
            if 'statistics' in self.analysis_results:
                stats = self.analysis_results['statistics']
                self.logger.info(f"  Shannon diversity: {stats.shannon_diversity:.3f}")
                self.logger.info(f"  Simpson diversity: {stats.simpson_diversity:.3f}")
                self.logger.info(f"  Effective sequences: {stats.effective_sequences:.1f}")
                self.logger.info(f"  Mean gap content: {stats.mean_gap_content:.1%}")
            
            # Quality metrics
            if 'metrics' in self.analysis_results:
                metrics = self.analysis_results['metrics']
                self.logger.info(f"  Overall quality score: {metrics.overall_quality_score:.3f}")
                self.logger.info(f"  Mean coverage: {metrics.mean_coverage:.1%}")
                self.logger.info(f"  Mean conservation: {metrics.mean_conservation:.3f}")
                
        except Exception as e:
            self.logger.error(f"Failed to perform statistical analysis: {e}")
            raise
    
    def step5_clustering_analysis(self) -> Dict[str, Any]:
        """Step 5: Test clustering with updated logic (if requested)."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 5: Testing MSA Clustering (Updated Logic)")
        self.logger.info("=" * 60)
        
        if not self.test_clustering:
            self.logger.info("Clustering test skipped (use --test-clustering to enable)")
            return {}
        
        clustering_results = {}
        
        try:
            # Initialize clustering manager
            manager = ClusteringManager(self.preprocessed_msa)
            
            # Test DBSCAN clustering with updated parameters
            self.logger.info("Testing DBSCAN clustering with updated logic...")
            self.logger.info("Key changes:")
            self.logger.info("  - Gap content filtering: 40% threshold")
            self.logger.info("  - Onehot encoding: Remove lowercase only during encoding")
            self.logger.info("  - Result extraction: Use original sequences with lowercase")
            
            # Perform clustering
            result = manager.cluster(
                algorithm='dbscan',
                gap_cutoff=0.4,          # Updated to 40%
                encoding_method='onehot', # Test onehot with lowercase handling
                eps=8.0,                 # DBSCAN parameters
                min_samples=5
            )
            
            # Log clustering results
            self.logger.info(f"Clustering Results:")
            self.logger.info(f"  Number of clusters: {result.n_clusters}")
            self.logger.info(f"  Number of noise points: {result.n_noise}")
            self.logger.info(f"  Total sequences processed: {len(result.cluster_labels)}")
            
            # Check that results contain original sequences (with lowercase)
            if result.sequences:
                has_lowercase = any(any(c.islower() for c in seq) for seq in result.sequences[:10])
                self.logger.info(f"  Original sequences preserved in results: {'YES' if has_lowercase else 'NO'}")
            
            # Analyze cluster composition
            if result.n_clusters > 0:
                self.logger.info("Cluster composition:")
                for cluster_id in range(result.n_clusters):
                    cluster_seqs = result.get_cluster_sequences(cluster_id)
                    if cluster_seqs:
                        self.logger.info(f"  Cluster {cluster_id}: {len(cluster_seqs)} sequences")
                        
                        # Check ALL sequences in cluster for lowercase preservation
                        lowercase_count = sum(1 for seq in cluster_seqs if any(c.islower() for c in seq))
                        has_lower = lowercase_count > 0
                        self.logger.info(f"    Lowercase preserved: {'YES' if has_lower else 'NO'} ({lowercase_count}/{len(cluster_seqs)} sequences)")
            
            clustering_results['dbscan'] = {
                'n_clusters': result.n_clusters,
                'n_noise': result.n_noise,
                'cluster_labels': result.cluster_labels,
                'sequences_preserved': len(result.sequences),
                'metadata': result.metadata
            }
            
            self.logger.info("Clustering analysis completed successfully")
            
        except Exception as e:
            self.logger.error(f"Clustering analysis failed: {str(e)}")
            self.logger.error(f"Error type: {type(e).__name__}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            clustering_results['error'] = str(e)
        
        return clustering_results
    
    def step6_visualization_testing(self) -> None:
        """Step 6: Test enhanced visualization functionality."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 6: Testing Enhanced Visualization")
        self.logger.info("=" * 60)
        
        try:
            # Import new visualization system
            from msa.visualization import VisualizationManager, visualize_msa_basic, visualize_msa_comprehensive
            from msa.visualization import PresetConfigs
            
            # Create visualization output directory
            viz_output_dir = self.output_dir / "visualizations"
            viz_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Test 1: Basic visualization with VisualizationManager
            self.logger.info("Testing VisualizationManager...")
            viz_manager = VisualizationManager()
            viz_manager.visualize_basic_msa(self.preprocessed_msa, str(viz_output_dir), "basic_")
            self.logger.info("✅ Basic MSA visualizations created")
            
            # Test 2: Statistical analysis
            self.logger.info("Testing statistical visualizations...")
            viz_manager.visualize_msa_statistics(self.preprocessed_msa, str(viz_output_dir), "stats_")
            self.logger.info("✅ Statistical visualizations created")
            
            # Test 3: Convenience function for basic visualization
            self.logger.info("Testing convenience functions...")
            visualize_msa_basic(self.preprocessed_msa, str(viz_output_dir), "convenience_basic_")
            self.logger.info("✅ Convenience function basic visualization created")
            
            # Test 4: Different configuration presets
            self.logger.info("Testing different configuration presets...")
            for preset_name in ['publication', 'presentation', 'web', 'quick']:
                try:
                    preset_config = getattr(PresetConfigs, preset_name)()
                    preset_manager = VisualizationManager(preset_config)
                    preset_output = viz_output_dir / preset_name
                    preset_output.mkdir(exist_ok=True)
                    preset_manager.visualize_basic_msa(self.preprocessed_msa, str(preset_output), f"{preset_name}_")
                    self.logger.info(f"✅ {preset_name} preset visualization created")
                except Exception as e:
                    self.logger.warning(f"⚠️ {preset_name} preset failed: {e}")
            
            # Test 5: Clustering visualization (if clustering was performed)
            if self.test_clustering and hasattr(self, 'clustering_results') and self.clustering_results:
                self.logger.info("Testing clustering visualizations...")
                try:
                    from msa.visualization import ClusteringVisualizer, visualize_msa_clustering
                    
                    # Get the first clustering result for testing
                    first_result = next(iter(self.clustering_results.values()))
                    if 'error' not in first_result:  # Only if clustering was successful
                        clustering_viz = ClusteringVisualizer()
                        
                        # Create clustering visualizations
                        clustering_viz.visualize_comprehensive_clustering(
                            self.preprocessed_msa, first_result, str(viz_output_dir), "clustering_"
                        )
                        self.logger.info("✅ Clustering visualizations created")
                        
                        # Test convenience function
                        visualize_msa_clustering(self.preprocessed_msa, first_result, str(viz_output_dir), "convenience_clustering_")
                        self.logger.info("✅ Clustering convenience function completed")
                        
                        # Test comprehensive analysis
                        visualize_msa_comprehensive(self.preprocessed_msa, str(viz_output_dir), first_result, "comprehensive_")
                        self.logger.info("✅ Comprehensive analysis visualization created")
                    else:
                        self.logger.warning("⚠️ Skipping clustering visualization (clustering failed)")
                    
                except Exception as e:
                    self.logger.warning(f"⚠️ Clustering visualization failed: {e}")
            
            self.logger.info(f"✅ Visualization tests completed! Check output in: {viz_output_dir}")
            
        except Exception as e:
            self.logger.error(f"❌ Visualization test failed: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def step7_save_results(self) -> None:
        """Step 7: Save results."""
        self.logger.info("=" * 60)
        self.logger.info("STEP 7: Saving Results")
        self.logger.info("=" * 60)
        
        try:
            # Save preprocessed MSA
            preprocessed_file = self.output_dir / "preprocessed_msa.fasta"
            save_msa(self.preprocessed_msa, str(preprocessed_file))
            self.logger.info(f"Preprocessed MSA saved to: {preprocessed_file}")
            
            # Generate comprehensive report
            report_file = self.output_dir / "enhanced_analysis_report.txt"
            self._generate_enhanced_report(report_file)
            
            self.logger.info(f"Enhanced report saved to: {report_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            raise
    
    def _generate_enhanced_report(self, report_file: Path) -> None:
        """Generate an enhanced analysis report."""
        with open(report_file, 'w') as f:
            f.write("Enhanced MSA Analysis Report\n")
            f.write("=" * 35 + "\n\n")
            
            f.write(f"Input File: {self.input_file}\n")
            f.write(f"Test Configuration:\n")
            f.write(f"  ESM Testing: {'enabled' if self.test_esm else 'disabled'}\n")
            f.write(f"  Clustering Testing: {'enabled' if self.test_clustering else 'disabled'}\n\n")
            
            # Basic MSA information
            f.write("MSA Information:\n")
            f.write(f"  Original sequences: {len(self.msa)}\n")
            f.write(f"  Preprocessed sequences: {len(self.preprocessed_msa)}\n")
            f.write(f"  Maximum length: {self.preprocessed_msa.max_length}\n")
            f.write(f"  Minimum length: {self.preprocessed_msa.min_length}\n\n")
            
            # Statistical analysis results
            if self.analysis_results:
                f.write("Statistical Analysis:\n")
                
                if 'statistics' in self.analysis_results:
                    stats = self.analysis_results['statistics']
                    f.write(f"  Shannon diversity: {stats.shannon_diversity:.3f}\n")
                    f.write(f"  Simpson diversity: {stats.simpson_diversity:.3f}\n")
                    f.write(f"  Effective sequences: {stats.effective_sequences:.1f}\n")
                
                if 'metrics' in self.analysis_results:
                    metrics = self.analysis_results['metrics']
                    f.write(f"  Overall quality score: {metrics.overall_quality_score:.3f}\n")
                    f.write(f"  Mean coverage: {metrics.mean_coverage:.1%}\n")
                
                f.write("\n")
            
            # Encoding results
            if self.encoding_results:
                f.write("Encoding Results:\n")
                for method, result in self.encoding_results.items():
                    if 'error' in result:
                        f.write(f"  {method.upper()}: Failed - {result['error']}\n")
                    else:
                        f.write(f"  {method.upper()}: Success - Shape {result['shape']}\n")
                        if 'n_sequences_tested' in result:
                            f.write(f"    Sequences tested: {result['n_sequences_tested']}\n")
                f.write("\n")
            
            # Clustering results
            if self.clustering_results:
                f.write("Clustering Results:\n")
                for algorithm, result in self.clustering_results.items():
                    if 'error' in result:
                        f.write(f"  {algorithm.upper()}: Failed - {result['error']}\n")
                    else:
                        f.write(f"  {algorithm.upper()}: Success\n")
                        f.write(f"    Clusters: {result.get('n_clusters', 'N/A')}\n")
                        f.write(f"    Noise points: {result.get('n_noise', 'N/A')}\n")
                        if 'n_sequences_tested' in result:
                            f.write(f"    Sequences tested: {result['n_sequences_tested']}\n")
                f.write("\n")
            
            f.write("Enhanced analysis completed successfully!\n")
    
    def run_enhanced_workflow(self) -> None:
        """Run the enhanced MSA analysis workflow."""
        self.logger.info("Starting enhanced MSA analysis workflow...")
        
        try:
            # Execute enhanced steps
            self.step1_load_msa()
            self.step2_preprocessing()
            self.step3_sequence_encoding()
            self.step4_basic_analysis()
            self.step5_clustering_analysis()
            self.step6_visualization_testing()  # New visualization testing step
            self.step7_save_results()
            
            self.logger.info("=" * 60)
            self.logger.info("ENHANCED WORKFLOW COMPLETED SUCCESSFULLY!")
            self.logger.info("=" * 60)
            self.logger.info(f"All results saved to: {self.output_dir}")
            
            # Print summary
            self._print_summary()
            
        except Exception as e:
            self.logger.error(f"Enhanced workflow failed: {e}")
            raise
    
    def _print_summary(self) -> None:
        """Print a summary of test results."""
        print("\n" + "=" * 50)
        print("ENHANCED TEST SUMMARY")
        print("=" * 50)
        
        print(f"✓ MSA loaded: {len(self.msa)} sequences")
        print(f"✓ Preprocessed: {len(self.preprocessed_msa)} sequences")
        
        # Encoding summary
        if self.encoding_results:
            print(f"✓ One-Hot encoding: {self.encoding_results.get('onehot', {}).get('shape', 'Failed')}")
            if 'esm' in self.encoding_results:
                if 'error' in self.encoding_results['esm']:
                    print(f"✗ ESM embedding: Failed")
                else:
                    print(f"✓ ESM embedding: {self.encoding_results['esm'].get('shape', 'Success')}")
        
        # Clustering summary
        if self.clustering_results:
            for algorithm, result in self.clustering_results.items():
                if 'error' in result:
                    print(f"✗ {algorithm.upper()} clustering: Failed")
                else:
                    print(f"✓ {algorithm.upper()} clustering: {result.get('n_clusters', 0)} clusters")
        
        print("=" * 50)


def main():
    """Main function to run the enhanced MSA test."""
    parser = argparse.ArgumentParser(
        description="Enhanced MSA analysis test script"
    )
    
    parser.add_argument(
        "--input",
        type=str,
        default="/home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m",
        help="Input MSA file path"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        default="msa_enhanced_test_output",
        help="Output directory"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    parser.add_argument(
        "--test-esm",
        action="store_true",
        help="Enable ESM embedding tests"
    )
    
    parser.add_argument(
        "--test-clustering",
        action="store_true",
        help="Enable clustering tests"
    )
    
    parser.add_argument(
        "--max-sequences",
        type=int,
        default=3000,
        help="Maximum number of sequences to use for testing (default: 3000)"
    )
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not Path(args.input).exists():
        print(f"Error: Input file does not exist: {args.input}")
        sys.exit(1)
    
    # Create and run tester
    tester = EnhancedMSATester(
        input_file=args.input,
        output_dir=args.output,
        verbose=args.verbose,
        test_esm=args.test_esm,
        test_clustering=args.test_clustering,
        max_sequences=args.max_sequences
    )
    
    try:
        # Run standard enhanced workflow
        tester.run_enhanced_workflow()
        print(f"\nEnhanced test completed successfully! Results saved to: {args.output}")
        
    except Exception as e:
        print(f"\nTest failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 