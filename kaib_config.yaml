general:
  output_base_dir: './kaib_clustering_results'
  chain_poly_type: 'protein'
  max_depth: 500  # Limit MSA to 500 sequences for faster testing
  verbose: true

logging:
  level: 'INFO'
  format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
  date_format: '%Y-%m-%d %H:%M:%S'

encoding_methods:
  default: ['MSAtransformer']
  # To test MSA transformer encoding, add 'MSAtransformer' to the list
  # default: ['onehot', 'MSAtransformer']

clustering_methods:
  default: ['dbscan', 'hierarchical']
  # Available: ['dbscan', 'hierarchical', 'hdbscan']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 6.0
      min_samples: 3
    optimization:
      min_eps: 3.0
      max_eps: 20.0
      eps_step: 0.5
      min_samples: [3, 5, 7, 10]
  hierarchical:
    defaults:
      linkage: 'ward'
    optimization:
      min_cluster_size: 19

output_settings:
  save_cluster_a3m_files: true
  save_analysis_results: true
  create_summary_report: true
  cluster_filename_format: '{filename_base}_{cluster_id:03d}.a3m'
  subdirectories:
    clusters: 'clusters'
    analysis: 'analysis'
    visualizations: 'visualizations'

analysis_settings:
  generate_composition_analysis: true
  calculate_quality_metrics: true

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'
  cluster_sequences: true
  create_pca: true
  create_tsne: true
  encoding_method: 'onehot'
  fallback_to_text_summary: true 