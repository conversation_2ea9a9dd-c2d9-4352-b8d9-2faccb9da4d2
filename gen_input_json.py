import json
import string
import argparse
from collections import OrderedDict

def main():
    parser = argparse.ArgumentParser(description="Convert FASTA to JSON for AlphaFold3.")
    parser.add_argument("--input_fasta_file", type=str, required=True, help="Input FASTA file path.")
    parser.add_argument("--output_json_file", type=str, required=True, help="Output JSON file path.")
    parser.add_argument("--metal_ions", type=str, nargs='+', help="Metal ions to include (e.g., ZN MG CA).")
    parser.add_argument("--metal_chain", type=str, default='A', help="Chain ID to bind metal ions (default: A).")
    parser.add_argument("--binding_residues", type=str, nargs='+', help="Residue positions to bind metals (optional, format: 'pos1,pos2,...').")
    parser.add_argument("--model_seeds", type=int, nargs='+', 
                        default=[0, 1, 10, 100, 1000, 10000, 100000, 1000000,
                                 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096],
                        help="List of model seeds for AlphaFold3 simulations.")
    args = parser.parse_args()

    # Assign protein sequence IDs in alphabetical order (A, B, C, ...)
    id_list = list(string.ascii_uppercase)  # chain order: A, B, C, ...

    # Initialize variables
    name = None  # name field initialization
    sequences = []
        
    print(f'[INFO ] Read {args.input_fasta_file} fastafile')
    with open(args.input_fasta_file, 'r') as fasta_file:
        lines = fasta_file.readlines()
        name = lines[0].strip().replace('>', '')  # remove '>' from the first line
        for idx, line in enumerate(lines[1:]): 
            seq = line.strip().replace(':', '')  # remove ':'
            if seq:  # ignore empty lines
                sequences.append({
                    "id": id_list[idx],  # chain order: A, B, C, ...
                    "sequence": seq
                })                
    print(f'  INFO: {len(sequences)} sequences found in given fasta file')

    
    print(f'[INFO ] Generate JSON data for AlphaFold3 input')
    input_data = OrderedDict()
    input_data["dialect"] = "alphafold3"
    input_data["version"] = 1
    input_data["modelSeeds"] = args.model_seeds
    input_data["name"] = name 
    input_data["sequences"] = []

    # Add protein sequences
    for seq in sequences:
        protein_entry = OrderedDict()
        protein_entry["protein"] = OrderedDict()
        protein_entry["protein"]["id"] = seq["id"]
        protein_entry["protein"]["sequence"] = seq["sequence"]
        # [TODO] 나중에 필요에 따라 추가 필드를 설정 가능 (modifications, templates 등)
        input_data["sequences"].append(protein_entry)
    
    # Add metal ions if specified
    if args.metal_ions:
        print(f'[INFO ] Adding metal ions: {args.metal_ions}')
        
        # Parse binding residues if provided
        binding_residues = None
        if args.binding_residues and len(args.binding_residues) > 0:
            binding_residues = [pos.split(',') for pos in args.binding_residues]
            print(f'  INFO: Binding residues: {binding_residues}')
        
        # Start assigning IDs from after the protein chains
        metal_ids = list(string.ascii_uppercase)[len(sequences):]
        
        # Add metal ions as separate sequences entries with ligand type
        for i, metal in enumerate(args.metal_ions):
            # Skip if we run out of available IDs
            if i >= len(metal_ids):
                print(f"[WARNING] Out of available chain IDs, skipping metal {metal}")
                continue
                
            metal_entry = OrderedDict()
            metal_entry["ligand"] = OrderedDict()
            metal_entry["ligand"]["id"] = metal_ids[i]
            metal_entry["ligand"]["ccdCodes"] = [metal]
            
            # Add binding information if specified
            if binding_residues and i < len(binding_residues):
                metal_entry["ligand"]["binding"] = {
                    "chain": args.metal_chain,
                    "residues": binding_residues[i]
                }
                
            input_data["sequences"].append(metal_entry)
            print(f"  INFO: Added metal ion {metal} with ID {metal_ids[i]}")
        
        # # If we have specific binding information, add bondedAtomPairs
        # if binding_residues:
        #     input_data["bondedAtomPairs"] = []
        #     # Example bond specification (would need more detailed implementation)
        #     # input_data["bondedAtomPairs"].append([
        #     #     [args.metal_chain, int(binding_residues[0][0]), "CA"],
        #     #     [metal_ids[0], 1, metal]
        #     # ])
            
        print(f'  INFO: Added {len(args.metal_ions)} metal ions')

    print(f'[INFO ] Save JSON file')
    with open(args.output_json_file, "w", encoding="utf-8") as json_file:
        json.dump(input_data, json_file, ensure_ascii=False, indent=4)
    print(f"  INFO: save {args.output_json_file} json file")


if __name__ == "__main__":
    main()
    
