import os, sys
import logging
from colorama import Fore, Style, Back

# 계층 구분을 위한 로깅 레벨 정의
MAIN_INFO = 25  # 주요 단계 ([INFO ])
SUB_INFO = 24   # 하위 정보 (  INFO:)
logging.addLevelName(MAIN_INFO, 'MAIN_INFO')
logging.addLevelName(SUB_INFO, 'SUB_INFO')

class HierarchicalLogger(logging.Logger):
    """계층적 로깅을 위한 커스텀 로거 클래스"""
    
    def main_info(self, msg, *args, **kwargs):
        """주요 단계를 로깅 ([INFO ])"""
        self.log(MAIN_INFO, msg, *args, **kwargs)
    
    def sub_info(self, msg, *args, **kwargs):
        """하위 단계 정보를 로깅 (  INFO:)"""
        self.log(SUB_INFO, msg, *args, **kwargs)

# 커스텀 로거 등록
logging.setLoggerClass(HierarchicalLogger)

class MyCustomFormatter(logging.Formatter):
    # log level emoji
    EMOJIS = {
        "DEBUG": "🔍 ",
        "INFO": "ℹ️ ",
        "MAIN_INFO": "ℹ️ ",
        "SUB_INFO": "ℹ️ ",
        "WARNING": "⚠️ ",
        "ERROR": "❌ ",
        "CRITICAL": "🔥 ",
    }
    # log level color
    COLORS = {
        logging.DEBUG: Fore.CYAN,
        #logging.INFO: Fore.GREEN,
        #MAIN_INFO: Fore.GREEN + Style.BRIGHT,
        #SUB_INFO: Fore.GREEN,
        logging.INFO: Back.CYAN + Fore.YELLOW + Style.BRIGHT,
        MAIN_INFO: Fore.WHITE + Style.BRIGHT,
        SUB_INFO: Fore.WHITE,
        logging.WARNING: Fore.YELLOW,
        logging.ERROR: Fore.RED,
        logging.CRITICAL: Fore.MAGENTA + Style.BRIGHT,
    }
    
    # 계층별 출력 형식
    FORMAT_PATTERNS = {
        logging.DEBUG: "%(message)s",
        logging.INFO: "%(message)s",
        MAIN_INFO: "[INFO ] %(message)s",
        SUB_INFO: "  INFO: %(message)s",
        logging.WARNING: "[WARNING] %(message)s",
        logging.ERROR: "[ERROR] %(message)s",
        logging.CRITICAL: "[CRITICAL] %(message)s"
    }

    def format(self, record):
        # 레벨에 맞는 형식 가져오기
        log_format = self.FORMAT_PATTERNS.get(record.levelno, "%(message)s")
        self._style._fmt = log_format
        
        # 이모지 추가 (계층에 따라 출력 형식이 다르므로 이모지는 사용하지 않음)
        if record.levelno not in [MAIN_INFO, SUB_INFO]:
            emoji = self.EMOJIS.get(record.levelname, "")
            record.levelname = f"{emoji} {record.levelname}"
        
        # 색상 적용 (콘솔 출력에만)
        if hasattr(self, 'console_output') and self.console_output:
            color = self.COLORS.get(record.levelno, Fore.WHITE)
            formatted_message = super().format(record)
            return f"{color}{formatted_message}{Style.RESET_ALL}"
        
        return super().format(record)

    
def get_logger(log_dir=None, log_filename='structure_similarity.log', console_level=logging.INFO, file_level=logging.DEBUG):
    """
    계층적 로깅을 지원하는 로거를 생성합니다.
    
    Parameters
    ----------
    log_dir : str, optional
        로그 파일을 저장할 디렉토리 경로. None인 경우 현재 디렉토리에 저장.
    log_filename : str, optional
        로그 파일의 이름.
    console_level : int, optional
        콘솔 출력의 로깅 레벨.
    file_level : int, optional
        파일 출력의 로깅 레벨.
        
    Returns
    -------
    logging.Logger
        설정된 로거 객체
    """
    logger = logging.getLogger("structure_similarity")
    logger.setLevel(min(console_level, file_level))
    
    # 이미 핸들러가 존재하면 중복 추가를 방지
    if logger.hasHandlers():
        logger.handlers.clear()

    # 콘솔 핸들러
    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)
    
    # 로그 디렉토리 설정
    if log_dir:
        try:
            os.makedirs(log_dir, exist_ok=True)
            log_path = os.path.join(log_dir, log_filename)
        except Exception as e:
            print(f"[WARNING] 로그 디렉토리 생성 실패: {e}")
            log_path = log_filename
    else:
        log_path = log_filename
    
    # 파일 핸들러
    try:
        file_handler = logging.FileHandler(log_path)
        file_handler.setLevel(file_level)
    except Exception as e:
        print(f"[WARNING] 로그 파일 생성 실패: {e}")
        file_handler = None

    # 포맷터 설정
    console_formatter = MyCustomFormatter("[%(asctime)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
    console_formatter.console_output = True
    console_handler.setFormatter(console_formatter)
    
    # 핸들러 추가
    logger.addHandler(console_handler)
    
    if file_handler:
        file_formatter = MyCustomFormatter("[%(asctime)s | %(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S")
        file_formatter.console_output = False
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger

# 계층적 로거 생성 함수
def create_hierarchical_logger(target_dir=None, filename='structure_similarity.log', 
                              console_level=MAIN_INFO, file_level=logging.DEBUG):
    """
    계층적 로깅을 지원하는 로거를 생성합니다.
    
    Parameters
    ----------
    target_dir : str, optional
        로그 파일을 저장할 디렉토리 경로.
    filename : str, optional
        로그 파일의 이름.
    console_level : int, optional
        콘솔 출력의 로깅 레벨 (기본값: MAIN_INFO).
    file_level : int, optional
        파일 출력의 로깅 레벨 (기본값: DEBUG).
        
    Returns
    -------
    logging.Logger
        설정된 계층적 로거 객체
    """
    return get_logger(target_dir, filename, console_level, file_level)

# 하위 호환성을 위한 기본 로거 생성
MyLogger = get_logger()
