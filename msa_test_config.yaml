# MSA Clustering Test Configuration
# ================================

# General Settings
input_file: "/home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m"
output_dir: "msa_enhanced_test_output"
max_sequences: 3000
verbose: false

# Module Enable/Disable Flags
test_esm: false          # Enable ESM embedding tests (memory intensive)
test_clustering: false   # Enable clustering tests
test_visualization: true # Enable visualization tests

# Preprocessing Configuration
preprocessing:
  max_gap_content: 0.4        # 40% gap threshold (updated from 80%)
  identity_threshold: 1.0     # 100% identity for deduplication (updated from 95%)
  remove_lowercase: false     # Preserve lowercase characters for clustering
  deduplicate: true          # Remove duplicate sequences

# Encoding Configuration
encoding:
  methods: ["onehot"]         # Available: ["onehot", "esm"]
  esm_enabled: false         # Enable ESM transformer encoding
  esm_batch_size: 256        # Batch size for ESM processing
  esm_max_length: 1000       # Maximum sequence length for ESM

# Clustering Configuration
clustering:
  algorithms: ["dbscan", "hierarchical", "hdbscan"]  # Test all algorithms
  enabled: true              # Enable clustering tests
  
  # DBSCAN parameters
  dbscan:
    eps: 8.0                 # DBSCAN epsilon parameter
    min_samples: 5           # Minimum samples per cluster
    gap_cutoff: 0.4          # Gap content filtering threshold
  
  # Hierarchical clustering parameters
  hierarchical:
    n_clusters: null         # Number of clusters (null for auto-detection)
    linkage: "ward"          # Linkage method
    distance_threshold: null # Distance threshold for clustering
  
  # HDBSCAN parameters
  hdbscan:
    min_cluster_size: 15     # Minimum cluster size
    min_samples: null        # Minimum samples (null for auto)
    cluster_selection_method: "eom"  # Cluster selection method

# Visualization Configuration
visualization:
  enabled: true              # Enable visualization tests
  presets: ["quick"]         # Available: ["quick", "publication", "presentation", "web"]
  create_clustering_viz: true # Create clustering-specific visualizations
  create_dimensional_reduction: false # Create PCA/t-SNE plots (computationally expensive)

# Note: Advanced settings will be added in future versions
# advanced:
#   parallel_processing: false # Enable parallel processing
#   cache_results: false      # Cache intermediate results
#   memory_limit_gb: 8        # Memory limit in GB
