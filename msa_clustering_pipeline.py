#!/usr/bin/env python3
"""
MSA Clustering Pipeline - Function-based Implementation

A modular, function-based framework for MSA clustering that leverages
existing components from msa_pipeline.py and ClusteringManager.

This pipeline provides:
- YAML-based configuration management
- Modular function design for better maintainability  
- Integration with existing MSA and clustering infrastructure
- Comprehensive analysis and reporting

Usage:
    # Standard usage with full configuration
    python msa_clustering_pipeline.py input_file.a3m [--config clustering_config.yaml]
    
    # Simple DBSCAN-only usage (equivalent to simple_msa_clustering_pipeline.py)
    # Create a simple config file or use defaults:
    # encoding_methods: {default: ['onehot']}
    # clustering_methods: {default: ['dbscan']}
    python msa_clustering_pipeline.py input_file.a3m --config simple_config.yaml
"""

import os, sys, json, time
import yaml, logging, argparse
import numpy as np

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt


from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union

# Import existing MSA infrastructure
from msa.msa_pipeline import MSA
from msa.clustering.manager import ClusteringManager
from msa.clustering.utils.postprocessing import (
    analyze_cluster_composition,
    generate_cluster_summary_report,
    extract_cluster_representatives,
    calculate_clustering_metrics
)

# Add MSAVisualizer import
try:
    from msa.msa_visualizer import MSAVisualizer
except ImportError:
    MSAVisualizer = None
    logging.warning("MSAVisualizer not available. Visualization will be limited.")


# ==========================================
# Configuration Management Functions
# ==========================================
def load_clustering_config(config_path: str = "clustering_config.yaml") -> Dict[str, Any]:
    """
    Load clustering configuration from YAML file.
    
    Parameters
    ----------
    config_path : str
        Path to the YAML configuration file
        
    Returns
    -------
    Dict[str, Any]
        Configuration dictionary with all settings
        
    Raises
    ------
    FileNotFoundError
        If configuration file doesn't exist
    yaml.YAMLError
        If YAML parsing fails
    """
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            logging.warning(f"Config file {config_path} not found. Using default settings.")
            return _get_default_config()
            
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
            
        # Validate configuration structure
        for section in ['general', 'encoding_methods', 'clustering_methods']:
            if section not in config:
                raise ValueError(f"Required configuration section '{section}' is missing")
        logging.info(f"Successfully loaded configuration from {config_path}")
        
        return config
        
    except yaml.YAMLError as e:
        logging.error(f"Error parsing YAML configuration: {e}")
        raise
    except Exception as e:
        logging.error(f"Error loading configuration: {e}")
        raise


def _get_default_config() -> Dict[str, Any]:
    """Get default configuration if config file is not available."""
    return {
        'general': {
            'output_base_dir': './clustering_results',
            'chain_poly_type': 'protein',
            'max_depth': None,
            'verbose': False
        },
        'encoding_methods': {
            'default': ['onehot']
        },
        'clustering_methods': {
            'default': ['dbscan']
        },
        'algorithm_parameters': {
            'dbscan': {
                'defaults': {'eps': 6.0, 'min_samples': 3}
            }
        },
        'output_settings': {
            'save_cluster_a3m_files': True,
            'subdirectories': {
                'clusters': 'clusters',
                'analysis': 'analysis',
                'visualizations': 'visualizations'
            }
        }
    }


def setup_logging_from_config(config: Dict[str, Any]) -> None:
    """
    Setup logging based on configuration settings.
    
    Parameters
    ----------
    config : Dict[str, Any]
        Configuration dictionary containing logging settings
    """
    log_config = config.get('logging', {})
    
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR
    }
    
    level = level_map.get(log_config.get('level', 'INFO'), logging.INFO)
    format_str = log_config.get('format', '%(asctime)s [%(levelname)s] %(name)s: %(message)s')
    date_format = log_config.get('date_format', '%Y-%m-%d %H:%M:%S')
    
    logging.basicConfig(
        level=level,
        format=format_str,
        datefmt=date_format,
        force=True  # Override any existing logging configuration
    )


# ==========================================
# MSA Loading Functions
# ==========================================

def load_msa_from_file(
    input_file: str, 
    config: Dict[str, Any]
) -> MSA:
    """
    Load MSA from file using existing MSA infrastructure.
    
    Parameters
    ----------
    input_file : str
        Path to the input A3M file
    config : Dict[str, Any]
        Configuration dictionary
        
    Returns
    -------
    MSA
        Loaded MSA object
        
    Raises
    ------
    FileNotFoundError
        If input file doesn't exist
    ValueError
        If MSA loading fails
    """
    input_path = Path(input_file)
    if not input_path.exists():
        raise FileNotFoundError(f"Input file not found: {input_file}")
    
    general_config = config.get('general', {})
    
    try:
        logging.info(f"Loading MSA from {input_file}")
        
        msa = MSA.from_file(
            file_path=str(input_path),
            chain_poly_type=general_config.get('chain_poly_type', 'protein'),
            max_depth=general_config.get('max_depth'),
            deduplicated=True
        )
        
        logging.info(f"Successfully loaded MSA with {msa.depth} sequences")
        logging.info(f"Average sequence length: {sum(len(s) for s in msa.sequences)/len(msa.sequences):.1f}")
        
        return msa
        
    except Exception as e:
        logging.error(f"Failed to load MSA: {e}")
        raise ValueError(f"MSA loading failed: {e}") from e


# ==========================================
# Parameter Optimization Functions  
# ==========================================

def optimize_algorithm_parameters(
    msa: MSA,
    algorithm: str,
    encoding_method: str,
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Optimize clustering parameters for the specified algorithm.
    
    This function extracts and refactors the parameter optimization logic
    from the original framework, using existing algorithm implementations.
    
    Parameters
    ----------
    msa : MSA
        MSA object containing sequences
    algorithm : str
        Clustering algorithm name ('dbscan', 'hierarchical', 'hdbscan')
    encoding_method : str
        Encoding method ('onehot', 'MSAtransformer')
    config : Dict[str, Any]
        Configuration dictionary
        
    Returns
    -------
    Dict[str, Any]
        Optimized parameters for the algorithm
    """
    logging.info(f"Optimizing parameters for {algorithm} with {encoding_method} encoding")
    
    # Get algorithm configuration
    algo_config = config.get('algorithm_parameters', {}).get(algorithm, {})
    default_params = algo_config.get('defaults', {})
    
    # Add encoding method to parameters
    optimized_params = default_params.copy()
    optimized_params['encoding_method'] = encoding_method
    
    try:
        if algorithm == 'dbscan':
            optimized_params.update(_optimize_dbscan_parameters(msa, algo_config))
        elif algorithm == 'hierarchical':
            optimized_params.update(_optimize_hierarchical_parameters(msa, algo_config))
        else:
            # For algorithms without specific optimization (like HDBSCAN)
            logging.info(f"Using default parameters for {algorithm} (no optimization implemented)")
            
    except Exception as e:
        logging.warning(f"Parameter optimization failed for {algorithm}: {e}. Using defaults.")
    
    logging.info(f"Final parameters for {algorithm}: {optimized_params}")
    return optimized_params


def _optimize_dbscan_parameters(msa: MSA, algo_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize DBSCAN parameters using existing implementation.
    
    Parameters
    ----------
    msa : MSA
        MSA object
    algo_config : Dict[str, Any]
        Algorithm-specific configuration
        
    Returns
    -------
    Dict[str, Any]
        Optimized DBSCAN parameters
    """
    try:
        # Convert to DBSCANClusterMSA to use existing optimization
        dbscan_msa = msa.to_dbscan_cluster_msa()
        
        # Get optimization parameters
        opt_config = algo_config.get('optimization', {})
        
        # Filter sequences based on gap content
        original_sequences, _, _ = dbscan_msa._filter_sequences(
            algo_config.get('gap_cutoff', 0.25)
        )
        
        # Prepare sequences for clustering (remove lowercase)
        filtered_sequences = [
            ''.join([char for char in seq if not char.islower()])
            for seq in original_sequences
        ]
        
        # Encode sequences
        max_seq_len = max(len(seq) for seq in filtered_sequences)
        encoding_method = algo_config.get('encoding_method', 'onehot')
        
        if encoding_method == 'MSAtransformer':
            encoded_seqs = dbscan_msa._encode_MSAtransformer(
                sequences=original_sequences[1:],  # Exclude query sequence
                max_seq_len=max_seq_len,
                remove_lowercase=algo_config.get('remove_lowercase', False),
                remove_special_chars=algo_config.get('remove_special_chars', True)
            )
        else:
            encoded_seqs = dbscan_msa.encode_sequences(
                sequences=filtered_sequences[1:],  # Exclude query sequence
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
        
        # Use existing optimization method
        from msa.dbscan_cluster import DBSCANClusterMSA
        best_params = DBSCANClusterMSA._optimize_dbscan_parameters(
            encoded_seqs=encoded_seqs,
            min_eps=opt_config.get('min_eps', 3.0),
            max_eps=opt_config.get('max_eps', 20.0),
            eps_step=opt_config.get('eps_step', 0.5),
            min_samples=opt_config.get('min_samples', [3])[0]  # Use first value
        )
        
        logging.info(f"DBSCAN optimization: eps={best_params['eps']}, "
                    f"min_samples={best_params['min_samples']}, "
                    f"n_clusters={best_params['n_clusters']}")
        
        return best_params
        
    except Exception as e:
        logging.warning(f"DBSCAN optimization failed: {e}")
        return {}


def _optimize_hierarchical_parameters(msa: MSA, algo_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize Hierarchical clustering parameters using existing implementation.
    
    Parameters
    ----------
    msa : MSA
        MSA object
    algo_config : Dict[str, Any]
        Algorithm-specific configuration
        
    Returns
    -------
    Dict[str, Any]
        Optimized hierarchical parameters
    """
    try:
        # Convert to HierarchicalClusterMSA
        hierarchical_msa = msa.to_hierarchical_cluster_msa()
        
        # Get optimization parameters
        opt_config = algo_config.get('optimization', {})
        
        # Filter and encode sequences
        original_sequences, _, _ = hierarchical_msa._filter_sequences(
            algo_config.get('gap_cutoff', 0.25)
        )
        
        filtered_sequences = [
            ''.join([char for char in seq if not char.islower()])
            for seq in original_sequences
        ]
        
        max_seq_len = max(len(seq) for seq in filtered_sequences)
        encoding_method = algo_config.get('encoding_method', 'onehot')
        
        if encoding_method == 'MSAtransformer':
            encoded_seqs = hierarchical_msa._encode_MSAtransformer(
                sequences=original_sequences[1:],
                max_seq_len=max_seq_len,
                remove_lowercase=algo_config.get('remove_lowercase', False),
                remove_special_chars=algo_config.get('remove_special_chars', True)
            )
        else:
            encoded_seqs = hierarchical_msa.encode_sequences(
                sequences=filtered_sequences[1:],
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
        
        # Calculate distance matrix and optimize
        distance_matrix = hierarchical_msa.calculate_pairwise_distance(encoded_seqs)
        
        from scipy.cluster.hierarchy import ward
        from scipy.spatial.distance import squareform
        
        dist_condensed = squareform(distance_matrix)
        Z = ward(dist_condensed)
        
        # Find optimal clustering
        min_cluster_size = opt_config.get('min_cluster_size', 19)
        max_clusters, _ = hierarchical_msa._search_minsize_clustering(
            Z, min_cluster_size, len(encoded_seqs)
        )
        
        optimized_params = {}
        if max_clusters is not None:
            optimized_params['n_clusters'] = max_clusters
            logging.info(f"Hierarchical optimization: n_clusters={max_clusters}")
        else:
            logging.warning("Hierarchical optimization failed to find suitable clustering")
            
        return optimized_params
        
    except Exception as e:
        logging.warning(f"Hierarchical optimization failed: {e}")
        return {}


# ==========================================
# Clustering Execution Functions
# ==========================================

def execute_clustering_combinations(
    msa: MSA,
    config: Dict[str, Any],
    input_file_path: str
) -> Dict[str, Dict[str, Any]]:
    """
    Execute clustering for all specified encoding and algorithm combinations.
    
    This function replaces the complex nested logic from the original framework
    with a clear, sequential approach using existing ClusteringManager.
    
    Parameters
    ----------
    msa : MSA
        MSA object to cluster
    config : Dict[str, Any]
        Configuration dictionary
    input_file_path : str
        Path to the input file (for naming outputs)
        
    Returns
    -------
    Dict[str, Dict[str, Any]]
        Dictionary of results keyed by combination names
    """
    logging.info("Starting clustering pipeline")
    
    # Get methods to test
    encoding_methods = config.get('encoding_methods', {}).get('default', ['onehot'])
    clustering_methods = config.get('clustering_methods', {}).get('default', ['dbscan'])
    
    # Get output configuration
    output_base_dir = Path(config.get('general', {}).get('output_base_dir', './clustering_results'))
    
    total_combinations = len(encoding_methods) * len(clustering_methods)
    current_combination = 0
    results = {}
    
    # Get clustering manager from MSA
    clustering_mgr = msa.get_clustering_manager()
    
    for encoding_method in encoding_methods:
        for clustering_method in clustering_methods:
            current_combination += 1
            combination_key = f"{encoding_method}_{clustering_method}"
            
            logging.info(
                f"Processing combination {current_combination}/{total_combinations}: "
                f"{encoding_method} + {clustering_method}"
            )
            
            try:
                # Generate output directory following existing naming convention
                output_dir = _generate_output_directory(
                    input_file_path, encoding_method, clustering_method, output_base_dir
                )
                output_dir.mkdir(parents=True, exist_ok=True)
                
                # Optimize parameters for this combination
                start_time = time.time()
                optimized_params = optimize_algorithm_parameters(
                    msa, clustering_method, encoding_method, config
                )
                
                # Remove parameters that are not accepted by the clustering algorithms
                clustering_params = optimized_params.copy()
                clustering_params.pop('n_clusters', None)  # DBSCAN doesn't accept n_clusters as input
                
                # Execute clustering using ClusteringManager
                logging.info(f"Running {clustering_method} clustering with optimized parameters")
                clustering_result = clustering_mgr.cluster(clustering_method, **clustering_params)
                
                elapsed_time = time.time() - start_time
                
                # Store results
                results[combination_key] = {
                    'result': clustering_result,
                    'output_dir': output_dir,
                    'elapsed_time': elapsed_time,
                    'optimized_params': optimized_params,
                    'encoding_method': encoding_method,
                    'clustering_method': clustering_method
                }
                
                logging.info(
                    f"Completed {combination_key}: {clustering_result.n_clusters} clusters, "
                    f"{clustering_result.n_noise} noise points, {elapsed_time:.2f}s"
                )
                
            except Exception as e:
                logging.error(f"Failed clustering for {combination_key}: {e}")
                continue
                
    return results


def _generate_output_directory(
    input_file_path: str,
    encoding_method: str,
    clustering_method: str,
    output_base_dir: Path
) -> Path:
    """
    Generate output directory following the existing naming convention.
    
    Format: {filename}_{encoding}_{clustering}
    
    Parameters
    ----------
    input_file_path : str
        Path to input file
    encoding_method : str
        Encoding method name
    clustering_method : str
        Clustering method name
    output_base_dir : Path
        Base output directory
        
    Returns
    -------
    Path
        Generated output directory path
    """
    filename_base = Path(input_file_path).stem  # Remove .a3m extension
    dir_name = f"{filename_base}_{encoding_method}_{clustering_method}"
    return output_base_dir / dir_name


# ==========================================
# Results Management Functions
# ==========================================

def save_cluster_outputs(
    msa: MSA,
    results: Dict[str, Dict[str, Any]],
    config: Dict[str, Any],
    input_file_path: str
) -> None:
    """
    Save clustering results to files with proper organization.
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    results : Dict[str, Dict[str, Any]]
        Clustering results from all combinations
    config : Dict[str, Any]
        Configuration dictionary
    input_file_path : str
        Path to the input file
    """
    output_config = config.get('output_settings', {})
    
    # Extract the base name of the input file (without extension)
    input_file_base = Path(input_file_path).stem
    
    for combination_key, result_data in results.items():
        try:
            logging.info(f"Saving results for {combination_key}")
            
            output_dir = result_data['output_dir']
            clustering_result = result_data['result']
            
            # Create subdirectories
            subdirs = output_config.get('subdirectories', {})
            clusters_dir = output_dir / subdirs.get('clusters', 'clusters')
            analysis_dir = output_dir / subdirs.get('analysis', 'analysis')
            
            clusters_dir.mkdir(exist_ok=True)
            analysis_dir.mkdir(exist_ok=True)
            
            # Save cluster A3M files if requested
            if output_config.get('save_cluster_a3m_files', True):
                _save_cluster_a3m_files(msa, clustering_result, clusters_dir, config, input_file_base)
            
            # Save analysis results if requested  
            if output_config.get('save_analysis_results', True):
                _save_analysis_files(clustering_result, analysis_dir, result_data, config)
                
            logging.info(f"Successfully saved results for {combination_key}")
            
        except Exception as e:
            logging.error(f"Failed to save results for {combination_key}: {e}")


def _save_cluster_a3m_files(
    msa: MSA,
    clustering_result,
    clusters_dir: Path,
    config: Dict[str, Any],
    input_file_base: str
) -> None:
    """
    Save individual cluster A3M files.
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    clustering_result : ClusteringResult
        Clustering result object
    clusters_dir : Path
        Directory to save cluster files
    config : Dict[str, Any]
        Configuration dictionary
    input_file_base : str
        Base name of the input file (without extension)
    """
    if clustering_result.n_clusters == 0:
        logging.warning("No clusters to save")
        return
        
    output_config = config.get('output_settings', {})
    filename_format = output_config.get('cluster_filename_format', '{filename_base}_{cluster_id:03d}.a3m')
    
    logging.info(f"Saving {clustering_result.n_clusters} cluster A3M files")
    
    # Collect cluster sizes for sorting
    cluster_sizes = []
    for cluster_id in range(clustering_result.n_clusters):
        cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
        cluster_sizes.append((cluster_id, len(cluster_sequences)))
    
    # Sort clusters by size (descending order)
    sorted_clusters = sorted(cluster_sizes, key=lambda x: x[1], reverse=True)
    
    # Save clusters in order of size
    for idx, (original_cluster_id, size) in enumerate(sorted_clusters):
        try:
            cluster_sequences = clustering_result.get_cluster_sequences(original_cluster_id)
            cluster_descriptions = clustering_result.get_cluster_descriptions(original_cluster_id)
            
            if cluster_sequences:
                # Create MSA for this cluster
                cluster_msa = MSA(
                    query_sequence=msa.query_sequence,
                    chain_poly_type=msa.chain_poly_type,
                    sequences=cluster_sequences,
                    descriptions=cluster_descriptions,
                    deduplicated=False
                )
                
                # Generate filename using the input file base name and sorted index
                cluster_filename = filename_format.format(
                    filename_base=input_file_base,
                    cluster_id=idx  # Use sorted index instead of original cluster_id
                )
                cluster_file_path = clusters_dir / cluster_filename
                
                # Save A3M file
                with open(cluster_file_path, 'w') as f:
                    f.write(cluster_msa.to_a3m())
                    
        except Exception as e:
            logging.error(f"Failed to save cluster {original_cluster_id}: {e}")


def _save_analysis_files(
    clustering_result,
    analysis_dir: Path,
    result_data: Dict[str, Any],
    config: Dict[str, Any]
) -> None:
    """Save analysis results and metadata."""
    analysis_config = config.get('analysis_settings', {})
    
    try:
        # Save clustering summary
        summary_data = {
            'algorithm': clustering_result.algorithm_name,
            'n_clusters': clustering_result.n_clusters,
            'n_noise': clustering_result.n_noise,
            'total_sequences': len(clustering_result.sequences),
            'noise_percentage': clustering_result.n_noise / len(clustering_result.sequences) * 100,
            'elapsed_time': result_data['elapsed_time'],
            'optimized_params': result_data['optimized_params']
        }
        
        summary_file = analysis_dir / "clustering_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary_data, f, indent=2, default=str)
        
        # Save cluster composition if requested
        if analysis_config.get('generate_composition_analysis', True):
            composition = analyze_cluster_composition(clustering_result)
            composition_file = analysis_dir / "cluster_composition.json"
            with open(composition_file, 'w') as f:
                json.dump(composition, f, indent=2, default=str)
        
        # Save quality metrics if requested and feasible
        if (analysis_config.get('calculate_quality_metrics', True) and 
            clustering_result.n_clusters > 1):
            
            try:
                # Encode sequences for quality metrics
                max_len = max(len(seq) for seq in clustering_result.sequences)
                encoded_seqs = MSA.encode_sequences(
                    sequences=clustering_result.sequences,
                    max_seq_len=max_len,
                    encoding_method='onehot'
                )
                
                quality_metrics = calculate_clustering_metrics(encoded_seqs, clustering_result.cluster_labels)
                metrics_file = analysis_dir / "quality_metrics.json"
                with open(metrics_file, 'w') as f:
                    json.dump(quality_metrics, f, indent=2, default=str)
                    
            except Exception as e:
                logging.warning(f"Failed to calculate quality metrics: {e}")
        
    except Exception as e:
        logging.error(f"Failed to save analysis files: {e}")


# ==========================================
# Visualization Functions
# ==========================================

def create_analysis_visualizations(
    msa: MSA,
    results: Dict[str, Dict[str, Any]], 
    config: Dict[str, Any]
) -> None:
    """
    Generate comprehensive visualizations for clustering results.
    
    This function integrates MSAVisualizer and adds clustering-specific 
    visualizations to provide comprehensive analysis.
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    results : Dict[str, Dict[str, Any]]
        Clustering results
    config : Dict[str, Any]
        Configuration dictionary
    """
    viz_config = config.get('visualization_settings', {})
    
    if not viz_config.get('create_msa_plots', True):
        logging.info("Visualization creation disabled in configuration")
        return
        
    logging.info("Generating comprehensive visualizations")
    
    for combination_key, result_data in results.items():
        try:
            output_dir = result_data['output_dir']
            subdirs = config.get('output_settings', {}).get('subdirectories', {})
            viz_dir = output_dir / subdirs.get('visualizations', 'visualizations')
            viz_dir.mkdir(exist_ok=True)
            
            clustering_result = result_data['result']
            
            # Create basic MSA visualizations using MSAVisualizer
            _create_basic_msa_visualizations(
                msa, viz_dir, combination_key, viz_config
            )
            
            # Create clustering-specific visualizations
            _create_clustering_visualizations(
                msa, clustering_result, viz_dir, combination_key, viz_config
            )
            
            # Create dimensional reduction visualizations (PCA/t-SNE)
            _create_dimensional_reduction_visualizations(
                msa, clustering_result, viz_dir, combination_key, viz_config
            )
            
            # Create cluster-specific MSA visualizations if clusters exist
            if clustering_result.n_clusters > 0:
                _create_cluster_specific_visualizations(
                    msa, clustering_result, viz_dir, combination_key, viz_config
                )
                
            logging.info(f"Generated comprehensive visualizations for {combination_key}")
            
        except Exception as e:
            logging.error(f"Failed to generate visualizations for {combination_key}: {e}")
            if viz_config.get('fallback_to_text_summary', True):
                _create_text_summary_fallback(result_data, viz_dir, combination_key)


def _create_basic_msa_visualizations(
    msa: MSA,
    viz_dir: Path,
    combination_key: str,
    viz_config: Dict[str, Any]
) -> None:
    """
    Create basic MSA visualizations using MSAVisualizer.
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    viz_dir : Path
        Visualization output directory
    combination_key : str
        Combination identifier for file naming
    viz_config : Dict[str, Any]
        Visualization configuration
    """
    if MSAVisualizer is None:
        logging.warning("MSAVisualizer not available, skipping basic MSA visualizations")
        return
    
    try:
        # Create MSAVisualizer instance
        visualizer = MSAVisualizer(msa)
        
        # Generate basic MSA visualizations
        prefix = f"{combination_key}_"
        
        # Conservation heatmap
        conservation_path = viz_dir / f"{prefix}conservation_heatmap.pdf"
        visualizer.visualize_conservation(
            output_path=str(conservation_path),
            title=f"Conservation Analysis - {combination_key}"
        )
        
        # Sequence similarity visualization
        similarity_path = viz_dir / f"{prefix}sequence_similarity.pdf"
        visualizer.visualize_similarity(
            output_path=str(similarity_path),
            method=viz_config.get('similarity_method', 'identity'),
            cluster_sequences=viz_config.get('cluster_sequences', True),
            title=f"Sequence Similarity - {combination_key}"
        )
        
        logging.info(f"Created basic MSA visualizations for {combination_key}")
        
    except Exception as e:
        logging.warning(f"Failed to create basic MSA visualizations for {combination_key}: {e}")


def _create_clustering_visualizations(
    msa: MSA,
    clustering_result,
    viz_dir: Path,
    combination_key: str,
    viz_config: Dict[str, Any]
) -> None:
    """
    Create clustering-specific visualizations.
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    clustering_result : ClusteringResult
        Clustering result object
    viz_dir : Path
        Visualization output directory
    combination_key : str
        Combination identifier
    viz_config : Dict[str, Any]
        Visualization configuration
    """
    try:
        # Cluster size distribution
        _plot_cluster_size_distribution(
            clustering_result, viz_dir, combination_key
        )
        
        # Clustering summary chart
        _plot_clustering_summary(
            clustering_result, viz_dir, combination_key
        )
        
        # If we have multiple clusters, create cluster comparison
        if clustering_result.n_clusters > 1:
            _plot_cluster_comparison(
                clustering_result, viz_dir, combination_key
            )
        
        logging.info(f"Created clustering-specific visualizations for {combination_key}")
        
    except Exception as e:
        logging.warning(f"Failed to create clustering visualizations for {combination_key}: {e}")


def _create_dimensional_reduction_visualizations(
    msa: MSA,
    clustering_result,
    viz_dir: Path,
    combination_key: str,
    viz_config: Dict[str, Any]
) -> None:
    """
    Create dimensional reduction visualizations (PCA and t-SNE).
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    clustering_result : ClusteringResult
        Clustering result object
    viz_dir : Path
        Visualization output directory
    combination_key : str
        Combination identifier
    viz_config : Dict[str, Any]
        Visualization configuration
    """
    if MSAVisualizer is None:
        logging.warning("MSAVisualizer not available, skipping dimensional reduction visualizations")
        return
    
    try:
        # Create MSAVisualizer instance
        visualizer = MSAVisualizer(msa)
        
        # Create dimensional reduction visualizations
        encoding_method = viz_config.get('encoding_method', 'onehot')
        create_pca = viz_config.get('create_pca', True)
        create_tsne = viz_config.get('create_tsne', True)
        
        visualizer.visualize_dimensional_reduction(
            clustering_result=clustering_result,
            output_dir=str(viz_dir),
            prefix=f"{combination_key}_",
            encoding_method=encoding_method,
            create_pca=create_pca,
            create_tsne=create_tsne,
            show_query=True,
            max_clusters_display=10
        )
        
        logging.info(f"Created dimensional reduction visualizations for {combination_key}")
        
    except Exception as e:
        logging.warning(f"Failed to create dimensional reduction visualizations for {combination_key}: {e}")


def _create_cluster_specific_visualizations(
    msa: MSA,
    clustering_result,
    viz_dir: Path,
    combination_key: str,
    viz_config: Dict[str, Any]
) -> None:
    """
    Create individual visualizations for each cluster.
    
    Parameters
    ----------
    msa : MSA
        Original MSA object
    clustering_result : ClusteringResult
        Clustering result object
    viz_dir : Path
        Visualization output directory
    combination_key : str
        Combination identifier
    viz_config : Dict[str, Any]
        Visualization configuration
    """
    if MSAVisualizer is None or clustering_result.n_clusters == 0:
        return
    
    try:
        # Create subdirectory for cluster-specific visualizations
        cluster_viz_dir = viz_dir / "clusters"
        cluster_viz_dir.mkdir(exist_ok=True)
        
        # Generate visualizations for each cluster
        for cluster_id in range(clustering_result.n_clusters):
            try:
                cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
                cluster_descriptions = clustering_result.get_cluster_descriptions(cluster_id)
                
                if len(cluster_sequences) < 2:  # Skip clusters with too few sequences
                    continue
                
                # Create MSA object for this cluster
                cluster_msa = MSA(
                    query_sequence=msa.query_sequence,
                    chain_poly_type=msa.chain_poly_type,
                    sequences=[msa.query_sequence] + cluster_sequences,  # Include query
                    descriptions=['Query'] + cluster_descriptions,
                    deduplicated=False
                )
                
                # Create visualizer for this cluster
                cluster_visualizer = MSAVisualizer(cluster_msa)
                
                # Generate cluster-specific visualizations
                cluster_prefix = f"{combination_key}_cluster_{cluster_id:03d}_"
                
                # Conservation for this cluster
                conservation_path = cluster_viz_dir / f"{cluster_prefix}conservation.pdf"
                cluster_visualizer.visualize_conservation(
                    output_path=str(conservation_path),
                    title=f"Cluster {cluster_id} Conservation ({len(cluster_sequences)} sequences)"
                )
                
                # Similarity for this cluster
                similarity_path = cluster_viz_dir / f"{cluster_prefix}similarity.pdf"
                cluster_visualizer.visualize_similarity(
                    output_path=str(similarity_path),
                    method=viz_config.get('similarity_method', 'identity'),
                    cluster_sequences=False,  # Don't cluster within cluster
                    title=f"Cluster {cluster_id} Sequence Similarity"
                )
                
            except Exception as e:
                logging.warning(f"Failed to create visualizations for cluster {cluster_id}: {e}")
        
        logging.info(f"Created cluster-specific visualizations for {combination_key}")
        
    except Exception as e:
        logging.warning(f"Failed to create cluster-specific visualizations for {combination_key}: {e}")


def _plot_cluster_size_distribution(
    clustering_result,
    viz_dir: Path,
    combination_key: str
) -> None:
    """
    Plot cluster size distribution.
    
    Parameters
    ----------
    clustering_result : ClusteringResult
        Clustering result object
    viz_dir : Path
        Output directory
    combination_key : str
        Combination identifier for file naming
    """
    try:
        # Calculate cluster sizes
        cluster_sizes = []
        for cluster_id in range(clustering_result.n_clusters):
            cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
            cluster_sizes.append(len(cluster_sequences))
        
        if not cluster_sizes:
            return
        
        # Create plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Histogram of cluster sizes
        ax1.hist(cluster_sizes, bins=min(20, len(cluster_sizes)), alpha=0.7, edgecolor='black')
        ax1.set_xlabel('Cluster Size (number of sequences)')
        ax1.set_ylabel('Number of Clusters')
        ax1.set_title(f'Cluster Size Distribution - {combination_key}')
        ax1.grid(True, alpha=0.3)
        
        # Box plot of cluster sizes
        ax2.boxplot(cluster_sizes)
        ax2.set_ylabel('Cluster Size (number of sequences)')
        ax2.set_title('Cluster Size Statistics')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics text
        stats_text = f'Total clusters: {len(cluster_sizes)}\n'
        stats_text += f'Mean size: {np.mean(cluster_sizes):.1f}\n'
        stats_text += f'Median size: {np.median(cluster_sizes):.1f}\n'
        stats_text += f'Max size: {np.max(cluster_sizes)}\n'
        stats_text += f'Min size: {np.min(cluster_sizes)}'
        
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        # Save plot
        output_path = viz_dir / f"{combination_key}_cluster_size_distribution.pdf"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"Created cluster size distribution plot: {output_path}")
        
    except Exception as e:
        logging.warning(f"Failed to create cluster size distribution plot: {e}")


def _plot_clustering_summary(
    clustering_result,
    viz_dir: Path,
    combination_key: str
) -> None:
    """
    Plot clustering summary information.
    
    Parameters
    ----------
    clustering_result : ClusteringResult
        Clustering result object
    viz_dir : Path
        Output directory
    combination_key : str
        Combination identifier for file naming
    """
    try:
        # Prepare data
        total_sequences = len(clustering_result.sequences)
        clustered_sequences = total_sequences - clustering_result.n_noise
        
        # Create summary pie chart
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Pie chart: Clustered vs Noise
        labels = ['Clustered', 'Noise']
        sizes = [clustered_sequences, clustering_result.n_noise]
        colors = ['lightblue', 'lightcoral']
        
        wedges, texts, autotexts = ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                          startangle=90, explode=(0.05, 0))
        ax1.set_title(f'Clustering Overview - {combination_key}')
        
        # Bar chart: Summary statistics
        categories = ['Total\nSequences', 'Clusters', 'Noise\nPoints', 'Largest\nCluster']
        
        # Calculate largest cluster size
        largest_cluster_size = 0
        if clustering_result.n_clusters > 0:
            cluster_sizes = []
            for cluster_id in range(clustering_result.n_clusters):
                cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
                cluster_sizes.append(len(cluster_sequences))
            largest_cluster_size = max(cluster_sizes) if cluster_sizes else 0
        
        values = [total_sequences, clustering_result.n_clusters, 
                 clustering_result.n_noise, largest_cluster_size]
        
        bars = ax2.bar(categories, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
        ax2.set_title('Clustering Statistics')
        ax2.set_ylabel('Count')
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01*max(values),
                    f'{value}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # Save plot
        output_path = viz_dir / f"{combination_key}_clustering_summary.pdf"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"Created clustering summary plot: {output_path}")
        
    except Exception as e:
        logging.warning(f"Failed to create clustering summary plot: {e}")


def _plot_cluster_comparison(
    clustering_result,
    viz_dir: Path,
    combination_key: str
) -> None:
    """
    Plot comparison between clusters.
    
    Parameters
    ----------
    clustering_result : ClusteringResult
        Clustering result object
    viz_dir : Path
        Output directory  
    combination_key : str
        Combination identifier for file naming
    """
    try:
        # Collect cluster information
        cluster_data = []
        for cluster_id in range(clustering_result.n_clusters):
            cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
            cluster_data.append({
                'cluster_id': cluster_id,
                'size': len(cluster_sequences),
                'avg_length': np.mean([len(seq) for seq in cluster_sequences]) if cluster_sequences else 0
            })
        
        if len(cluster_data) < 2:
            return
        
        # Create DataFrame for easier plotting
        df = pd.DataFrame(cluster_data)
        
        # Create comparison plots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Cluster size comparison
        bars1 = ax1.bar(df['cluster_id'], df['size'], color='lightblue', alpha=0.7)
        ax1.set_xlabel('Cluster ID')
        ax1.set_ylabel('Number of Sequences')
        ax1.set_title('Cluster Size Comparison')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars1, df['size']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01*df['size'].max(),
                    f'{value}', ha='center', va='bottom')
        
        # Average sequence length comparison
        bars2 = ax2.bar(df['cluster_id'], df['avg_length'], color='lightgreen', alpha=0.7)
        ax2.set_xlabel('Cluster ID')
        ax2.set_ylabel('Average Sequence Length')
        ax2.set_title('Average Sequence Length by Cluster')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars2, df['avg_length']):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01*df['avg_length'].max(),
                    f'{value:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # Save plot
        output_path = viz_dir / f"{combination_key}_cluster_comparison.pdf"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"Created cluster comparison plot: {output_path}")
        
    except Exception as e:
        logging.warning(f"Failed to create cluster comparison plot: {e}")


def _create_text_summary_fallback(
    result_data: Dict[str, Any],
    viz_dir: Path,
    combination_key: str
) -> None:
    """Create a simple text summary when visualization fails."""
    clustering_result = result_data['result']
    
    summary_content = [
        f"Clustering Summary for {combination_key}",
        "=" * 50,
        f"Algorithm: {clustering_result.algorithm_name}",
        f"Number of clusters: {clustering_result.n_clusters}",
        f"Number of noise points: {clustering_result.n_noise}",
        f"Total sequences: {len(clustering_result.sequences)}",
        f"Noise percentage: {clustering_result.n_noise / len(clustering_result.sequences) * 100:.1f}%",
        f"Processing time: {result_data['elapsed_time']:.2f} seconds",
        "",
        "Optimized Parameters:",
        "-" * 20
    ]
    
    for param, value in result_data['optimized_params'].items():
        summary_content.append(f"{param}: {value}")
    
    summary_file = viz_dir / f"{combination_key}_summary.txt"
    with open(summary_file, 'w') as f:
        f.write('\n'.join(summary_content))


def create_comprehensive_comparison_visualization(
    results: Dict[str, Dict[str, Any]],
    config: Dict[str, Any]
) -> None:
    """
    Create comprehensive comparison visualization across all clustering methods.
    
    Parameters
    ----------
    results : Dict[str, Dict[str, Any]]
        All clustering results
    config : Dict[str, Any]
        Configuration dictionary
    """
    output_base_dir = Path(config.get('general', {}).get('output_base_dir', './clustering_results'))
    
    try:
        # Prepare comparison data
        comparison_data = []
        for combination_key, result_data in results.items():
            clustering_result = result_data['result']
            comparison_data.append({
                'method': combination_key,
                'algorithm': clustering_result.algorithm_name,
                'encoding': result_data['encoding_method'],
                'n_clusters': clustering_result.n_clusters,
                'n_noise': clustering_result.n_noise,
                'total_sequences': len(clustering_result.sequences),
                'noise_percentage': clustering_result.n_noise / len(clustering_result.sequences) * 100,
                'elapsed_time': result_data['elapsed_time']
            })
        
        if len(comparison_data) < 2:
            logging.info("Not enough methods to create comparison visualization")
            return
        
        df = pd.DataFrame(comparison_data)
        
        # Create comprehensive comparison plot
        fig = plt.figure(figsize=(16, 12))
        
        # 1. Number of clusters comparison
        ax1 = plt.subplot(2, 3, 1)
        bars1 = ax1.bar(range(len(df)), df['n_clusters'], color='lightblue', alpha=0.8)
        ax1.set_xlabel('Method')
        ax1.set_ylabel('Number of Clusters')
        ax1.set_title('Clusters Found by Each Method')
        ax1.set_xticks(range(len(df)))
        ax1.set_xticklabels(df['method'], rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars1, df['n_clusters']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01*df['n_clusters'].max(),
                    f'{value}', ha='center', va='bottom')
        
        # 2. Noise percentage comparison
        ax2 = plt.subplot(2, 3, 2)
        bars2 = ax2.bar(range(len(df)), df['noise_percentage'], color='lightcoral', alpha=0.8)
        ax2.set_xlabel('Method')
        ax2.set_ylabel('Noise Percentage (%)')
        ax2.set_title('Noise Points by Each Method')
        ax2.set_xticks(range(len(df)))
        ax2.set_xticklabels(df['method'], rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars2, df['noise_percentage']):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01*df['noise_percentage'].max(),
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 3. Processing time comparison
        ax3 = plt.subplot(2, 3, 3)
        bars3 = ax3.bar(range(len(df)), df['elapsed_time'], color='lightgreen', alpha=0.8)
        ax3.set_xlabel('Method')
        ax3.set_ylabel('Processing Time (seconds)')
        ax3.set_title('Processing Time by Each Method')
        ax3.set_xticks(range(len(df)))
        ax3.set_xticklabels(df['method'], rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars3, df['elapsed_time']):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01*df['elapsed_time'].max(),
                    f'{value:.1f}s', ha='center', va='bottom')
        
        # 4. Clusters vs Noise scatter plot
        ax4 = plt.subplot(2, 3, 4)
        scatter = ax4.scatter(df['n_clusters'], df['noise_percentage'], 
                             c=df['elapsed_time'], cmap='viridis', s=100, alpha=0.7)
        ax4.set_xlabel('Number of Clusters')
        ax4.set_ylabel('Noise Percentage (%)')
        ax4.set_title('Clusters vs Noise (color = processing time)')
        ax4.grid(True, alpha=0.3)
        
        # Add method labels to points
        for i, method in enumerate(df['method']):
            ax4.annotate(method, (df['n_clusters'].iloc[i], df['noise_percentage'].iloc[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        # Add colorbar
        plt.colorbar(scatter, ax=ax4, label='Processing Time (s)')
        
        # 5. Performance summary table
        ax5 = plt.subplot(2, 3, 5)
        ax5.axis('tight')
        ax5.axis('off')
        
        # Create summary table
        table_data = []
        for _, row in df.iterrows():
            table_data.append([
                row['method'],
                f"{row['n_clusters']}",
                f"{row['noise_percentage']:.1f}%",
                f"{row['elapsed_time']:.1f}s"
            ])
        
        table = ax5.table(cellText=table_data,
                         colLabels=['Method', 'Clusters', 'Noise %', 'Time'],
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(9)
        table.scale(1.2, 1.5)
        ax5.set_title('Performance Summary')
        
        # 6. Algorithm distribution pie chart
        ax6 = plt.subplot(2, 3, 6)
        algorithm_counts = df['algorithm'].value_counts()
        ax6.pie(algorithm_counts.values, labels=algorithm_counts.index, autopct='%1.1f%%')
        ax6.set_title('Algorithm Distribution')
        
        plt.tight_layout()
        
        # Save comprehensive comparison
        output_path = output_base_dir / "comprehensive_method_comparison.pdf"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"Created comprehensive comparison visualization: {output_path}")
        
    except Exception as e:
        logging.warning(f"Failed to create comprehensive comparison visualization: {e}")


# ==========================================
# Reporting Functions
# ==========================================

def create_comprehensive_report(
    results: Dict[str, Dict[str, Any]],
    config: Dict[str, Any],
    input_file_path: str,
    msa: MSA
) -> None:
    """
    Generate comprehensive summary report comparing all methods.
    
    Parameters
    ----------
    results : Dict[str, Dict[str, Any]]
        All clustering results
    config : Dict[str, Any]
        Configuration dictionary
    input_file_path : str
        Path to input file
    msa : MSA
        Original MSA object
    """
    if not config.get('output_settings', {}).get('create_summary_report', True):
        logging.info("Summary report creation disabled in configuration")
        return
        
    logging.info("Generating comprehensive summary report")
    
    output_base_dir = Path(config.get('general', {}).get('output_base_dir', './clustering_results'))
    
    # Compile comprehensive summary data
    summary_data = {
        'input_file': str(input_file_path),
        'msa_info': {
            'n_sequences': msa.depth,
            'avg_sequence_length': sum(len(s) for s in msa.sequences) / len(msa.sequences),
            'chain_poly_type': msa.chain_poly_type
        },
        'methods_tested': {
            'encoding_methods': config.get('encoding_methods', {}).get('default', []),
            'clustering_methods': config.get('clustering_methods', {}).get('default', [])
        },
        'results_summary': {}
    }
    
    # Process each result
    for combination_key, result_data in results.items():
        clustering_result = result_data['result']
        summary_data['results_summary'][combination_key] = {
            'algorithm': clustering_result.algorithm_name,
            'encoding_method': result_data['encoding_method'],
            'clustering_method': result_data['clustering_method'],
            'n_clusters': clustering_result.n_clusters,
            'n_noise': clustering_result.n_noise,
            'noise_percentage': clustering_result.n_noise / len(clustering_result.sequences) * 100,
            'elapsed_time': result_data['elapsed_time'],
            'optimized_params': result_data['optimized_params'],
            'output_directory': str(result_data['output_dir'])
        }
    
    # Save JSON summary
    summary_file = output_base_dir / "comprehensive_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(summary_data, f, indent=2, default=str)
    
    # Generate human-readable text summary
    _generate_text_summary_report(summary_data, output_base_dir)
    
    logging.info(f"Comprehensive summary saved to {summary_file}")


def _generate_text_summary_report(summary_data: Dict[str, Any], output_dir: Path) -> None:
    """Generate human-readable text summary report."""
    summary_file = output_dir / "summary_report.txt"
    
    with open(summary_file, 'w') as f:
        f.write("MSA Clustering Pipeline - Summary Report\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Input File: {summary_data['input_file']}\n")
        f.write(f"Number of Sequences: {summary_data['msa_info']['n_sequences']}\n")
        f.write(f"Average Sequence Length: {summary_data['msa_info']['avg_sequence_length']:.1f}\n")
        f.write(f"Chain Type: {summary_data['msa_info']['chain_poly_type']}\n\n")
        
        f.write("Methods Tested:\n")
        f.write(f"  Encoding: {', '.join(summary_data['methods_tested']['encoding_methods'])}\n")
        f.write(f"  Clustering: {', '.join(summary_data['methods_tested']['clustering_methods'])}\n\n")
        
        f.write("Results Summary:\n")
        f.write("-" * 30 + "\n")
        
        for combination, results in summary_data['results_summary'].items():
            f.write(f"\n{combination.upper()}:\n")
            f.write(f"  Algorithm: {results['algorithm']}\n")
            f.write(f"  Clusters: {results['n_clusters']}\n")
            f.write(f"  Noise Points: {results['n_noise']} ({results['noise_percentage']:.1f}%)\n")
            f.write(f"  Runtime: {results['elapsed_time']:.2f} seconds\n")
            f.write(f"  Output: {results['output_directory']}\n")


# ==========================================
# Main Pipeline Function
# ==========================================

def run_clustering_pipeline(
    input_file: str,
    config_file: str = "clustering_config.yaml"
) -> Dict[str, Dict[str, Any]]:
    """
    Execute the complete MSA clustering pipeline.
    
    This is the main orchestrator function that coordinates all pipeline steps
    in a clear, sequential manner.
    
    Parameters
    ----------
    input_file : str
        Path to input A3M file
    config_file : str
        Path to configuration YAML file
        
    Returns
    -------
    Dict[str, Dict[str, Any]]
        Complete results from all clustering combinations
        
    Raises
    ------
    Exception
        If any critical pipeline step fails
    """
    try:
        # Step 1: Load configuration and setup logging
        config = load_clustering_config(config_file)
        setup_logging_from_config(config)
        
        logging.info(f"[INFO ] Start complete MSA clustering pipeline")
        logging.info(f"  INFO: input file: {input_file}")
        logging.info(f"  INFO: configuration: {config_file}")
        
        # Step 2: Load MSA from file
        msa = load_msa_from_file(input_file, config)
        
        # Step 3: Execute clustering for all combinations
        results = execute_clustering_combinations(msa, config, input_file)
        
        if not results:
            raise RuntimeError("No clustering results were generated")
        
        # Step 4: Save clustering outputs - pass the input_file path
        save_cluster_outputs(msa, results, config, input_file)
        
        # Step 5: Create visualizations
        create_analysis_visualizations(msa, results, config)
        
        # Step 6: Create comprehensive comparison visualization
        create_comprehensive_comparison_visualization(results, config)
        
        # Step 7: Generate comprehensive report
        create_comprehensive_report(results, config, input_file, msa)
        
        output_dir = config.get('general', {}).get('output_base_dir', './clustering_results')
        logging.info("Complete pipeline finished successfully!")
        logging.info(f"Results saved to: {output_dir}")
        
        return results
        
    except Exception as e:
        logging.error(f"Pipeline execution failed: {e}")
        raise


# ==========================================
# Command Line Interface
# ==========================================

def main():
    """Main function for command line interface."""
    parser = argparse.ArgumentParser(
        description="MSA Clustering Pipeline - Function-based Implementation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Basic usage with default configuration
    python msa_clustering_pipeline.py KaiB_101.a3m

    # With custom configuration file
    python msa_clustering_pipeline.py KaiB_101.a3m --config my_config.yaml

    # With verbose output
    python msa_clustering_pipeline.py KaiB_101.a3m --verbose
        """
    )
    parser.add_argument('input_file', help='Path to input A3M file'
    )
    parser.add_argument('--config', default='clustering_config.yaml',
        help='Path to configuration YAML file (default: clustering_config.yaml)'
    )
    parser.add_argument('--verbose', action='store_true', 
        help='Enable verbose logging'
    )
    args = parser.parse_args()
    
    try:
        # Override verbose setting if specified
        if args.verbose:
            config = load_clustering_config(args.config)
            config.setdefault('logging', {})['level'] = 'DEBUG'
            setup_logging_from_config(config)
        
        print(f'[INFO ] Run the pipeline')
        results = run_clustering_pipeline(args.input_file, args.config)
        
        # Print summary to console
        print(f"[INFO ] Pipeline completed successfully!")
        print(f"[INFO ] Generated {len(results)} clustering result combinations")
        for combination_key, result_data in results.items():
            clustering_result = result_data['result']
            print(f"  {combination_key}: {clustering_result.n_clusters} clusters, "
                  f"{clustering_result.n_noise} noise points")
        
    except Exception as e:
        logging.error(f"Pipeline execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 
