import os
import numpy as np

from typing import List, Tuple, Dict, Any, Optional, Union
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd

from sklearn.metrics.pairwise import cosine_similarity

from sklearn.preprocessing import normalize
from sklearn.cluster import AgglomerativeClustering, KMeans, DBSCAN
from sklearn.manifold import TSNE, MDS
from scipy.spatial.distance import pdist, squareform
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize

import Bio.PDB
from Bio.PDB import PDBParser

from utils.os_utils import read_input, get_coords_by_chains


def load_model_coordinates(
    pdb_files: list,  # List of PDB files to process
    e1_start: int,  # Start index for E1 domain
    e1_end: int,  # End index for E1 domain
    e2_start: int,  # Start index for E2 domain
    e2_end: int  # End index for E2 domain
) -> np.ndarray:
    """
    Load and prepare model coordinates from PDB files.
    
    Parameters
    ----------
    pdb_files (list):
        List of PDB file paths to load.
    e1_start (int):
        Start index for E1 domain.
    e1_end (int):
        End index for E1 domain.
    e2_start (int):
        Start index for E2 domain.
    e2_end (int):
        End index for E2 domain.
        
    Returns
    -------
    np.ndarray:
        Array of flattened coordinates of shape (n_models, n_features).
    """    
    # Initialize PDB parser
    parser = PDBParser(QUIET=True)
    
    # Prepare array to hold coordinates
    n_models = len(pdb_files)
    
    # Extract coordinates for all models
    coords_list = []
    
    for pdb_file in pdb_files:
        try:
            # Load PDB structure
            structure = parser.get_structure('model', pdb_file)
            model = structure[0]  # First model
            
            # Extract CA atoms for E1 and E2 domains
            e1_coords = []
            e2_coords = []
            
            for residue in model.get_residues():
                res_id = residue.get_id()[1]  # Residue number
                if ('CA' in residue) and (e1_start <= res_id <= e1_end or e2_start <= res_id <= e2_end):
                    ca_atom = residue['CA']
                    coords = ca_atom.get_coord()  # Get 3D coordinates
                    
                    if e1_start <= res_id <= e1_end:
                        e1_coords.append(coords)
                    elif e2_start <= res_id <= e2_end:
                        e2_coords.append(coords)
            
            # Combine E1 and E2 domains
            model_coords = np.vstack(e1_coords + e2_coords)
            coords_list.append(model_coords.flatten())
            
        except Exception as e:
            print(f"[WARNING] Error processing {pdb_file}: {e}")
            # Add empty coordinates to maintain order
            coords_list.append(np.zeros(3 * ((e1_end - e1_start + 1) + (e2_end - e2_start + 1))))
    
    # Stack all model coordinates
    reshaped_coords = np.vstack(coords_list)
    return reshaped_coords

def calculate_cosine_similarity_matrix(
    models_data: np.ndarray # flattened coordinates, [n_models, n_features]
) -> np.ndarray:
    """
    Calculate cosine similarity between all pairs of models.
    
    Parameters
    ----------
    models_data (np.ndarray):
        Array of shape (n_models, n_features) containing the flattened coordinates.
        
    Returns
    -------
    similarity_matrix (np.ndarray):    
        Similarity matrix of shape (n_models, n_models).
    """
    # Calculate cosine similarity matrix
    similarity_matrix = cosine_similarity(models_data)
    return similarity_matrix

def plot_similarity_heatmap(
    similarity_matrix: np.ndarray,  # Similarity matrix between models, [n_models, n_models]
    model_labels: list=None,  # Labels for the models
    output_file: str="model_cosine_similarity.pdf"  # Path to save the output figure
) -> bool: # 
    """
    Plot a heatmap of the similarity matrix.
    
    Parameters
    ----------
    similarity_matrix (np.ndarray):
        Similarity matrix of shape (n_models, n_models).
    model_labels (list, optional):
        Labels for the models. If None, uses indices.
    output_file (str, optional):
        Path to save the output figure.
        
    Returns
    -------
    bool:
        True if the heatmap was successfully created and saved, False otherwise.
    """
    try:
        plt.figure(figsize=(12, 10))
        
        # Create a mask for the upper triangle to show only lower triangle
        mask = np.triu(np.ones_like(similarity_matrix, dtype=bool))
        
        # If model labels are not provided, use indices
        if model_labels is None:
            model_labels = [f"Model {i+1}" for i in range(similarity_matrix.shape[0])]
        
        # Plot the heatmap
        similarity_df = pd.DataFrame(
            similarity_matrix, index=model_labels, columns=model_labels
        )
        sns.heatmap(similarity_df, annot=False, cmap="viridis", mask=mask,
                    vmin=0, vmax=1, fmt=".2f", linewidths=0.5,
                    cbar_kws={"label": "Cosine Similarity"}
        )
        plt.title("Cosine Similarity Between AF3 Models", fontsize=16)
        plt.tight_layout()
        
        # Ensure output directory exists
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # Save the plot
        plt.savefig(output_file, dpi=300, bbox_inches="tight")
        plt.close()
        print(f'  INFO: saved similarity heatmap to {output_file}')
        
        return True
    except Exception as e:
        print(f"[WARNING] Error creating heatmap: {e}")
        return False

def find_most_similar_pairs(
    similarity_matrix: np.ndarray, # Similarity matrix between models, [n_models, n_models]
    model_labels: List[str]=None,  # Labels for the models
    top_n: int=5                   # Number of top similar pairs to return
) -> List[Tuple[str, str, float]]: # (model1, model2, similarity)
    """
    Find the most similar pairs of models based on cosine similarity.
    
    Parameters
    ----------
    similarity_matrix (np.ndarray):
        Similarity matrix of shape (n_models, n_models).
    model_labels (list, optional):
        Labels for the models. If None, uses indices.
    top_n (int, optional):
        Number of top similar pairs to return.
        
    Returns
    -------
    list:
        List of tuples (model1, model2, similarity) for the top_n most similar pairs.
    """
    # If model labels are not provided, use indices
    if model_labels is None:
        model_labels = [f"Model {i+1}" for i in range(similarity_matrix.shape[0])]
    
    # Create a list of all model pairs and their similarities
    n_models = similarity_matrix.shape[0]
    pairs = []
    
    # Only consider the lower triangle of the matrix (excluding diagonal)
    for i in range(1, n_models):
        for j in range(i):
            pairs.append((model_labels[i], model_labels[j], similarity_matrix[i, j]))
    
    # Sort by similarity in descending order
    pairs.sort(key=lambda x: x[2], reverse=True)
    
    # Return top_n pairs
    return pairs[:top_n]

def calculate_average_similarity(
    similarity_matrix: np.ndarray # Similarity matrix between models, [n_models, n_models]
) -> np.ndarray: # average similarities for each model, [n_models]
    """
    Calculate the average similarity of each model to all other models.
    
    Parameters
    ----------
    similarity_matrix (np.ndarray):
        Similarity matrix of shape (n_models, n_models).
        
    Returns
    -------
    avg_similarities (np.ndarray):
        Array of average similarities for each model, [n_models]
    """
    n_models = similarity_matrix.shape[0]
    avg_similarities = np.zeros(n_models)
    
    for i in range(n_models):
        # Exclude the diagonal (self-similarity)
        others = np.concatenate([similarity_matrix[i, :i], similarity_matrix[i, i+1:]])
        avg_similarities[i] = np.mean(others)
    
    return avg_similarities


## --- Representative structure selection --- ##
def _find_central_structure_in_cluster(
    cluster_indices: List[int],  # Indices of structures in the cluster
    distance_matrix: np.ndarray  # Distance matrix between structures
) -> int: # Index of the most central structure in the cluster
    """
    Find the most central structure within a cluster based on minimum average distance.
    
    Parameters
    ----------
    cluster_indices (List[int]):
        Indices of structures that belong to the cluster.
    distance_matrix (np.ndarray):
        Distance matrix between structures.
        
    Returns
    -------
    int:
        Index of the most central structure in the cluster.
    """
    # Handle singleton clusters
    if len(cluster_indices) == 1:
        return cluster_indices[0]
    
    # Calculate average distance within the cluster for each structure
    cluster_distances = np.zeros(len(cluster_indices))
    for j, idx in enumerate(cluster_indices):
        # Calculate distances to other structures in the same cluster
        other_indices = [other for other in cluster_indices if other != idx]
        if other_indices:  # Ensure other structures exist
            cluster_distances[j] = np.mean([distance_matrix[idx, other] for other in other_indices])
        else:
            # Safety check (should never occur since we handled singleton clusters)
            cluster_distances[j] = 0
    
    # Select the most central structure (minimum average distance)
    central_idx = cluster_indices[np.argmin(cluster_distances)]
    return central_idx

def _select_diverse_structures(
    distance_matrix: np.ndarray,     # Distance matrix between structures
    n_representatives: int,          # Number of structures to select
    existing_indices: List[int]=None  # Already selected indices (optional)
) -> List[int]: # Indices of selected diverse structures
    """
    Select diverse structures using MaxMin algorithm.
    
    Parameters
    ----------
    distance_matrix (np.ndarray):
        Distance matrix between structures.
    n_representatives (int):
        Number of structures to select.
    existing_indices (List[int], optional):
        Indices of already selected structures.
        
    Returns
    -------
    List[int]:
        Indices of selected diverse structures.
    """
    n_models = distance_matrix.shape[0]
    
    # Initialize with already selected structures or most central structure
    if existing_indices and len(existing_indices) > 0:
        representatives = existing_indices.copy()
        selected = set(representatives)
    else:
        # Start with the most central structure
        avg_distances = np.mean(distance_matrix, axis=1)
        representatives = [np.argmin(avg_distances)]
        selected = set(representatives)
    
    # Select remaining structures to maximize diversity
    while len(representatives) < n_representatives:
        # Find structure with maximum minimum distance to existing representatives
        max_min_dist = -1
        max_idx = -1
        
        for i in range(n_models):
            if i not in selected:
                min_dist = min(distance_matrix[i, j] for j in selected)
                if min_dist > max_min_dist:
                    max_min_dist = min_dist
                    max_idx = i
        
        if max_idx >= 0:
            representatives.append(max_idx)
            selected.add(max_idx)
        else:
            break
    
    return representatives

def _process_cluster_labels(
    cluster_labels: np.ndarray,       # Cluster assignments for each structure
    n_clusters: int,                  # Number of clusters
    distance_matrix: np.ndarray,      # Distance matrix between structures
    normalized_coords: np.ndarray=None, # Optional normalized coordinates for kmeans
    cluster_centers: np.ndarray=None  # Optional cluster centers for kmeans
) -> Tuple[List[int], np.ndarray]: # (representatives, cluster_labels)
    """
    Process cluster labels to select one representative structure per cluster.
    
    Parameters
    ----------
    cluster_labels (np.ndarray):
        Array of cluster assignments for each structure.
    n_clusters (int):
        Number of clusters.
    distance_matrix (np.ndarray):
        Distance matrix between structures.
    normalized_coords (np.ndarray, optional):
        Normalized coordinates for kmeans method.
    cluster_centers (np.ndarray, optional):
        Cluster centers for kmeans method.
        
    Returns
    -------
    Tuple[List[int], np.ndarray]:
        A tuple containing:
        - List of indices of selected representative structures
        - Array of cluster assignments for each structure
    """
    representatives = []
    
    for i in range(n_clusters):
        cluster_indices = np.where(cluster_labels == i)[0]
        if len(cluster_indices) == 0:
            print(f"[WARNING] No structures in cluster {i}")
            continue
            
        # For kmeans with provided coordinates and centers, use distance to center
        if normalized_coords is not None and cluster_centers is not None:
            # Find the structure closest to the cluster center
            center = cluster_centers[i]
            distances_to_center = [np.linalg.norm(normalized_coords[idx] - center) for idx in cluster_indices]
            central_idx = cluster_indices[np.argmin(distances_to_center)]
        else:
            # For other methods, find the most central structure based on distance matrix
            central_idx = _find_central_structure_in_cluster(cluster_indices, distance_matrix)
            
        representatives.append(central_idx)
        
    return representatives, cluster_labels

def _process_dbscan_clusters(
    cluster_labels: np.ndarray,     # DBSCAN cluster labels
    distance_matrix: np.ndarray,    # Distance matrix
    n_representatives: int          # Desired number of representatives
) -> Tuple[List[int], np.ndarray]:  # (representatives, cluster_labels)
    """
    Process DBSCAN clustering results to select representative structures.
    
    Parameters
    ----------
    cluster_labels (np.ndarray):
        Cluster assignments from DBSCAN.
    distance_matrix (np.ndarray):
        Distance matrix between structures.
    n_representatives (int):
        Desired number of representative structures.
        
    Returns
    -------
    Tuple[List[int], np.ndarray]:
        A tuple containing:
        - List of indices of selected representative structures
        - Array of cluster assignments for each structure
    """
    # Select clusters, handling noise points (-1)
    unique_clusters = np.unique(cluster_labels)
    if -1 in unique_clusters:  # -1 indicates noise points
        unique_clusters = unique_clusters[unique_clusters != -1]
    
    # Handle case with too many or too few clusters
    if len(unique_clusters) > n_representatives:
        print('[INFO ] DBSCAN: Too many clusters, selecting the largest ones')
        cluster_sizes = [np.sum(cluster_labels == c) for c in unique_clusters]
        selected_clusters = unique_clusters[np.argsort(cluster_sizes)[-n_representatives:]]
    else:
        print(f'[INFO ] DBSCAN: Using all {len(unique_clusters)} clusters')
        selected_clusters = unique_clusters
    
    # Get representatives from selected clusters
    representatives = []
    for cluster in selected_clusters:
        cluster_indices = np.where(cluster_labels == cluster)[0]
        print(f'  INFO: {len(cluster_indices)} structures in cluster {cluster}')
        if len(cluster_indices) == 0:
            continue
        
        central_idx = _find_central_structure_in_cluster(cluster_indices, distance_matrix)
        representatives.append(central_idx)
    
    # If not enough representatives, add more using diversity-based approach
    if len(representatives) < n_representatives:
        print(f'[INFO ] DBSCAN: Only {len(representatives)} clusters found, adding diverse structures')
        representatives = _select_diverse_structures(
            distance_matrix, n_representatives, representatives
        )
    
    # Generate cluster labels manually instead of calling generate_cluster_labels
    n_structures = distance_matrix.shape[0]
    new_cluster_labels = np.zeros(n_structures, dtype=int)
    
    # Assign each structure to the closest representative
    for i in range(n_structures):
        if i in representatives:
            # If the structure is a representative, assign it to its own cluster
            new_cluster_labels[i] = representatives.index(i)
        else:
            # Find the closest representative
            distances = [distance_matrix[i, rep] for rep in representatives]
            closest_rep_idx = np.argmin(distances)
            new_cluster_labels[i] = closest_rep_idx
    
    return representatives, new_cluster_labels

def _select_diverse_structures_with_labels(
    distance_matrix: np.ndarray,     # Distance matrix between structures
    n_representatives: int,          # Number of structures to select
    existing_indices: List[int]=None  # Already selected indices (optional)
) -> Tuple[List[int], np.ndarray]:  # (representatives, cluster_labels)
    """
    Select diverse structures using MaxMin algorithm and generate cluster labels.
    
    Parameters
    ----------
    distance_matrix (np.ndarray):
        Distance matrix between structures.
    n_representatives (int):
        Number of structures to select.
    existing_indices (List[int], optional):
        Indices of already selected structures.
        
    Returns
    -------
    Tuple[List[int], np.ndarray]:
        A tuple containing:
        - List of indices of selected representative structures
        - Array of cluster assignments for each structure
    """
    representatives = _select_diverse_structures(distance_matrix, n_representatives, existing_indices)
    
    # Generate cluster labels manually
    n_structures = distance_matrix.shape[0]
    cluster_labels = np.zeros(n_structures, dtype=int)
    
    # Assign each structure to the closest representative
    for i in range(n_structures):
        if i in representatives:
            # If the structure is a representative, assign it to its own cluster
            cluster_labels[i] = representatives.index(i)
        else:
            # Find the closest representative
            distances = [distance_matrix[i, rep] for rep in representatives]
            closest_rep_idx = np.argmin(distances)
            cluster_labels[i] = closest_rep_idx
    
    return representatives, cluster_labels

def select_representative_structures(
    similarity_matrix: np.ndarray, # Cosine similarity matrix, [n_models, n_models]
    model_labels: List[str],       # List of model names
    n_representatives: int = 5,    # Number of representative structures to select
    method: str = 'hierarchical'   # Method for selecting representatives
) -> Tuple[List[int], np.ndarray]: # Indices of selected representative structures and cluster labels
    """
    Select representative structures from protein structure data.
    
    Parameters
    ----------
    similarity_matrix (np.ndarray):
        Cosine similarity matrix of shape (n_models, n_models).
    model_labels (list):
        List of model names or identifiers.
    n_representatives (int, optional):
        Number of representative structures to select.
    method (str, optional):
        Method for selecting representatives:
        - 'hierarchical': Hierarchical clustering
        - 'kmeans': Spherical K-means clustering
        - 'dbscan': Density-based clustering
        - 'maxmin': Maximum minimum distance (diversity-based)
        - 'central': Centrality-based selection (most average structures)
        
    Returns
    -------
    Tuple[List[int], np.ndarray]:
        A tuple containing:
        - List of indices of selected representative structures
        - Array of cluster assignments for each structure
    """
    n_models = similarity_matrix.shape[0]
    
    # Convert similarity matrix to distance matrix (1 - similarity)
    distance_matrix = 1 - similarity_matrix
    
    # Ensure distance matrix has no negative values
    distance_matrix = np.maximum(0, distance_matrix)
    
    # Select method
    if method == 'hierarchical':
        # Perform hierarchical clustering
        clustering = AgglomerativeClustering(
            n_clusters=n_representatives, metric='precomputed',
            linkage='average'  # Options: 'average', 'complete', 'single'
        )
        cluster_labels = clustering.fit_predict(distance_matrix)
        
        # Select representative structure from each cluster
        return _process_cluster_labels(
            cluster_labels, n_representatives, distance_matrix
        )
    elif method == 'kmeans':
        # Use MDS to reduce dimensions from the distance matrix
        embedding = MDS(n_components=3, dissimilarity='precomputed')
        coords = embedding.fit_transform(distance_matrix)
        
        # Normalize coordinates for spherical k-means
        normalized_coords = normalize(coords, axis=1)
        
        # Perform K-means clustering
        kmeans = KMeans(n_clusters=n_representatives, random_state=42)
        cluster_labels = kmeans.fit_predict(normalized_coords)
        
        # Select representative structure from each cluster using distance to center
        return _process_cluster_labels(
            cluster_labels, n_representatives, distance_matrix, normalized_coords, kmeans.cluster_centers_
        )
    elif method == 'dbscan':
        # DBSCAN density-based clustering
        clustering = DBSCAN(eps=0.3, min_samples=3, metric='precomputed')
        cluster_labels = clustering.fit_predict(distance_matrix)
        
        # Process DBSCAN clusters
        return _process_dbscan_clusters(cluster_labels, distance_matrix, n_representatives)

    else:
        raise ValueError(f"Unknown method: {method}")
## ----------------------------------------------------------------------------- ##

def get_validation_top_n_models(
    metric_file: str,  # Path to the metric file
    top_n: int = 10,   # Number of top models to select
    model_labels: list = None  # List of model labels to match with metric file
) -> list:
    """
    Get top N models from pre-sorted metric file.
    
    Parameters
    ----------
    metric_file (str):
        Path to the pre-sorted metric file.
    top_n (int, optional):
        Number of top models to select.
    model_labels (list, optional):
        List of model labels to match with metric file.
        
    Returns
    -------
    list:
        Indices of top N models in the pre-sorted order.
    """
    try:
        # Read metric file
        df = pd.read_csv(metric_file, sep='\t')
        print(f"  INFO: reading metric file with {len(df)} entries")
        
        # Get top N model paths (already sorted)
        top_model_paths = df['Model path'].head(top_n).tolist()
        print(f"  INFO: found {len(top_model_paths)} top model paths")
        
        # Convert paths to model labels format
        top_model_labels = []
        for idx, path in enumerate(top_model_paths):
            # Extract directory name (e.g., 'seed-10000_sample-2')
            dir_name = os.path.basename(os.path.dirname(path))
            if dir_name:
                top_model_labels.append(dir_name)
                print(f"   > extracted label {idx}: {dir_name} from path: {path}")
        
        # Find indices of top models in model_labels
        top_indices = []
        for label in top_model_labels:
            try:
                idx = model_labels.index(label)
                top_indices.append(idx)
                print(f"   > found index {idx} for label {label}")
            except ValueError:
                print(f"[WARNING] model {label} not found in model_labels")
                print(f"  INFO: available labels: {model_labels[:5]}...")  # Print first 5 labels for debugging
        print(f"  INFO: found {len(top_indices)} matching indices")
        return top_indices
        
    except Exception as e:
        print(f"[WARNING] Error reading validation top models: {e}")
        return []

def visualize_selected_structures(
    distance_matrix: np.ndarray,  # Distance matrix between structures
    representatives: list,  # Indices of representative structures
    model_labels: list = None,  # Labels for the models
    top_n: int = 10,  # Number of top similar models to display
    coord_data: np.ndarray = None,  # 3D coordinate data of the models
    cluster_labels: np.ndarray = None,  # Cluster labels for each structure
    metric_file: str = None  # Path to metric file for validation top models
) -> plt.Figure:
    """
    Visualize selected representative structures and validation top models in 2D.
    
    Parameters
    ----------
    distance_matrix (np.ndarray):
        Distance matrix between structures of shape (n_samples, n_samples).
    representatives (list):
        Indices of representative structures selected by clustering algorithms.
    model_labels (list, optional):
        Labels for the models. If None, indices will be used.
    top_n (int, optional):
        Number of top validation models to display.
    coord_data (np.ndarray, optional):
        3D coordinate data of the models with shape (n_samples, n_features).
        If provided, PCA will be performed directly on this data.
    cluster_labels (np.ndarray, optional):
        Cluster labels for each structure. If provided, points will be colored by cluster.
    metric_file (str, optional):
        Path to metric file containing validation ranks.
        
    Returns
    -------
    plt.Figure:
        Matplotlib figure object containing the visualization.
    """
    n_samples = distance_matrix.shape[0]
    
    # Get validation top models if metric file is provided
    validation_top_indices = []
    if metric_file and model_labels:
        validation_top_indices = get_validation_top_n_models(metric_file, top_n, model_labels)
    
    # Perform dimensionality reduction for visualization
    if coord_data is not None and coord_data.shape[0] == n_samples:
        # Apply PCA on 3D coordinates if provided
        pca = PCA(n_components=2)
        embedding = pca.fit_transform(coord_data)
        method = "PCA"
        explained_var = pca.explained_variance_ratio_
        explained_var_text = f"(explained variance: {explained_var[0]:.2f}, {explained_var[1]:.2f})"
    else:
        # Fall back to MDS on distance matrix if coordinates not provided
        distance_matrix_safe = np.maximum(0, distance_matrix)  # Ensure non-negative distances
        mds = MDS(n_components=2, dissimilarity='precomputed', random_state=42)
        embedding = mds.fit_transform(distance_matrix_safe)
        method = "MDS"
        explained_var_text = ""
    
    # Create visualization
    plt.figure(figsize=(10, 8))
    legend_elements = []  # Store legend elements
    
    # If cluster labels are provided, color points by cluster
    if cluster_labels is not None:
        # Create color map for clusters
        cluster_unique = np.unique(cluster_labels)
        n_clusters = len(cluster_unique)
        cmap = plt.cm.get_cmap('tab10', n_clusters)
        cluster_colors = {cluster: cmap(i) for i, cluster in enumerate(cluster_unique)}
        
        # Plot normal points by cluster (excluding representatives and validation top models)
        other_indices = [i for i in range(n_samples) if i not in representatives and i not in validation_top_indices]
        for cluster in cluster_unique:
            # Get indices that belong to this cluster and are not representatives or validation top models
            indices = [i for i in other_indices if cluster_labels[i] == cluster]
            if indices:
                plt.scatter(
                    embedding[indices, 0], embedding[indices, 1], 
                    c=[cluster_colors[cluster]], alpha=0.5, label=f'Cluster {cluster}'
                )
                legend_elements.append(
                    plt.Line2D(
                        [0], [0], marker='o', color='w', 
                        markerfacecolor=cluster_colors[cluster], 
                        label=f'Cluster {cluster}', markersize=8, alpha=0.5
                    )
                )
        
        # Highlight representative structures with solid circles in cluster colors
        for rep in representatives:
            cluster = cluster_labels[rep]
            plt.scatter(embedding[rep, 0], embedding[rep, 1], 
                       c=[cluster_colors[cluster]], s=150, marker='o', edgecolors='black', linewidth=1.5)
        
        legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', 
                                        markerfacecolor=cmap(0), markeredgecolor='black',
                                        label='Representative structures', 
                                        markersize=12, linewidth=1.5))
        
        # Highlight validation top models with star markers and bold text
        if validation_top_indices:
            plt.scatter(embedding[validation_top_indices, 0], embedding[validation_top_indices, 1], 
                       c='black', s=120, marker='*', edgecolors='white', linewidth=1.5,
                       label='Validation top models')
            legend_elements.append(plt.Line2D([0], [0], marker='*', color='w', 
                                            markerfacecolor='black', markeredgecolor='white',
                                            label='Validation top models', 
                                            markersize=12, linewidth=1.5))
        
        # Add labels to representative structures with small text
        for idx in representatives:
            plt.annotate(model_labels[idx], (embedding[idx, 0], embedding[idx, 1]), 
                        fontsize=7, ha='right', va='bottom')
        
        # Add labels to validation top models with bold text
        for idx in validation_top_indices:
                plt.annotate(model_labels[idx], (embedding[idx, 0], embedding[idx, 1]), 
                        fontsize=9, ha='left', va='bottom', weight='bold')
    else:
        # Standard visualization without cluster coloring
        
        # Plot structures that are neither representatives nor validation top models
        other_indices = [i for i in range(n_samples) if i not in representatives and i not in validation_top_indices]
        if other_indices:
            plt.scatter(embedding[other_indices, 0], embedding[other_indices, 1], 
                      c='lightgray', alpha=0.5, label='Other structures')
            legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='lightgray', 
                                            label='Other structures', markersize=8, alpha=0.5))
        
        # Plot validation top models with star markers
        if validation_top_indices:
            plt.scatter(embedding[validation_top_indices, 0], embedding[validation_top_indices, 1], 
                      c='black', s=120, marker='*', edgecolors='white', linewidth=1.5,
                      label='Validation top models')
            legend_elements.append(plt.Line2D([0], [0], marker='*', color='w', 
                                            markerfacecolor='black', markeredgecolor='white',
                                            label='Validation top models', 
                                            markersize=12, linewidth=1.5))
            
            # Add labels to validation top models with bold text
            for idx in validation_top_indices:
                plt.annotate(model_labels[idx], (embedding[idx, 0], embedding[idx, 1]), 
                            fontsize=9, ha='left', va='bottom', weight='bold')
        
        # Plot representative structures with distinct colors
        colors = ['red', 'green', 'orange', 'purple', 'brown', 'magenta', 'cyan', 'pink', 'olive', 'teal']
        for i, rep in enumerate(representatives):
            color_idx = i % len(colors)
            plt.scatter(embedding[rep, 0], embedding[rep, 1], 
                      c=colors[color_idx], s=150, marker='o', edgecolors='black', linewidth=1.5,
                      label=f'Rep: {model_labels[rep]}')
            legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', 
                                            markerfacecolor=colors[color_idx], markeredgecolor='black',
                                            label=f'Rep: {model_labels[rep]}', 
                                            markersize=12, linewidth=1.5))
            
            # Add labels to representative structures with small text
            plt.annotate(model_labels[rep], (embedding[rep, 0], embedding[rep, 1]), 
                        fontsize=7, ha='right', va='top')
    
    # Add title, labels and legend
    plt.title(f'{method} visualization of protein structures {explained_var_text}')
    plt.xlabel('Component 1')
    plt.ylabel('Component 2')
    plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
    plt.tight_layout()
    
    return plt.gcf()  # Return the current figure

