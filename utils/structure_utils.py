import os, sys
import numpy as np
import json
import math
import subprocess

from scipy.spatial import distance
from .os_utils import read_input, read_json, get_coords_by_chains


## -- Functions -- ++
def calculate_pairdistance(arr1, arr2):
    """Calculate the pairwise distances between two arrays of coordinates.
    
    Args:
        arr1: Array of shape (n, 3) with xyz coordinates
        arr2: Array of shape (m, 3) with xyz coordinates
        
    Returns:
        Distance matrix of shape (n, m)
    """
    # Check and reshape inputs if needed
    if arr1.ndim == 1:
        if len(arr1) == 3:  # Single coordinate
            arr1 = arr1.reshape(1, 3)
        else:
            raise ValueError(f"Invalid shape for arr1: {arr1.shape}. Expected (n, 3).")
            
    if arr2.ndim == 1:
        if len(arr2) == 3:  # Single coordinate
            arr2 = arr2.reshape(1, 3)
        else:
            raise ValueError(f"Invalid shape for arr2: {arr2.shape}. Expected (m, 3).")
    
    # Now calculate pairwise distances with proper shapes
    return np.sqrt(np.sum((arr1[:, np.newaxis, :] - arr2[np.newaxis, :, :])**2, axis=2))
    
    
def check_clash_between_chains(pdb_file, clash_dist=3):
    def check_pairwise_clash(coords1, coords2, chain_pair, clash_dist):
        pair_dist  = calculate_pairdistance(coords1, coords2)
        # -- Debug code. Do note remove ++ #
        # print(np.where(pair_dist < clash_dist))  
        # clash_flag = np.any(pair_dist < clash_dist) # If there is one more clash        
        # -------------------------------- #
        clash_flag = np.all(pair_dist > clash_dist) # If there is no any clash
        clash_results[chain_pair] = clash_flag

    # Get pdb structure coordinates
    chain_coords = get_coords_by_chains(pdb_file)
    
    A_coords  = np.array(chain_coords['A'])
    B_coords  = np.array(chain_coords['B'])
    C_coords  = np.array(chain_coords['C'])
    E1_coords = C_coords[:133, : ]
    E2_coords = C_coords[347:, :]
    IDR_coords = C_coords[133:347, :]

    # 나중에 코드 수정 (참고용)
    #E1_indices = np.arange(0, 133)
    #E2_indices = np.arange(347, len(C_coords))
    #exclude_indices = np.concatenate((E1_indices, E2_indices))

    clash_results = {}
    check_pairwise_clash(A_coords, C_coords, ('A', 'C'), clash_dist)
    check_pairwise_clash(B_coords, C_coords, ('B', 'C'), clash_dist)
    check_pairwise_clash(C_coords, C_coords, ('C', 'C'), 2.0)
    check_pairwise_clash(E1_coords, E2_coords, ('E1', 'E2'), clash_dist)
    check_pairwise_clash(
        np.concatenate((E1_coords, E2_coords)), IDR_coords, ('E1E2', 'IDR'), clash_dist
    )
    return pdb_file, clash_results    


# def check_clash_chimeraX(pdb_file):
#     def write_chimera_cxc(pdb_file, msa_dir, struct_name, model_rank):
#         input_content = f"""open {os.getcwd()}/{pdb_file}
# clashes interModel false intraMol false dashes 5 radius 0.16 saveFile {os.getcwd()}/{pdb_file.replace('.pdb', '_clash.out')}
# exit
#         """
#         with open(f'{msa_dir}/{model_rank}.cxc', 'w') as f:
#             f.write(input_content)
#         return None

#     # Define prefix 
#     msa_dir, struct_name = pdb_file.split('/')[:]
#     model_rank = struct_name.split('_')[3]
#     write_chimera_cxc(pdb_file, msa_dir, struct_name, model_rank)

#     # Run chimeraX
#     cmd = f'chimerax --nogui --silent {msa_dir}/{model_rank}.cxc'.split()
#     result = subprocess.run(cmd, capture_output=True, text=True)
    
#     # Parse clash output
#     clash_out = f"{os.getcwd()}/{pdb_file.replace('.pdb', '_clash.out')}"
#     clash_flag = [
#         line for line in read_input(clash_out) 
#         if 'clashes' in line and 'Ignore' not in line and 'Detect' not in line
#     ][0].split()[0]
#     clash_flag = True if float(clash_flag) < 200 else False
    
#     clash_results = {'A': clash_flag}
    
#     return pdb_file, clash_results


def Identify_contact_and_ipLDDT(
    #pdb_file, E1_start, E1_end, E2_start, E2_end, contact_dist=6.0
    E1_start, E1_end, E2_start, E2_end, pdb_file, contact_dist=6.0
):
    json_data = read_json(
        pdb_file.replace('unrelaxed', 'scores').replace('.pdb', '.json')
    )
    plddt = np.array( json_data['plddt'] ) 
    
    # Collect CA coords from structure
    chain_coords = get_coords_by_chains(pdb_file)
    
    A_coords  = np.array(chain_coords['A'])
    B_coords  = np.array(chain_coords['B'])
    C_coords  = np.array(chain_coords['C'])
    E1_coords = C_coords[E1_start: E1_end, : ]
    E2_coords = C_coords[E2_start: E2_end, : ]

    # Calculate pairwise distance for counting contact
    dist_A_E1 = calculate_pairdistance(A_coords, E1_coords)
    dist_A_E2 = calculate_pairdistance(A_coords, E2_coords)
    dist_B_E1 = calculate_pairdistance(B_coords, E1_coords)
    dist_B_E2 = calculate_pairdistance(B_coords, E2_coords)
    
    # The number of residues
    n_res_A, n_res_B = A_coords.shape[0], B_coords.shape[0]
    n_res_AB = n_res_A + n_res_B
    
    # Identify contact between E1/E2 and chains
    contact_A_E1 = np.where((3.0 < dist_A_E1) & (dist_A_E1 < contact_dist))
    contact_A_E2 = np.where((3.0 < dist_A_E2) & (dist_A_E2 < contact_dist))
    contact_B_E1 = np.where((3.0 < dist_B_E1) & (dist_B_E1 < contact_dist))
    contact_B_E2 = np.where((3.0 < dist_B_E2) & (dist_B_E2 < contact_dist))

    # Averaged interface pLDDT
    contact_resid = np.concatenate((
        contact_A_E1[0], contact_A_E1[1] + n_res_AB,
        contact_A_E2[0], contact_A_E2[1] + n_res_AB,
        contact_B_E1[0] + n_res_A, contact_B_E1[1] + n_res_AB,
        contact_B_E2[0] + n_res_A, contact_B_E2[1] + n_res_AB
    ), axis=0)
    avg_iplddt = round(np.mean(plddt[contact_resid]), 2) if contact_resid.shape[0] != 0 else 0
    
    if avg_iplddt != 0:
        # Averaged pLDDT of APP E1/E2 domain    
        domain_plddt = np.concatenate((
            plddt[n_res_AB - 1: n_res_AB + 133], 
            plddt[n_res_AB + 347 -1 :]
        ), axis=0)
        avg_domain_plddt = round(np.mean(domain_plddt), 2)
        return [pdb_file, avg_iplddt, avg_domain_plddt, json_data.get('ptm'), json_data.get('iptm')]


def calculate_z_coords(pdb_file, DUM):
    if DUM:
        z_coords = [
            float(i[46:54]) for i in read_input(pdb_file)
            if i.startswith('HETATM') and 'DUM' in i and i[12: 16].strip() == 'N'
        ]
    else:
        z_coords = [
            float(i[46:54]) for i in read_input(pdb_file)
            if i.startswith('ATOM') and i[12: 16].strip() == 'CA'
        ]
    return np.array(z_coords)