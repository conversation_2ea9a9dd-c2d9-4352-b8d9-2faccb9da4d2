"""
MSA (Multiple Sequence Alignment) visualization utilities.
"""

from typing import List, Tuple
import numpy as np
import matplotlib.pyplot as plt
import logging
from scipy.cluster.hierarchy import linkage, dendrogram
from scipy.spatial.distance import squareform

# Initialize position frequency matrix
amino_acids = 'ACDEFGHIKLMNPQRSTVWY-'  # Including gap character
aa_to_idx = {aa: idx for idx, aa in enumerate(amino_acids)}

# BLOSUM62 matrix for amino acid similarity scoring
blosum62 = {
    'A': {'A': 4, 'R': -1, 'N': -2, 'D': -2, 'C': 0, 'Q': -1, 'E': -1, 'G': 0, 'H': -2, 'I': -1,
            'L': -1, 'K': -1, 'M': -1, 'F': -2, 'P': -1, 'S': 1, 'T': 0, 'W': -3, 'Y': -2, 'V': 0, '-': -4},
    'R': {'A': -1, 'R': 5, 'N': 0, 'D': -2, 'C': -3, 'Q': 1, 'E': 0, 'G': -2, 'H': 0, 'I': -3,
            'L': -2, 'K': 2, 'M': -1, 'F': -3, 'P': -2, 'S': -1, 'T': -1, 'W': -3, 'Y': -2, 'V': -3, '-': -4},
    'N': {'A': -2, 'R': 0, 'N': 6, 'D': 1, 'C': -3, 'Q': 0, 'E': 0, 'G': 0, 'H': 1, 'I': -3,
            'L': -3, 'K': 0, 'M': -2, 'F': -3, 'P': -2, 'S': 1, 'T': 0, 'W': -4, 'Y': -2, 'V': -3, '-': -4},
    'D': {'A': -2, 'R': -2, 'N': 1, 'D': 6, 'C': -3, 'Q': 0, 'E': 2, 'G': -1, 'H': -1, 'I': -3,
            'L': -4, 'K': -1, 'M': -3, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -4, 'Y': -3, 'V': -3, '-': -4},
    'C': {'A': 0, 'R': -3, 'N': -3, 'D': -3, 'C': 9, 'Q': -3, 'E': -4, 'G': -3, 'H': -3, 'I': -1,
            'L': -1, 'K': -3, 'M': -1, 'F': -2, 'P': -3, 'S': -1, 'T': -1, 'W': -2, 'Y': -2, 'V': -1, '-': -4},
    'Q': {'A': -1, 'R': 1, 'N': 0, 'D': 0, 'C': -3, 'Q': 5, 'E': 2, 'G': -2, 'H': 0, 'I': -3,
            'L': -2, 'K': 1, 'M': 0, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -2, 'Y': -1, 'V': -2, '-': -4},
    'E': {'A': -1, 'R': 0, 'N': 0, 'D': 2, 'C': -4, 'Q': 2, 'E': 5, 'G': -2, 'H': 0, 'I': -3,
            'L': -3, 'K': 1, 'M': -2, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -3, 'Y': -2, 'V': -2, '-': -4},
    'G': {'A': 0, 'R': -2, 'N': 0, 'D': -1, 'C': -3, 'Q': -2, 'E': -2, 'G': 6, 'H': -2, 'I': -4,
            'L': -4, 'K': -2, 'M': -3, 'F': -3, 'P': -2, 'S': 0, 'T': -2, 'W': -2, 'Y': -3, 'V': -3, '-': -4},
    'H': {'A': -2, 'R': 0, 'N': 1, 'D': -1, 'C': -3, 'Q': 0, 'E': 0, 'G': -2, 'H': 8, 'I': -3,
            'L': -3, 'K': -1, 'M': -2, 'F': -1, 'P': -2, 'S': -1, 'T': -2, 'W': -2, 'Y': 2, 'V': -3, '-': -4},
    'I': {'A': -1, 'R': -3, 'N': -3, 'D': -3, 'C': -1, 'Q': -3, 'E': -3, 'G': -4, 'H': -3, 'I': 4,
            'L': 2, 'K': -3, 'M': 1, 'F': 0, 'P': -3, 'S': -2, 'T': -1, 'W': -3, 'Y': -1, 'V': 3, '-': -4},
    'L': {'A': -1, 'R': -2, 'N': -3, 'D': -4, 'C': -1, 'Q': -2, 'E': -3, 'G': -4, 'H': -3, 'I': 2,
            'L': 4, 'K': -2, 'M': 2, 'F': 0, 'P': -3, 'S': -2, 'T': -1, 'W': -2, 'Y': -1, 'V': 1, '-': -4},
    'K': {'A': -1, 'R': 2, 'N': 0, 'D': -1, 'C': -3, 'Q': 1, 'E': 1, 'G': -2, 'H': -1, 'I': -3,
            'L': -2, 'K': 5, 'M': -1, 'F': -3, 'P': -1, 'S': 0, 'T': -1, 'W': -3, 'Y': -2, 'V': -2, '-': -4},
    'M': {'A': -1, 'R': -1, 'N': -2, 'D': -3, 'C': -1, 'Q': 0, 'E': -2, 'G': -3, 'H': -2, 'I': 1,
            'L': 2, 'K': -1, 'M': 5, 'F': 0, 'P': -2, 'S': -1, 'T': -1, 'W': -1, 'Y': -1, 'V': 1, '-': -4},
    'F': {'A': -2, 'R': -3, 'N': -3, 'D': -3, 'C': -2, 'Q': -3, 'E': -3, 'G': -3, 'H': -1, 'I': 0,
            'L': 0, 'K': -3, 'M': 0, 'F': 6, 'P': -4, 'S': -2, 'T': -2, 'W': 1, 'Y': 3, 'V': -1, '-': -4},
    'P': {'A': -1, 'R': -2, 'N': -2, 'D': -1, 'C': -3, 'Q': -1, 'E': -1, 'G': -2, 'H': -2, 'I': -3,
            'L': -3, 'K': -1, 'M': -2, 'F': -4, 'P': 7, 'S': -1, 'T': -1, 'W': -4, 'Y': -3, 'V': -2, '-': -4},
    'S': {'A': 1, 'R': -1, 'N': 1, 'D': 0, 'C': -1, 'Q': 0, 'E': 0, 'G': 0, 'H': -1, 'I': -2,
            'L': -2, 'K': 0, 'M': -1, 'F': -2, 'P': -1, 'S': 4, 'T': 1, 'W': -3, 'Y': -2, 'V': -2, '-': -4},
    'T': {'A': 0, 'R': -1, 'N': 0, 'D': -1, 'C': -1, 'Q': -1, 'E': -1, 'G': -2, 'H': -2, 'I': -1,
            'L': -1, 'K': -1, 'M': -1, 'F': -2, 'P': -1, 'S': 1, 'T': 5, 'W': -2, 'Y': -2, 'V': 0, '-': -4},
    'W': {'A': -3, 'R': -3, 'N': -4, 'D': -4, 'C': -2, 'Q': -2, 'E': -3, 'G': -2, 'H': -2, 'I': -3,
            'L': -2, 'K': -3, 'M': -1, 'F': 1, 'P': -4, 'S': -3, 'T': -2, 'W': 11, 'Y': 2, 'V': -3, '-': -4},
    'Y': {'A': -2, 'R': -2, 'N': -2, 'D': -3, 'C': -2, 'Q': -1, 'E': -2, 'G': -3, 'H': 2, 'I': -1,
            'L': -1, 'K': -2, 'M': -1, 'F': 3, 'P': -3, 'S': -2, 'T': -2, 'W': 2, 'Y': 7, 'V': -1, '-': -4},
    'V': {'A': 0, 'R': -3, 'N': -3, 'D': -3, 'C': -1, 'Q': -2, 'E': -2, 'G': -3, 'H': -3, 'I': 3,
            'L': 1, 'K': -2, 'M': 1, 'F': -1, 'P': -2, 'S': -2, 'T': 0, 'W': -3, 'Y': -1, 'V': 4, '-': -4},
    '-': {'A': -4, 'R': -4, 'N': -4, 'D': -4, 'C': -4, 'Q': -4, 'E': -4, 'G': -4, 'H': -4, 'I': -4,
            'L': -4, 'K': -4, 'M': -4, 'F': -4, 'P': -4, 'S': -4, 'T': -4, 'W': -4, 'Y': -4, 'V': -4, '-': 1}
}



def plot_conservation_heatmap(
    msa_data: List[str],  # List of aligned sequences
    output_path: str = None,  # Path to save the output figure # type: ignore
    figsize: Tuple[int, int] = (20, 8),  # Figure size (increased width for better visibility)
    reference_seq: str = None,  # Reference sequence for annotation
    position_labels: List[int] = None,  # Position labels for x-axis
    title: str = "Sequence Conservation Heatmap"  # Plot title
) -> str:
    """
    Create a heatmap visualization of sequence conservation from MSA data.

    Parameters
    ----------
    msa_data : List[str]
        List of aligned sequences (all sequences must have same length).
    output_path : str, optional
        Path to save the output figure.
    figsize : Tuple[int, int], optional
        Figure size (width, height).
    reference_seq : str, optional
        Reference sequence for position annotation.
    position_labels : List[int], optional
        Position labels for x-axis.
    title : str, optional
        Title for the plot.

    Returns
    -------
    str
        Path to the saved visualization or None if error occurs.
    """

    try:
        if not msa_data or len(msa_data) == 0:
            logging.error("Empty MSA data provided")
            return None

        # Convert MSA data to numpy array for easier processing
        seq_length = len(msa_data[0])
        n_sequences = len(msa_data)
        print(f'[INFO ] Plot AA conservation heatmap for {n_sequences} sequences of length {seq_length}')

        # Initialize position frequency matrix
        position_matrix = np.zeros((len(amino_acids), seq_length))

        # Calculate frequencies
        for seq in msa_data:
            for pos, aa in enumerate(seq.upper()):
                if aa in aa_to_idx:
                    position_matrix[aa_to_idx[aa], pos] += 1

        # Convert to frequencies: frequency = count / total_count
        position_matrix = position_matrix / n_sequences

        # Calculate conservation scores (using Shannon entropy)
        conservation_scores = np.zeros(seq_length)
        for i in range(seq_length):
            freqs = position_matrix[:, i]
            # Only consider non-zero frequencies for entropy calculation
            nonzero_freqs = freqs[freqs > 0]
            if len(nonzero_freqs) > 0:
                conservation_scores[i] = -np.sum(nonzero_freqs * np.log2(nonzero_freqs))

        # Normalize conservation scores to [0,1]
        max_entropy = -np.log2(1/len(amino_acids))  # Maximum possible entropy
        conservation_scores = 1 - (conservation_scores / max_entropy)


        ## Create figure with 2 subplots: heatmap and conservation scores
        fig, (ax1, ax2) = plt.subplots(
            2, 1, figsize=figsize,
            gridspec_kw={'height_ratios': [4, 1]},
            constrained_layout=True  # Use constrained_layout for better automatic spacing
        )

        # Plot frequency heatmap with stretched aspect ratio for better horizontal visibility
        im = ax1.imshow(
            position_matrix, aspect='auto', cmap='viridis',
            extent=(-0.5, seq_length-0.5, len(amino_acids)-0.5, -0.5)
        )  # Explicitly set extent

        # Add colorbar to the right side of the heatmap (original position)
        plt.colorbar(im, ax=ax1, label='Frequency')

        # Add amino acid labels
        ax1.set_yticks(range(len(amino_acids)))
        ax1.set_yticklabels(list(amino_acids))

        # Add red boxes to highlight reference sequence amino acids on the heatmap
        # Always assume reference_seq is provided
        if reference_seq:
            for pos, aa in enumerate(reference_seq.upper()):
                if aa in aa_to_idx:
                    # Create a red rectangle around the reference amino acid at each position
                    rect = plt.Rectangle(
                        (pos - 0.5, aa_to_idx[aa] - 0.5),  # Position (x, y)
                        1, 1,                              # Width, Height
                        linewidth=1.0,                     # Border width (increased for visibility)
                        edgecolor='red',                   # Border color
                        facecolor='none'                   # Transparent fill
                    )
                    ax1.add_patch(rect)

        # Plot conservation scores with exact alignment to heatmap
        # Use step='post' to align with cell boundaries in heatmap
        x_positions = np.arange(seq_length)
        ax2.plot(x_positions, conservation_scores, color='blue', linewidth=.7)
        ax2.fill_between(x_positions, conservation_scores, alpha=0.3)
        ax2.set_ylim(0, 1.03)

        # Set x-axis limits to match the heatmap exactly
        ax1.set_xlim(-0.5, seq_length - 0.5)
        ax2.set_xlim(-0.5, seq_length - 0.5)

        ax2.set_ylabel('Conservation')
        ax2.set_xlabel('Position')

        # Add position labels if provided
        if position_labels:
            # Determine appropriate tick interval based on sequence length
            if seq_length > 500:
                tick_interval = 50
            elif seq_length > 200:
                tick_interval = 20
            else:
                tick_interval = 10

            # Show positions at regular intervals to avoid overcrowding
            tick_positions = range(0, seq_length, tick_interval)

            # Ensure position_labels has the correct length
            if len(position_labels) != seq_length:
                logging.warning(f"Position labels length ({len(position_labels)}) does not match sequence length ({seq_length}). Using indices instead.")
                tick_labels = [str(i) for i in tick_positions]
            else:
                tick_labels = [str(position_labels[i]) for i in tick_positions]

            # Remove x-axis labels from heatmap (top plot) since they're redundant with conservation plot
            # Keep the ticks but remove the labels for heatmap
            ax1.set_xticks(tick_positions)
            ax1.set_xticklabels([])  # Remove x-tick labels from heatmap

            # Set ticks and rotate labels for conservation plot (bottom plot)
            ax2.set_xticks(tick_positions)
            ax2.set_xticklabels(tick_labels, rotation=45, ha='right')

        # Add titles
        ax1.set_title(title)

        # Save figure if output path provided
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        logging.info(f"Saved conservation heatmap to {output_path}")
        return output_path

    except Exception as e:
        logging.error(f"Error in plot_conservation_heatmap: {str(e)}", exc_info=True)
        if 'fig' in locals():
            plt.close()
        return None

def plot_sequence_similarity(
    msa_data: List[str],  # List of aligned sequences
    output_path: str = None,  # Path to save the output figure
    figsize: Tuple[int, int] = (12, 10),  # Figure size
    method: str = 'identity',  # Similarity calculation method ('identity' or 'blosum62')
    cluster_sequences: bool = True,  # Whether to cluster sequences by similarity
    sequence_labels: List[str] = None,  # Labels for sequences
    title: str = "Sequence Similarity Matrix",  # Plot title
    cluster_assignments: List[int] = None,  # Cluster assignments for each sequence
    use_sequence_numbers: bool = True  # Whether to use sequence numbers instead of descriptions
) -> str:
    """
    Create a visualization of sequence similarity between MSA sequences.

    Parameters
    ----------
    msa_data : List[str]
        List of aligned sequences (all sequences must have same length).
    output_path : str, optional
        Path to save the output figure.
    figsize : Tuple[int, int], optional
        Figure size (width, height).
    method : str, optional
        Method to calculate similarity ('identity' or 'blosum62').
    cluster_sequences : bool, optional
        Whether to cluster sequences by similarity.
    sequence_labels : List[str], optional
        Labels for sequences (if None, uses sequence indices).
    title : str, optional
        Title for the plot.
    cluster_assignments : List[int], optional
        Pre-computed cluster assignments for each sequence.
    use_sequence_numbers : bool, optional
        Whether to use sequence numbers instead of descriptions for readability.

    Returns
    -------
    str
        Path to the saved visualization or None if error occurs.
    """
    try:
        if not msa_data or len(msa_data) == 0:
            logging.error("Empty MSA data provided")
            return None

        n_sequences = len(msa_data)

        # Initialize similarity matrix
        similarity_matrix = np.zeros((n_sequences, n_sequences))

        # Calculate similarity matrix
        for i in range(n_sequences):
            for j in range(i, n_sequences):
                if method == 'identity':
                    # Calculate sequence identity
                    matches = sum(1 for a, b in zip(msa_data[i], msa_data[j]) if a == b)
                    similarity = matches / len(msa_data[i])
                else:  # blosum62
                    # Calculate BLOSUM62-based similarity
                    score = 0
                    max_possible = 0
                    for a, b in zip(msa_data[i], msa_data[j]):
                        if a in blosum62 and b in blosum62:
                            score += blosum62[a].get(b, -4)  # Default to gap penalty
                            max_possible += blosum62[a][a]  # Maximum possible score
                    similarity = score / max_possible if max_possible != 0 else 0

                similarity_matrix[i, j] = similarity
                similarity_matrix[j, i] = similarity

        # Handle clustering and ordering
        ordering = list(range(n_sequences))
        
        if cluster_assignments is not None:
            # Use pre-computed cluster assignments to order sequences
            cluster_order = sorted(enumerate(cluster_assignments), key=lambda x: (x[1], x[0]))
            ordering = [idx for idx, _ in cluster_order]
            similarity_matrix = similarity_matrix[ordering, :][:, ordering]
            if sequence_labels:
                sequence_labels = [sequence_labels[i] for i in ordering]
            
            # Create simple figure without dendrogram for cluster assignments
            fig, ax_heatmap = plt.subplots(figsize=figsize)
                
        elif cluster_sequences:
            # Perform hierarchical clustering
            distance_matrix = 1 - similarity_matrix
            linkage_matrix = linkage(squareform(distance_matrix), method='average')

            # Create figure with dendrogram
            fig = plt.figure(figsize=figsize)

            # Add gridspec for dendrogram and heatmap
            gs = plt.GridSpec(2, 2, width_ratios=[0.15, 1], height_ratios=[0.15, 1])

            # Plot dendrograms
            ax_dendr_top = fig.add_subplot(gs[0, 1])
            ax_dendr_left = fig.add_subplot(gs[1, 0])
            ax_heatmap = fig.add_subplot(gs[1, 1])

            # Plot dendrograms
            dendrogram(linkage_matrix, ax=ax_dendr_top, orientation='top')
            dendrogram(linkage_matrix, ax=ax_dendr_left, orientation='left')

            # Get the ordering of leaves
            leaves = dendrogram(linkage_matrix, no_plot=True)['leaves']
            ordering = leaves

            # Reorder similarity matrix
            similarity_matrix = similarity_matrix[leaves, :][:, leaves]

            # If sequence labels provided, reorder them
            if sequence_labels:
                sequence_labels = [sequence_labels[i] for i in leaves]

            # Remove dendrogram axes
            ax_dendr_top.set_xticks([])
            ax_dendr_top.set_yticks([])
            ax_dendr_left.set_xticks([])
            ax_dendr_left.set_yticks([])

        else:
            # Create simple figure without dendrogram
            fig, ax_heatmap = plt.subplots(figsize=figsize)

        # Plot heatmap
        im = ax_heatmap.imshow(similarity_matrix, cmap='viridis', aspect='equal')
        plt.colorbar(im, ax=ax_heatmap, label='Similarity Score')

        # Create sequence labels based on use_sequence_numbers flag
        if use_sequence_numbers:
            # Use sequence numbers for better readability
            display_labels = [f"#{i+1}" for i in range(n_sequences)]
        else:
            # Use provided sequence labels or default numbering
            if sequence_labels:
                display_labels = sequence_labels
            else:
                display_labels = [f"Seq_{i+1}" for i in range(n_sequences)]
        
        # Add sequence labels with improved formatting
        if n_sequences <= 50:  # Show all labels for 50 or fewer sequences
            ax_heatmap.set_xticks(range(n_sequences))
            ax_heatmap.set_yticks(range(n_sequences))
            ax_heatmap.set_xticklabels(display_labels, rotation=45, ha='right', fontsize=8)
            ax_heatmap.set_yticklabels(display_labels, fontsize=8)
        elif n_sequences <= 100:  # Show every other label for 51-100 sequences
            step = 2
            tick_positions = range(0, n_sequences, step)
            ax_heatmap.set_xticks(tick_positions)
            ax_heatmap.set_yticks(tick_positions)
            ax_heatmap.set_xticklabels([display_labels[i] for i in tick_positions], 
                                     rotation=45, ha='right', fontsize=7)
            ax_heatmap.set_yticklabels([display_labels[i] for i in tick_positions], fontsize=7)
        else:
            # For more than 100 sequences, show every 20th label
            step = max(1, n_sequences // 20)
            tick_positions = range(0, n_sequences, step)
            ax_heatmap.set_xticks(tick_positions)
            ax_heatmap.set_yticks(tick_positions)
            ax_heatmap.set_xticklabels([display_labels[i] for i in tick_positions], 
                                     rotation=45, ha='right', fontsize=6)
            ax_heatmap.set_yticklabels([display_labels[i] for i in tick_positions], fontsize=6)

        # Add cluster boundaries if cluster assignments are provided
        if cluster_assignments is not None:
            # Reorder cluster assignments according to ordering
            ordered_clusters = [cluster_assignments[ordering[i]] for i in range(n_sequences)]
            
            # Find cluster boundaries (ignore noise points with value -1)
            cluster_boundaries = []
            current_cluster = ordered_clusters[0]
            for i, cluster in enumerate(ordered_clusters[1:], 1):
                if cluster != current_cluster:
                    cluster_boundaries.append(i - 0.5)
                    current_cluster = cluster
            
            # Draw cluster boundary lines with enhanced visibility
            for boundary in cluster_boundaries:
                ax_heatmap.axhline(y=boundary, color='red', linewidth=2.5, alpha=0.9)
                ax_heatmap.axvline(x=boundary, color='red', linewidth=2.5, alpha=0.9)
            
            # Log cluster boundary information for debugging
            logging.debug(f"Drew {len(cluster_boundaries)} cluster boundaries at positions: {cluster_boundaries}")
            
            # Count clusters and noise points for verification
            unique_clusters = set(ordered_clusters)
            n_clusters = len(unique_clusters - {-1}) if -1 in unique_clusters else len(unique_clusters)
            n_noise = ordered_clusters.count(-1)
            logging.info(f"Cluster visualization: {n_clusters} clusters, {n_noise} noise points, {len(cluster_boundaries)} boundaries")

        # Add title
        method_str = 'Sequence Identity' if method == 'identity' else 'BLOSUM62 Similarity'
        plt.suptitle(f"{title}\n({method_str})")

        # Adjust layout
        plt.tight_layout()

        # Save figure if output path provided
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            logging.info(f"Saved sequence similarity visualization to {output_path}")
            return output_path
        else:
            plt.close()
            return None

    except Exception as e:
        logging.error(f"Error in plot_sequence_similarity: {str(e)}", exc_info=True)
        if 'fig' in locals():
            plt.close()
        return None