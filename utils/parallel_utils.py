import os, sys
import logging  
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Any, Tuple, Callable, TypeVar, Optional

from tqdm import tqdm

T = TypeVar('T')
R = TypeVar('R')


def get_chunk_size(
    items: List[Any], # List of items to process
    n_workers: int # Number of worker processes
) -> int: # Chunk size
    """
    Calculate an appropriate chunk size for parallel processing.

    Args:
        items (list): List of items to process
        n_workers (int): Number of worker processes

    Returns:
        int: Chunk size
    """
    n_items = len(items)
    if n_items <= n_workers:
        return 1
    return max(1, int(n_items / (n_workers * 2)))


def split_tasks(tasks, n_chunks, chunk_size=None):
    """
    Split tasks into optimal chunks for parallel processing.
    
    Parameters
    ----------
    tasks (list):
        List of tasks to be split.
    n_chunks (int):
        Number of chunks to create (typically number of workers).
    chunk_size (int, optional):
        Size of each chunk. If None, calculated based on n_chunks.
        
    Returns
    -------
    list:
        List of task chunks.
    """
    if chunk_size is None:
        chunk_size = get_chunk_size(tasks, n_chunks)
    
    return [tasks[i:i+chunk_size] for i in range(0, len(tasks), chunk_size)]


def optimize_workers(
    n_workers: int,
    msasub_dirs: List[str]
) -> Tuple[int, int, int]:
    """
    Optimize worker allocation based on available cores and task type.
    
    Args:
        n_workers (int): Requested number of workers (0 for auto)
        msasub_dirs (list): List of MSA subset directories
        
    Returns:
        tuple: (n_workers, dir_workers, model_workers)
    """    
    if n_workers <= 0:  # Auto-detect the number of cores
        # Check if we're in a cluster environment (look for SLURM variables)
        if 'SLURM_CPUS_PER_TASK' in os.environ:
            # SLURM environment
            slurm_cpus = int(os.environ.get('SLURM_CPUS_PER_TASK', 1))
            logging.info(f"Detected SLURM environment with {slurm_cpus} CPUs")
            n_workers = max(1, slurm_cpus - 1)  # Reserve 1 CPU for system
        else:
            # Standard environment
            available_cores = multiprocessing.cpu_count()
            n_workers = max(1, available_cores - 1)  # Reserve 1 CPU for system
            logging.info(f"Auto-detected {available_cores} cores")
            
        logging.info(f"Using {n_workers} workers")
    else:
        logging.info(f"Using {n_workers} workers as specified")

    # Calculate optimal worker distribution
    # For I/O bound tasks (directory processing), use more threads
    # For CPU bound tasks (model processing), use fewer but dedicated processes
    dir_workers = min(n_workers, len(msasub_dirs))
    model_workers = max(1, n_workers // 2)
    logging.info(f"Worker allocation: {dir_workers} for directories, {model_workers} for models")
    
    return n_workers, dir_workers, model_workers


def parallel_process(
    items: List[T],  # List of items to process
    func: Callable[[T], R],  # Function to apply to each item
    n_workers: int = 4,  # Number of worker processes or threads
    use_threads: bool = False,  # Whether to use threads instead of processes
    chunksize: int = 1,  # Chunk size for multiprocessing.Pool
    show_progress: bool = True,  # Whether to show progress bar
    desc: str = "Processing",  # Description for progress bar
    filter_none: bool = True,  # Whether to filter out None results
) -> List[R]:
    """
    Process items in parallel using either threads or processes.
    
    Parameters
    ----------
    items (List[T]):
        List of items to process with the function.
    func (Callable[[T], R]):
        Function to apply to each item.
    n_workers (int):
        Number of worker processes or threads to use.
    use_threads (bool):
        If True, use ThreadPoolExecutor; if False, use multiprocessing.Pool.
    chunksize (int):
        Size of chunks for multiprocessing.Pool (ignored if using threads).
    show_progress (bool):
        Whether to show a progress bar.
    desc (str):
        Description for the progress bar.
    filter_none (bool):
        Whether to filter out None results from the result list.
        
    Returns
    -------
    List[R]:
        List of results from applying the function to each item.
        
    Notes
    -----
    - Use threads (use_threads=True) for I/O-bound tasks
    - Use processes (use_threads=False) for CPU-bound tasks
    - Exceptions are caught and logged, but do not halt processing
    """
    results = []
    
    # Handle empty input
    if not items:
        logging.warning("No items to process")
        return results
    
    # Adjust workers if needed
    n_workers = min(n_workers, len(items))
    
    if use_threads:
        # Thread-based parallelism (better for I/O bound tasks)
        with ThreadPoolExecutor(max_workers=n_workers) as executor:
            # Submit all tasks
            futures = {executor.submit(func, item): item for item in items}
            
            # Process results as they complete
            iterator = as_completed(futures)
            if show_progress:
                iterator = tqdm(iterator, total=len(futures), desc=desc)
                
            for future in iterator:
                try:
                    result = future.result()
                    if result is not None or not filter_none:
                        results.append(result)
                except Exception as e:
                    item = futures[future]
                    logging.error(f"Error processing {item}: {str(e)}")
    else:
        # Process-based parallelism (better for CPU bound tasks)
        with multiprocessing.Pool(processes=n_workers) as pool:
            # Apply function to all items
            iterator = pool.imap(func, items, chunksize=chunksize)
            
            # Add progress bar if requested
            if show_progress:
                iterator = tqdm(iterator, total=len(items), desc=desc)
                
            # Collect results
            for result in iterator:
                if result is not None or not filter_none:
                    results.append(result)
    
    return results