import os, sys
import json
import numpy as np
import subprocess
import time
import csv
import logging
from typing import List, Dict, Union, Any, Tuple
from pathlib import Path
from collections import defaultdict
from Bio.PDB import PDBIO, PDBParser, MMCIFParser
import matplotlib.pyplot as plt  # Required for visualize_matrix function


## -- Functions -- ++
def configure_app_isoform(target_dir: str
) -> str | None:
    app_isoform = next((iso for iso in ["695", "751", "770"] if iso in target_dir), None)
    return app_isoform


def read_input(
    input_fname: str,  # Path to the input file
) -> list[str]:
    """
    Read a file and return its contents as a list of lines.

    Parameters
    ----------
    input_fname (str):
        Path to the file to be read.

    Returns
    -------
    list[str]:
        List containing all lines from the file.
    """
    return open(input_fname, 'r').readlines()


def write_output(
    output_lines: list[str],  # Lines to write to file
    output_fname: str,        # Path to the output file
) -> None:
    """
    Write a list of strings to a file.

    Parameters
    ----------
    output_lines (list[str]):
        List of strings to be written to the file.
    output_fname (str):
        Path to the output file.

    Returns
    -------
    None
    """
    output_f = open(output_fname, 'w')
    [ output_f.write(i) for i in output_lines ]
    output_f.close()


def read_json(
    input_fname: str,  # Path to the JSON file
) -> dict | list | None:
    """
    Read a JSON file and return its contents as a Python object.

    Parameters
    ----------
    input_fname (str):
        Path to the JSON file to be read.

    Returns
    -------
    dict | list | None:
        Python object representation of the JSON content,
        or None if the file is not found or contains invalid JSON.
    """
    try:
        with open(input_fname, 'r') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"Error: File not found - {input_fname}")
        return None
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format - {input_fname}")
        return None
    

def scan_for_model_cif(
    base_dir: str,                # Directory to scan
    entity_query: str = "model.cif"  # File to search for
) -> str | None:
    """
    Scan for a model.cif file in the given directory.

    Parameters
    ----------
    base_dir (str):
        Directory to scan for the file.
    entity_query (str):
        Name of the file to search for, defaults to "model.cif".

    Returns
    -------
    str | None:
        Full path to the found file, or None if not found.
    """
    try:
        with os.scandir(base_dir) as entries:
            for entry in entries:
                if entry.name == entity_query and entry.is_file():
                    return os.path.join(base_dir, entry.name)
    except FileNotFoundError:
        pass
    return None


## -- PDB -- ++
def mmcif_to_pdb(
    mmcif_file: str,  # Path to the input MMCIF file
    pdb_file: str,    # Path for the output PDB file
) -> None:
    """
    Convert an MMCIF file to PDB file format.
    
    Parameters
    ----------
    mmcif_file (str):
        Path to the input MMCIF file.
    pdb_file (str):
        Path for the output PDB file.
        
    Returns
    -------
    None
    """    
    # Parse the MMCIF file
    parser = MMCIFParser(QUIET=True)
    structure = parser.get_structure("structure", mmcif_file)

    # Write the structure to a PDB file
    io = PDBIO()
    io.set_structure(structure)
    io.save(pdb_file)
    print(f"[INFO] Conversion complete: {mmcif_file} -> {pdb_file}")


def change_chain_id_to_A(
    input_pdb_file: str,  # Path to the input PDB file
    output_pdb_file: str, # Path for the output PDB file
    chain_id: str,        # Chain ID(s) to change to 'A'
) -> None:
    """
    Change specific chain IDs in a PDB file to 'A'.
    
    Parameters
    ----------
    input_pdb_file (str):
        Path to the input PDB file.
    output_pdb_file (str):
        Path for the output PDB file.
    chain_id (str):
        String containing the chain ID(s) to be changed to 'A'.
    
    Returns
    -------
    None
    """
    with open(input_pdb_file, 'r') as infile, open(output_pdb_file, 'w') as outfile:
        for line in infile:
            if line.startswith(('ATOM', 'HETATM')) and line[21] in chain_id:  # These lines contain atomic data
                # Replace the chain ID (usually in column 22) with 'A'
                # Columns: 0-5 for record name, chain ID is at position 21 (0-indexed)
                line = line[:21] + 'A' + line[22:]
                outfile.write(line)

    
def get_coords_by_chains(
    pdb_file: str,  # Path to the PDB file
) -> defaultdict[str, list[list[float]]]:
    """
    Extract coordinates for each chain from a PDB file.
    Prioritizes CB atoms, falls back to CA atoms if CB is not present (e.g., for Glycine).

    Parameters
    ----------
    pdb_file (str):
        Path to the PDB file.

    Returns
    -------
    defaultdict[str, list[list[float]]]:
        Dictionary with chain IDs as keys and lists of [x, y, z] coordinates as values.
        Each coordinate is selected from CB atoms when available, otherwise from CA atoms.
    """
    pdb_lines = read_input(pdb_file)
    # Temporary storage: chain_id -> res_seq -> {'CA': [x,y,z], 'CB': [x,y,z]}
    temp_residue_coords = defaultdict(lambda: defaultdict(lambda: {'CA': None, 'CB': None}))
    processed_residues = set() # Keep track of residues already added to final dict

    for line in pdb_lines:
        if line.startswith('ATOM'):
            atom_name = line[12:16].strip()
            if atom_name in ['CA', 'CB']:
                chain_id = line[21]
                # Use residue sequence number + insertion code as unique identifier
                res_seq_id = (int(line[22:26].strip()), line[26].strip()) # Tuple (res_seq, iCode)
                
                x = float(line[30:38])
                y = float(line[38:46])
                z = float(line[46:54])
                
                # Store coordinate, overwriting if alternate conformation exists (takes the last one)
                temp_residue_coords[chain_id][res_seq_id][atom_name] = [x, y, z]

    # Build the final coordinate list, prioritizing CB over CA
    chain_coords = defaultdict(list)
    # Sort chains and residues to maintain order
    sorted_chains = sorted(temp_residue_coords.keys())
    for chain_id in sorted_chains:
        sorted_res_seq_ids = sorted(temp_residue_coords[chain_id].keys())
        for res_seq_id in sorted_res_seq_ids:
            coords = temp_residue_coords[chain_id][res_seq_id]
            # Prioritize CB
            if coords['CB'] is not None:
                chain_coords[chain_id].append(coords['CB'])
            # Fallback to CA if CB is missing
            elif coords['CA'] is not None:
                chain_coords[chain_id].append(coords['CA'])
            # Else: Neither CA nor CB found for this residue (should be rare) - skip or log warning
            # else:
            #     print(f"Warning: No CA or CB found for residue {res_seq_id} in chain {chain_id}")

    return chain_coords


def prompt_subproc_for_TMRmsd(
    proj_p: str,      # Project path
    output_dir: str,  # Output directory
    pdb_file: str,    # PDB file name
) -> list[str | float]:
    """
    Run TMalign to calculate similarity between two protein structures and generate alignment files.
    
    Parameters
    ----------
    proj_p (str):
        Path to the project directory.
    output_dir (str):
        Path to the output directory.
    pdb_file (str):
        Name of the PDB file to compare.
        
    Returns
    -------
    list[str | float]:
        List containing [PDB file name, TM-score value].
    """
    suffix = pdb_file.split('.pdb')[0]
    cmd = f"TMalign {proj_p}/{output_dir}/{suffix}_Asec.pdb {proj_p}/Asec_OPM.pdb".split()
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        output_lines = result.stdout.splitlines()
        #tmrmsd = [line.split()[4].replace(',', '') for line in output_lines if 'RMSD' in line][0]
        tmrmsd = [line.split()[1].replace(',', '') for line in output_lines if 'TM-score' in line][0]
    else:
        print("Error:", result.stderr)
    
    cmd = f"TMalign {proj_p}/{output_dir}/{suffix}.pdb {proj_p}/Asec_OPM.pdb -o {proj_p}/{output_dir}/{suffix}_align.pdb".split()
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode == 0:
        output_lines = result.stdout.splitlines()
    else:
        print("Error:", result.stderr)
    return [f'{suffix}.pdb', float(tmrmsd)]


def visualize_matrix(
    matrix: np.ndarray,           # Matrix to visualize
    cmap: str = 'coolwarm',       # Colormap for visualization
    title: str = 'Matrix Visualization'  # Plot title
) -> None:
    """
    Visualize an n x n matrix as a heatmap.

    Parameters
    ----------
    matrix (np.ndarray):
        An n x n numpy array to be visualized.
    cmap (str):
        Colormap to use for visualization, defaults to 'coolwarm'.
    title (str):
        Title of the plot, defaults to 'Matrix Visualization'.
        
    Returns
    -------
    None:
        The function displays a plot but doesn't return any value.
    """
    if not isinstance(matrix, np.ndarray):
        raise ValueError("Input must be a numpy array.")
    if matrix.ndim != 2 or matrix.shape[0] != matrix.shape[1]:
        raise ValueError("Input must be a square matrix (n x n).")

    plt.figure(figsize=(8, 6))
    plt.imshow(matrix, cmap=cmap, aspect='equal')
    plt.colorbar(label='Value')
    plt.title(title, fontsize=14)
    plt.xlabel('Column Index')
    plt.ylabel('Row Index')
    plt.xticks(range(matrix.shape[0]))
    plt.yticks(range(matrix.shape[0]))
    plt.show()


def write_metric_tsv(
    filename: str, metric_list: List[Dict[str, Any]]
) -> bool:
    """
    Write a list of metric dictionaries to a TSV file.

    Parameters
    ----------
    filename (str):
        Path to the output TSV file.
    metric_list (list):
        List of dictionaries containing metrics.

    Returns
    -------
    bool:
        True if successful, False otherwise.
    """
    try:
        if not metric_list:
            logging.warning(f"No metrics to write to {filename}")
            return False

        # Define ordered headers to match sort criteria
        ordered_headers = [
            'model', 'msasub',
            'avg_ipAE', 'Median_ipAE', 'Min_ipAE', 'Max_ipAE',
            'avg_ipLDDT', 'avg_domain_pLDDT', 'ipTM', 'pTM'
        ]

        # Filter headers to only include those present in the data
        available_keys = set()
        for metric_dict in metric_list:
            available_keys.update(metric_dict.keys())

        headers = [h for h in ordered_headers if h in available_keys]

        # Add any remaining keys not in the ordered list
        remaining_keys = sorted(k for k in available_keys if k not in ordered_headers)
        headers.extend(remaining_keys)

        with open(filename, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=headers, delimiter='\t')
            writer.writeheader()
            for metric_dict in metric_list:
                writer.writerow(metric_dict)

        return True
    except Exception as e:
        logging.error(f"Error writing metrics to {filename}: {str(e)}")
        return False


def read_metric_tsv(
    filename: str
) -> List[Dict[str, Any]]:
    """
    Read metrics from a TSV file and convert string values to appropriate types.
    
    Parameters
    ----------
    filename (str):
        Path to the TSV file.
        
    Returns
    -------
    list:
        List of dictionaries containing metrics.
    """
    metric_list = []
    
    try:
        if not os.path.exists(filename):
            logging.warning(f"Metrics file not found: {filename}")
            return []
            
        with open(filename, 'r') as f:
            reader = csv.DictReader(f, delimiter='\t')
            for row in reader:
                # Convert string values to float where needed
                numeric_keys = ['avg_ipAE', 'Median_ipAE', 'Min_ipAE', 'Max_ipAE', 
                               'avg_ipLDDT', 'avg_domain_pLDDT', 'ipTM', 'pTM']
                for key in numeric_keys:
                    if key in row:
                        try:
                            row[key] = float(row[key])
                        except (ValueError, TypeError):
                            row[key] = 0.0
                metric_list.append(row)
                
        logging.info(f"Successfully loaded {len(metric_list)} metrics from {filename}")
        return metric_list
    except Exception as e:
        logging.warning(f"Error reading metrics file {filename}: {e}")
        return []
