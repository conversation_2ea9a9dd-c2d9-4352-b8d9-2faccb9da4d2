import re
import os
import subprocess
import json # For parsing system_name from input.json if needed, and potentially _data.json
import sys # For sys.executable
from typing import List, Tuple, Optional, Any

# Define type alias for a single sequence entry
SequenceEntry = Tuple[str, str] # (description, sequence)

class MSA:
    """
    A class to represent and manipulate Multiple Sequence Alignments (MSAs).
    Primarily focused on loading MSA data, potentially after generation via AlphaFold3 pipeline.
    """

    def __init__(
        self,
        sequences: Optional[List[str]] = None, # List of amino acid sequences
        descriptions: Optional[List[str]] = None, # List of sequence descriptions or headers
        msa_format: Optional[str] = "unknown" # Format of the MSA (e.g., 'fasta', 'a3m')
    ):
        """
        Initializes an MSA object.

        Parameters
        ----------
        sequences (Optional[List[str]]):
            A list of protein sequences.
        descriptions (Optional[List[str]]):
            A list of descriptions for each sequence.
        msa_format (Optional[str]):
            The format of the MSA (e.g., 'fasta', 'a3m').
        """
        if sequences is None:
            sequences = []
        if descriptions is None:
            descriptions = []

        if len(sequences) != len(descriptions):
            raise ValueError("The number of sequences must match the number of descriptions.")

        self._sequences: List[str] = sequences
        self._descriptions: List[str] = descriptions
        self._msa_format: str = msa_format

    @classmethod
    def from_file(
        cls,
        file_path: str, # Path to the MSA file
        msa_format: Optional[str] = None # The format of the MSA file (e.g., 'fasta', 'a3m'). Auto-detect if None.
    ) -> 'MSA':
        """
        Loads an MSA from a file.

        Automatically detects format if not specified, based on file extension or content.

        Parameters
        ----------
        file_path (str):
            The path to the MSA file.
        msa_format (Optional[str]):
            The format of the MSA file (e.g., 'fasta', 'a3m').
            If None, attempts to auto-detect common formats.

        Returns
        -------
        MSA:
            An MSA object loaded from the file.
        
        Raises
        ------
        ValueError:
            If the file format is not supported or cannot be determined.
        FileNotFoundError:
            If the MSA file does not exist.
        """
        try:
            with open(file_path, 'r') as f:
                content = f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"MSA file not found at: {file_path}")

        detected_format = msa_format
        if detected_format is None:
            if file_path.endswith((".fasta", ".fa", ".fas")):
                detected_format = "fasta"
            elif file_path.endswith((".a3m")):
                detected_format = "a3m"
            else: # Basic content detection
                if content.startswith(">") and "\n>" in content:
                    detected_format = "fasta"
                elif content.startswith("#A3M#") or (content.startswith(">") and "\n" not in content.split("\n",1)[1].split("\n",1)[0]): # crude a3m check
                    detected_format = "a3m"
                else:
                    raise ValueError(
                        f"Could not auto-detect MSA format for {file_path}. Please specify 'msa_format'."
                    )
        
        sequences, descriptions = [], []
        if detected_format == "fasta":
            sequences, descriptions = _parse_fasta(content)
        elif detected_format == "a3m":
            sequences, descriptions = _parse_a3m(content)
        else:
            raise ValueError(f"Unsupported MSA format: {detected_format}")

        return cls(sequences=sequences, descriptions=descriptions, msa_format=detected_format)

    @classmethod
    def from_alphafold_pipeline_or_a3m(
        cls,
        input_fasta_abs_path: str, # Absolute path to the input FASTA file
        project_root_abs_path: str, # Absolute path to the project root directory
        msa_output_dir_name: str = "MSA_JKHmmer", # Directory name for final A3M files, relative to project_root
        af3_intermediate_dir_name: str = "AF3/Full_wo_tmpl", # AF3 output dir for _data.json, relative to project_root
        af3_script_path: str = "/home/<USER>/git-repo/AlphaFold3/run_alphafold.py", # Path to run_alphafold.py
        af3_python_exec: str = "/appl/anaconda3/envs/AlphaFold3/bin/python", # Python executable for AF3 env
        force_msa_generation: bool = False # If True, always run AF3 data pipeline
    ) -> 'MSA':
        """
        Ensures MSA data (_data.json) is generated via AlphaFold3 pipeline if not present or forced,
        then loads the MSA from an expected A3M file.

        This method assumes that if the AlphaFold3 data pipeline runs successfully (or has already run),
        an A3M file corresponding to the input FASTA will be available in the specified msa_output_dir_name.
        The generation of this A3M from _data.json is considered an external step for this method's scope.

        Parameters
        ----------
        input_fasta_abs_path (str):
            Absolute path to the input FASTA file.
        project_root_abs_path (str):
            Absolute path to the project root directory.
        msa_output_dir_name (str):
            Directory name where the final A3M MSA file is expected, relative to project_root_abs_path.
        af3_intermediate_dir_name (str):
            Directory name for AlphaFold3's intermediate outputs (like _data.json),
            relative to project_root_abs_path.
        af3_script_path (str):
            Absolute path to the run_alphafold.py script.
        af3_python_exec (str):
            Absolute path to the Python executable in the AlphaFold3 conda environment.
        force_msa_generation (bool):
            If True, the AlphaFold3 data pipeline step will be run even if the 
            _data.json file already exists.

        Returns
        -------
        MSA:
            An MSA object loaded from the A3M file.

        Raises
        -------
        FileNotFoundError:
            If input_fasta_abs_path, af3_script_path, af3_python_exec do not exist,
            or if the expected A3M file is not found after the pipeline step.
        RuntimeError:
            If the AlphaFold3 data pipeline subprocess fails.
        ValueError:
            If system_name cannot be determined from the FASTA file.
        """
        if not os.path.exists(input_fasta_abs_path):
            raise FileNotFoundError(f"Input FASTA file not found: {input_fasta_abs_path}")
        if not os.path.exists(af3_script_path):
            raise FileNotFoundError(f"AlphaFold3 script not found: {af3_script_path}")
        if not os.path.exists(af3_python_exec):
            raise FileNotFoundError(f"AlphaFold3 Python executable not found: {af3_python_exec}")

        # Determine system_name from FASTA header
        try:
            with open(input_fasta_abs_path, 'r') as f:
                first_line = f.readline().strip()
            if not first_line.startswith(">"):
                raise ValueError("Input FASTA file must start with a header line beginning with '>'.")
            system_name = first_line[1:].split()[0] # Get the first word after '>'
            if not system_name:
                 raise ValueError("Could not extract system_name from FASTA header.")
        except Exception as e:
            raise ValueError(f"Error reading system_name from FASTA file {input_fasta_abs_path}: {e}")

        system_name_lower = system_name.lower()

        # Define paths based on project_root_abs_path
        af3_output_root_abs_path = os.path.join(project_root_abs_path, af3_intermediate_dir_name)
        # Path for the _data.json generated by AF3 pipeline
        # e.g., <project_root>/AF3/Full_wo_tmpl/<system_name_lower>/<system_name_lower>_data.json
        af3_system_specific_output_dir = os.path.join(af3_output_root_abs_path, system_name_lower)
        target_data_json_path = os.path.join(af3_system_specific_output_dir, f"{system_name_lower}_data.json")

        # Path for the input.json that gen_input_json.py creates
        # This is typically in project_root_abs_path directly for the script AF3W_wo-tmpl.sh
        input_json_for_af3_path = os.path.join(project_root_abs_path, "input.json")

        # Check if AlphaFold3 _data.json exists or if generation is forced
        if force_msa_generation or not os.path.exists(target_data_json_path):
            print(f"[INFO ] AlphaFold3 _data.json not found at {target_data_json_path} or generation is forced.")
            
            # Step 1: Generate input.json for AlphaFold3 (mimicking AF3W_wo-tmpl.sh)
            # This step assumes gen_input_json.py is in a known location, e.g., project_root/src or similar.
            # For simplicity, let's assume gen_input_json.py is accessible or adjust path.
            # The script AF3W_wo-tmpl.sh uses ${src}/gen_input_json.py. We need this src_path.
            # Let's assume src is at project_root_abs_path + "/src" for this example.
            src_dir_abs_path = os.path.join(project_root_abs_path, "src") 
            gen_input_json_script_path = os.path.join(src_dir_abs_path, "gen_input_json.py")
            if not os.path.exists(gen_input_json_script_path):
                 # Fallback if src/gen_input_json.py doesn't exist, maybe it's in project_root_abs_path directly.
                 alt_gen_input_json_script_path = os.path.join(project_root_abs_path, "gen_input_json.py")
                 if os.path.exists(alt_gen_input_json_script_path):
                     gen_input_json_script_path = alt_gen_input_json_script_path
                 else:
                     raise FileNotFoundError(f"gen_input_json.py script not found in {src_dir_abs_path} or {project_root_abs_path}")

            # Determine python executable for gen_input_json.py. Assuming it can run in the current env or same as AF3.
            # The script AF3W_wo-tmpl.sh activates 'Draph'. Let's use the current python for simplicity or make it an arg.
            current_python_exec = sys.executable # Or pass as an argument

            print(f"[INFO ] Generating input.json using {gen_input_json_script_path}...")
            # Default all_seeds, from AF3W_wo-tmpl.sh it seems to be an array (1..100). Let's use a few for example.
            # In a real scenario, this should be configurable.
            model_seeds_str = ",".join(map(str, range(1, 6))) # Example: seeds 1 to 5
            gen_json_cmd = [
                current_python_exec, gen_input_json_script_path,
                "--input_fasta_file", input_fasta_abs_path,
                "--model_seeds", model_seeds_str, # Needs to be a comma-separated string for the script
                "--output_json_file", input_json_for_af3_path
            ]
            print(f"  > cmd: {' '.join(gen_json_cmd)}")
            try:
                subprocess.run(gen_json_cmd, check=True, capture_output=True, text=True)
                print(f"[INFO ] Successfully generated {input_json_for_af3_path}")
            except subprocess.CalledProcessError as e:
                print(f"[ERROR] Failed to generate input.json for AlphaFold3.")
                print(f"[ERROR] Command: {' '.join(e.cmd)}")
                print(f"[ERROR] Return code: {e.returncode}")
                print(f"[ERROR] stdout: {e.stdout}")
                print(f"[ERROR] stderr: {e.stderr}")
                raise RuntimeError(f"Failed to generate input.json for AlphaFold3: {e.stderr}")
            
            print(f"[INFO ] Running AlphaFold3 data pipeline for {system_name}...")
            os.makedirs(af3_output_root_abs_path, exist_ok=True)
            af3_cmd = [
                af3_python_exec, "-u", af3_script_path,
                "--run_inference=false",
                "--run_data_pipeline=true",
                "--json_path", input_json_for_af3_path, # Use the generated input.json
                "--output_dir", af3_output_root_abs_path
            ]
            print(f"  > AF3 cmd: {' '.join(af3_cmd)}")
            try:
                # Using Popen for potentially long-running process and stream output if desired
                process = subprocess.Popen(af3_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                stdout, stderr = process.communicate()
                if process.returncode != 0:
                    print(f"[ERROR] AlphaFold3 data pipeline failed for {system_name}.")
                    print(f"[ERROR] stdout: {stdout}")
                    print(f"[ERROR] stderr: {stderr}")
                    raise RuntimeError(f"AlphaFold3 data pipeline failed: {stderr}")
                print(f"[INFO ] AlphaFold3 data pipeline completed for {system_name}. Check {target_data_json_path}")
            except Exception as e:
                raise RuntimeError(f"Error executing AlphaFold3 data pipeline: {e}")
        else:
            print(f"[INFO ] Found existing AlphaFold3 _data.json at {target_data_json_path}. Skipping generation.")

        # After ensuring _data.json is present, expect A3M file in msa_output_dir_name
        msa_dir_abs_path = os.path.join(project_root_abs_path, msa_output_dir_name)
        target_a3m_path = os.path.join(msa_dir_abs_path, f"{system_name_lower}.a3m")

        print(f"[INFO ] Attempting to load MSA from A3M file: {target_a3m_path}")
        if not os.path.exists(target_a3m_path):
            # AF3W_wo-tmpl.sh has a colabfold_batch fallback here. 
            # For this method, we will state that A3M must exist.
            error_msg = (
                f"Expected A3M file not found: {target_a3m_path}.\n"
                f"The AlphaFold3 data pipeline (_data.json located at {target_data_json_path}) should have been run, "
                f"and an A3M file should be generated (e.g., by colabfold_batch or other means) "
                f"in the MSA directory."
            )
            # Try to find the .a3m file using colabfold_batch naming convention if original name fails
            # Example: <msa_dir_abs_path>/<system_name_fasta_header_prefix>.a3m
            # Sometimes colabfold_batch might create <fasta_filename_without_ext>.a3m
            # This part could be expanded if more sophisticated search is needed.
            print(f"[WARNING] {error_msg}")
            # As per plan, strict check for now.
            raise FileNotFoundError(error_msg)

        return cls.from_file(target_a3m_path, msa_format='a3m')

    @classmethod
    def from_sequences_and_descriptions(
        cls,
        sequences: List[str], # List of amino acid sequences
        descriptions: List[str], # List of sequence descriptions
        msa_format: str = "custom" # Format identifier
    ) -> 'MSA':
        """
        Creates an MSA object from lists of sequences and descriptions.

        Parameters
        ----------
        sequences (List[str]):
            A list of protein sequences.
        descriptions (List[str]):
            A list of descriptions corresponding to each sequence.
        msa_format (str):
            An identifier for the MSA's origin or format.

        Returns
        -------
        MSA:
            A new MSA object.
        """
        return cls(sequences=sequences, descriptions=descriptions, msa_format=msa_format)

    def to_file(
        self,
        output_path: str, # Path to save the MSA file
        msa_format: Optional[str] = None # The format to save the MSA in. Defaults to original format.
    ):
        """
        Saves the MSA to a file.

        Parameters
        ----------
        output_path (str):
            The path where the MSA file will be saved.
        msa_format (Optional[str]):
            The format to save the MSA in (e.g., 'fasta', 'a3m').
            If None, uses the MSA's original format or defaults to 'fasta'.
        
        Raises
        ------
        ValueError:
            If the specified output format is not supported.
        """
        output_format = msa_format if msa_format is not None else self._msa_format
        if output_format == "unknown" and msa_format is None:
            output_format = "fasta" # Default to FASTA if original format is unknown

        content = ""
        if output_format == "fasta":
            content = _write_fasta(self._sequences, self._descriptions)
        elif output_format == "a3m":
            content = _write_a3m(self._sequences, self._descriptions)
        else:
            raise ValueError(f"Unsupported MSA output format: {output_format}")

        with open(output_path, 'w') as f:
            f.write(content)

    def get_num_sequences(self) -> int:
        """
        Returns the number of sequences in the MSA.

        Returns
        -------
        int:
            The total number of sequences.
        """
        return len(self._sequences)

    def get_sequences(self) -> List[str]:
        """
        Returns the list of sequences.

        Returns
        -------
        List[str]:
            A list of all sequences.
        """
        return self._sequences[:] # Return a copy

    def get_descriptions(self) -> List[str]:
        """
        Returns the list of sequence descriptions.

        Returns
        -------
        List[str]:
            A list of all sequence descriptions.
        """
        return self._descriptions[:] # Return a copy

    def get_sequence_at(self, index: int) -> Tuple[str, str]:
        """
        Retrieves a sequence and its description at a specific index.

        Parameters
        ----------
        index (int):
            The index of the sequence to retrieve.

        Returns
        -------
        Tuple[str, str]:
            A tuple containing the description and the sequence (description, sequence).
            
        Raises
        ------
        IndexError:
            If the index is out of bounds.
        """
        if 0 <= index < len(self._sequences):
            return self._descriptions[index], self._sequences[index]
        raise IndexError("Sequence index out of range.")
    
    def __len__(self) -> int:
        """Returns the number of sequences in the MSA."""
        return self.get_num_sequences()

    def __getitem__(self, index: int) -> Tuple[str, str]:
        """Allows dictionary-style access to sequences: msa_obj[index] -> (description, sequence)"""
        return self.get_sequence_at(index)

    def __repr__(self) -> str:
        """Returns a string representation of the MSA object."""
        num_seqs = self.get_num_sequences()
        if num_seqs > 0:
            avg_len = sum(len(s) for s in self._sequences) / num_seqs if num_seqs > 0 else 0
            # Make sure sequences are not empty before calculating min/max length
            min_len_val = min(len(s) for s in self._sequences) if self._sequences and all(s for s in self._sequences) else 0
            max_len_val = max(len(s) for s in self._sequences) if self._sequences and all(s for s in self._sequences) else 0
            return f"<MSA object: {num_seqs} sequences, format='{self._msa_format}', avg_len={avg_len:.2f}, min_len={min_len_val}, max_len={max_len_val}>"
        return f"<MSA object: 0 sequences, format='{self._msa_format}'>"

    def get_sequence_lengths(self) -> List[int]:
        """
        Returns a list of lengths for each sequence in the MSA.

        Returns
        -------
        List[int]:
            A list containing the length of each sequence.
        """
        return [len(s) for s in self._sequences]

    def get_subset(
        self,
        indices: List[int] # A list of indices for sequences to include in the subset
    ) -> 'MSA':
        """
        Creates a new MSA object containing only the sequences at the specified indices.

        Parameters
        ----------
        indices (List[int]):
            A list of integer indices specifying which sequences to include.

        Returns
        -------
        MSA:
            A new MSA object representing the subset.

        Raises
        ------
        IndexError:
            If any index is out of bounds.
        """
        subset_sequences = []
        subset_descriptions = []
        for i in indices:
            if not (0 <= i < len(self._sequences)):
                raise IndexError(f"Index {i} is out of bounds for MSA with {len(self._sequences)} sequences.")
            subset_sequences.append(self._sequences[i])
            subset_descriptions.append(self._descriptions[i])
        
        # Preserve the format, or mark as 'subset' or 'custom'
        new_format = self._msa_format if self._msa_format != "unknown" else "subset"
        return MSA(sequences=subset_sequences, descriptions=subset_descriptions, msa_format=new_format)

    def sample(
        self,
        num_samples: int, # The number of sequences to sample
        random_seed: Optional[int] = None # Optional random seed for reproducibility
    ) -> 'MSA':
        """
        Randomly samples a specified number of sequences from the MSA.

        Parameters
        ----------
        num_samples (int):
            The number of sequences to sample.
        random_seed (Optional[int]):
            An optional random seed to ensure reproducibility of the sampling.

        Returns
        -------
        MSA:
            A new MSA object containing the sampled sequences.
        
        Raises
        ------
        ValueError:
            If num_samples is greater than the total number of sequences or less than 0.
        """
        import random

        num_total_sequences = self.get_num_sequences()
        if not (0 <= num_samples <= num_total_sequences):
            raise ValueError(
                f"Number of samples ({num_samples}) must be between 0 and {num_total_sequences}."
            )

        if random_seed is not None:
            random.seed(random_seed)

        if num_samples == num_total_sequences:
            return MSA(sequences=self._sequences[:], descriptions=self._descriptions[:], msa_format=self._msa_format)
        if num_samples == 0:
            return MSA(sequences=[], descriptions=[], msa_format=self._msa_format)

        sampled_indices = random.sample(range(num_total_sequences), num_samples)
        
        sampled_sequences = [self._sequences[i] for i in sampled_indices]
        sampled_descriptions = [self._descriptions[i] for i in sampled_indices]
        
        new_format = self._msa_format if self._msa_format != "unknown" else "sampled_subset"
        return MSA(sequences=sampled_sequences, descriptions=sampled_descriptions, msa_format=new_format)

    def filter_by_length(
        self,
        min_len: Optional[int] = None, # Minimum sequence length (inclusive)
        max_len: Optional[int] = None # Maximum sequence length (inclusive)
    ) -> 'MSA':
        """
        Filters sequences in the MSA based on their length.

        Parameters
        ----------
        min_len (Optional[int]):
            The minimum length for a sequence to be included. If None, no minimum length filter is applied.
        max_len (Optional[int]):
            The maximum length for a sequence to be included. If None, no maximum length filter is applied.

        Returns
        -------
        MSA:
            A new MSA object containing only the sequences that meet the length criteria.
            
        Raises
        ------
        ValueError:
            If min_len is greater than max_len (and both are specified).
        """
        if min_len is not None and max_len is not None and min_len > max_len:
            raise ValueError(f"min_len ({min_len}) cannot be greater than max_len ({max_len}).")

        filtered_sequences = []
        filtered_descriptions = []

        for seq, desc in zip(self._sequences, self._descriptions):
            length = len(seq)
            passes_min = True if min_len is None else length >= min_len
            passes_max = True if max_len is None else length <= max_len
            if passes_min and passes_max:
                filtered_sequences.append(seq)
                filtered_descriptions.append(desc)
        
        new_format = self._msa_format if self._msa_format != "unknown" else "filtered_subset"
        return MSA(sequences=filtered_sequences, descriptions=filtered_descriptions, msa_format=new_format)

    def remove_duplicates(self) -> 'MSA':
        """
        Removes duplicate sequences from the MSA, keeping the first occurrence.

        Returns
        -------
        MSA:
            A new MSA object with duplicate sequences removed.
        """
        unique_sequences = []
        unique_descriptions = []
        seen_sequences = set()

        for seq, desc in zip(self._sequences, self._descriptions):
            if seq not in seen_sequences:
                unique_sequences.append(seq)
                unique_descriptions.append(desc)
                seen_sequences.add(seq)
        
        new_format = self._msa_format if self._msa_format != "unknown" else "deduplicated_subset"
        return MSA(sequences=unique_sequences, descriptions=unique_descriptions, msa_format=new_format)

    def calculate_pairwise_distances(
        self,
        metric: str = 'levenshtein' # Distance metric ('levenshtein' or 'normalized_levenshtein')
    ) -> 'np.ndarray': # Type hint for numpy.ndarray
        """
        Calculates a pairwise distance matrix for all sequences in the MSA.

        Parameters
        ----------
        metric (str):
            The distance metric to use. Supported: 'levenshtein', 'normalized_levenshtein'.

        Returns
        -------
        np.ndarray:
            A 2D NumPy array representing the pairwise distance matrix.
            
        Raises
        ------
        ImportError:
            If required libraries (numpy, python-Levenshtein) are not installed.
        ValueError:
            If an unsupported metric is specified.
        """
        try:
            import numpy as np
        except ImportError:
            raise ImportError("NumPy library is required for distance matrix calculation. Please install it: pip install numpy")
        
        try:
            import Levenshtein
        except ImportError:
            raise ImportError("python-Levenshtein library is required for Levenshtein distance. Please install it: pip install python-Levenshtein")

        num_sequences = self.get_num_sequences()
        if num_sequences == 0:
            return np.array([])
        
        distance_matrix = np.zeros((num_sequences, num_sequences), dtype=np.float32)
        sequences = self.get_sequences()

        for i in range(num_sequences):
            for j in range(i + 1, num_sequences):
                s1 = sequences[i]
                s2 = sequences[j]
                dist = 0
                if metric == 'levenshtein':
                    dist = Levenshtein.distance(s1, s2)
                elif metric == 'normalized_levenshtein':
                    # Normalized Levenshtein distance = D(s1,s2) / max(len(s1),len(s2))
                    # Ensures distance is between 0 and 1.
                    len_s1, len_s2 = len(s1), len(s2)
                    if len_s1 == 0 and len_s2 == 0:
                        dist = 0.0
                    elif max(len_s1, len_s2) == 0: # Should be caught by above, but for safety
                        dist = 0.0
                    else:
                        dist = Levenshtein.distance(s1, s2) / max(len_s1, len_s2)
                else:
                    raise ValueError(f"Unsupported distance metric: {metric}. Supported: 'levenshtein', 'normalized_levenshtein'.")
                
                distance_matrix[i, j] = dist
                distance_matrix[j, i] = dist # Distance matrix is symmetric
        
        return distance_matrix

    def cluster_sequences(
        self,
        method: str = 'dbscan', # Clustering method (currently only 'dbscan')
        distance_matrix: Optional['np.ndarray'] = None, # Optional precomputed distance matrix
        distance_metric: str = 'normalized_levenshtein', # Metric if distance_matrix is not provided
        **kwargs # Arguments for the clustering algorithm (e.g., eps, min_samples for DBSCAN)
    ) -> Tuple[List[int], Optional[object]]: # Returns cluster labels and the clustering model/result
        """
        Clusters sequences in the MSA using the specified method.

        Parameters
        ----------
        method (str):
            The clustering method to use. Currently supports 'dbscan'.
        distance_matrix (Optional[np.ndarray]):
            An optional precomputed pairwise distance matrix. If None, it will be calculated
            using the `distance_metric`.
        distance_metric (str):
            The metric to use for calculating distances if `distance_matrix` is not provided.
            Default is 'normalized_levenshtein'.
        **kwargs:
            Keyword arguments to pass to the clustering algorithm.
            For DBSCAN: `eps` (float, default 0.5), `min_samples` (int, default 5).

        Returns
        -------
        Tuple[List[int], Optional[object]]:
            A tuple containing:
            - A list of cluster labels for each sequence (-1 for noise points in DBSCAN).
            - The fitted clustering model object (e.g., DBSCAN instance), or None if not applicable.

        Raises
        -------
        ImportError:
            If required libraries (scikit-learn, numpy) are not installed.
        ValueError:
            If an unsupported clustering method is specified or if arguments are invalid.
        """
        num_sequences = self.get_num_sequences()
        if num_sequences == 0:
            return [], None
        
        try:
            import numpy as np
            from sklearn.cluster import DBSCAN
        except ImportError:
            raise ImportError("scikit-learn and NumPy are required for clustering. Please install them: pip install scikit-learn numpy")

        if distance_matrix is None:
            if num_sequences < 2 : # Not enough sequences to form pairs for distance matrix
                 # For DBSCAN, if min_samples is 1, every point is its own cluster.
                 # If min_samples > 1, all points might become noise.
                 # Handle this gracefully depending on DBSCAN params.
                 if method.lower() == 'dbscan':
                    min_samples = kwargs.get('min_samples', 5)
                    if num_sequences == 1 and min_samples ==1:
                        return [0], None # Single sequence in its own cluster
                    else:
                        return [-1] * num_sequences, None # All noise or single noise point
                 else:
                    return [0] * num_sequences, None # Default to single cluster if method is unknown and few seqs

            print(f"Calculating pairwise distances using '{distance_metric}' for {num_sequences} sequences...")
            dm = self.calculate_pairwise_distances(metric=distance_metric)
        else:
            if not isinstance(distance_matrix, np.ndarray) or distance_matrix.shape != (num_sequences, num_sequences):
                raise ValueError(
                    f"Provided distance_matrix must be a NumPy array of shape ({num_sequences}, {num_sequences})."
                )
            dm = distance_matrix

        if method.lower() == 'dbscan':
            eps = kwargs.get('eps', 0.5)  # Default eps for normalized Levenshtein might be around 0.1-0.3
            min_samples = kwargs.get('min_samples', 5)
            
            # DBSCAN requires at least min_samples to form a core point.
            # If num_sequences < min_samples, all points might be noise.
            if num_sequences < min_samples and num_sequences > 0:
                print(f"Warning: Number of sequences ({num_sequences}) is less than min_samples ({min_samples}). All points may be classified as noise by DBSCAN.")
                # Depending on desired behavior, could return all noise or adjust parameters.
                # For now, proceed with DBSCAN; it will likely label them as noise.

            print(f"Running DBSCAN with eps={eps}, min_samples={min_samples}...")
            dbscan_model = DBSCAN(eps=eps, min_samples=min_samples, metric='precomputed')
            try:
                cluster_labels = dbscan_model.fit_predict(dm)
            except Exception as e:
                 # Catch potential issues if dm is all zeros (e.g., identical sequences) for certain eps values.
                 print(f"Error during DBSCAN fitting: {e}. This can happen with highly similar sequences or specific eps values.")
                 # Fallback: assign all to one cluster or all as noise if problematic.
                 if np.all(dm < eps):
                     print("All pairwise distances are less than eps. Assigning all sequences to cluster 0.")
                     cluster_labels = np.zeros(num_sequences, dtype=int).tolist()
                 else:
                     print("Assigning all sequences as noise due to DBSCAN error.")
                     cluster_labels = np.full(num_sequences, -1, dtype=int).tolist()
                 return cluster_labels, None

            n_clusters = len(set(label for label in cluster_labels if label != -1))
            n_noise = list(cluster_labels).count(-1)
            print(f"DBSCAN found {n_clusters} clusters and {n_noise} noise points.")
            return cluster_labels.tolist(), dbscan_model
        else:
            raise ValueError(f"Unsupported clustering method: {method}. Supported: 'dbscan'.")


# Helper functions for parsing and writing MSA formats

def _parse_fasta(
    fasta_string: str # The string content of a FASTA file
) -> Tuple[List[str], List[str]]:
    """
    Parses a FASTA formatted string into sequences and descriptions.

    Parameters
    ----------
    fasta_string (str):
        The content of a FASTA file as a single string.

    Returns
    -------
    Tuple[List[str], List[str]]:
        A tuple containing two lists: sequences and descriptions.
    """
    sequences: List[str] = []
    descriptions: List[str] = []
    current_sequence_lines: List[str] = []

    for line in fasta_string.splitlines():
        line = line.strip()
        if not line:
            continue
        if line.startswith(">"):
            if current_sequence_lines: # If there's a sequence buffered
                sequences.append("".join(current_sequence_lines))
                current_sequence_lines = []
            descriptions.append(line[1:]) # Store description without '>'
        else:
            # Validate sequence characters (basic check)
            if not re.match(r"^[A-Z*\-.]*$", line.upper()):
                # Allow standard amino acids, gaps, and sometimes stop codons or unknown
                # This check can be made more stringent if necessary
                pass # For now, allow potentially non-standard characters
            current_sequence_lines.append(line)
    
    if current_sequence_lines: # Append the last sequence
        sequences.append("".join(current_sequence_lines))

    if len(descriptions) != len(sequences):
         # This can happen if a FASTA file ends with a description line but no sequence,
         # or if there are sequences without preceding description lines.
         # Depending on strictness, one might raise an error or try to reconcile.
         # For now, we'll assume a basic well-formed FASTA.
         # If descriptions are more, trim the last one. If sequences are more, it's a bigger issue.
        if len(descriptions) > len(sequences) and not sequences: # Only descriptions, no sequences
             descriptions = [] # Or raise error
        elif len(descriptions) > len(sequences) and descriptions[-1] and not current_sequence_lines:
             descriptions = descriptions[:-1] # Last description had no sequence
        # Add more sophisticated error handling or correction if needed
    
    return sequences, descriptions


def _write_fasta(
    sequences: List[str], # List of amino acid sequences
    descriptions: List[str] # List of sequence descriptions
) -> str:
    """
    Writes sequences and descriptions to a FASTA formatted string.

    Parameters
    ----------
    sequences (List[str]):
        A list of protein sequences.
    descriptions (List[str]):
        A list of descriptions for each sequence.

    Returns
    -------
    str:
        A string in FASTA format.
    """
    fasta_string_parts: List[str] = []
    for desc, seq in zip(descriptions, sequences):
        fasta_string_parts.append(f">{desc}")
        # Typically, FASTA lines are wrapped at a certain length (e.g., 60 or 80 chars)
        for i in range(0, len(seq), 60):
            fasta_string_parts.append(seq[i:i+60])
    return "\n".join(fasta_string_parts)


def _parse_a3m(
    a3m_string: str # The string content of an A3M file
) -> Tuple[List[str], List[str]]:
    """
    Parses an A3M formatted string into sequences and descriptions.
    A3M format:
    >description
    SEQUENCE_LINE_1 (may contain lowercase for inserts)
    SEQUENCE_LINE_2
    ...
    >description_2
    SEQUENCE_LINE_1_FOR_SEQ_2
    ...

    This is a simplified parser. A3M can have comment lines starting with #.
    The first sequence is often the query, and its >description line might be omitted
    if the file starts with #A3M#.

    Parameters
    ----------
    a3m_string (str):
        The content of an A3M file as a single string.

    Returns
    -------
    Tuple[List[str], List[str]]:
        A tuple containing two lists: sequences and descriptions.
        Sequences will have insertions (lowercase letters) removed and kept as uppercase.
    """
    sequences: List[str] = []
    descriptions: List[str] = []
    current_sequence_lines: List[str] = []
    
    lines = a3m_string.splitlines()
    
    # Handle potential #A3M# header
    if lines and lines[0].startswith("#A3M#"):
        lines.pop(0) # Remove header line

    for line in lines:
        line = line.strip()
        if not line or line.startswith("#"): # Skip empty lines and comments
            continue
        
        if line.startswith(">"):
            if current_sequence_lines:
                # Process the sequence: remove insertions (lowercase chars)
                raw_sequence = "".join(current_sequence_lines)
                processed_sequence = "".join([char for char in raw_sequence if not char.islower()])
                sequences.append(processed_sequence)
                current_sequence_lines = []
            descriptions.append(line[1:])
        else:
            current_sequence_lines.append(line)
            
    if current_sequence_lines: # Append the last sequence
        raw_sequence = "".join(current_sequence_lines)
        processed_sequence = "".join([char for char in raw_sequence if not char.islower()])
        sequences.append(processed_sequence)

    # A3M query sequence might not have a description line if it's the first one
    # and there was no #A3M# line. This parser assumes descriptions always exist if sequences exist.
    # If the first sequence had no ">" line before it, it might be missed or misaligned.
    # A more robust A3M parser would handle these edge cases.
    # For now, we rely on the structure of >desc\nSEQ.
    
    # Ensure descriptions match sequences. If there's one more sequence than description,
    # it's likely the first sequence (query) didn't have a description line.
    # This is a common scenario in some A3M files.
    if len(sequences) == len(descriptions) + 1 and not descriptions: # Only one sequence, no description
        descriptions.append("query_sequence_1") # Add a default description
    elif len(sequences) > len(descriptions):
        # If multiple sequences and descriptions are mismatched, it's harder to guess.
        # For now, if the first sequence seems to be missing a description:
        if descriptions and sequences[0] and not descriptions[0].startswith(lines[0][1:] if lines and lines[0].startswith(">") else ""):
             # This condition is a bit complex. A simpler approach for now:
             # If num_seq > num_desc, pad descriptions.
             num_to_pad = len(sequences) - len(descriptions)
             for i in range(num_to_pad):
                 descriptions.insert(i, f"unnamed_sequence_{i+1}") # Pad at the beginning or make more sophisticated

    # Or, if descriptions exist for all but one sequence, try to ensure correct alignment.
    # The current parser is basic and might misalign if the first seq has no ">".

    # A common A3M structure:
    # >query
    # ABC
    # >hit1
    # ADC
    # This is handled.
    # Another common structure (e.g. from HHblits):
    # ACES # (no ">", this is the query sequence, often without inserts)
    # >hit1
    # ADCS
    # The current parser would miss the first sequence "ACES" because it doesn't start with ">".
    # And if it did process it, it would strip lowercase, which is not typical for the query itself.

    # For simplicity, this parser will only correctly parse A3M files where each sequence block
    # (including the query) starts with a ">" description line.
    # And it will strip lowercase characters (insertions relative to query) from ALL sequences.

    # If lengths mismatch after parsing, it indicates a format deviation not handled.
    if len(sequences) != len(descriptions):
        # Heuristic: if only one sequence and no description, assume it's a query
        if len(sequences) == 1 and not descriptions:
            descriptions.append("query_sequence")
        # Heuristic: if descriptions are one less than sequences, assume query description was missing
        elif len(descriptions) == len(sequences) -1:
            descriptions.insert(0, "query_sequence") # Add to the beginning
        else:
            # For more complex mismatches, raise error or log warning
            # print(f"Warning: A3M parsing resulted in {len(sequences)} sequences and {len(descriptions)} descriptions. Alignment might be off.")
            # Truncate to the shorter list to maintain 1:1 mapping, or raise error
            min_len = min(len(sequences), len(descriptions))
            sequences = sequences[:min_len]
            descriptions = descriptions[:min_len]
            if not sequences: # If this results in no sequences, it's a failure
                 raise ValueError("Failed to parse A3M: No valid sequence-description pairs found.")
                 
    return sequences, descriptions


def _write_a3m(
    sequences: List[str], # List of amino acid sequences (assumed to be aligned, without lowercase insertions)
    descriptions: List[str] # List of sequence descriptions
) -> str:
    """
    Writes sequences and descriptions to an A3M formatted string.
    Note: This basic writer does not re-introduce lowercase for insertions
    as that information is typically lost during parsing by _parse_a3m (or not present).
    It writes a FASTA-like A3M.

    Parameters
    ----------
    sequences (List[str]):
        A list of protein sequences.
    descriptions (List[str]):
        A list of descriptions for each sequence.

    Returns
    -------
    str:
        A string in A3M format.
    """
    a3m_string_parts: List[str] = ["#A3M#"] # Start with A3M header
    for desc, seq in zip(descriptions, sequences):
        a3m_string_parts.append(f">{desc}")
        a3m_string_parts.append(seq) # In A3M, sequence usually on one line after description
    return "\n".join(a3m_string_parts) 