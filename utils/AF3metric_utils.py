import os, sys
import numpy as np
from typing import List, Dict, Union

import Bio
from Bio.PDB import MMCIF<PERSON>ars<PERSON>


def extract_atom_plddt_AF3(
    mmcif_file: str, # Path to the MMCIF file.
    target_chain: str, # Target chain identifier.
    target_residues: List[int] # List of target residue numbers.
)-> Dict[int, List[Dict[str, Union[int, float, tuple]]]]:
    """
    Extract per-atom pLDDT values, atom indices, and coordinates for specific chain and residue numbers.
    [TODO] 나중에 residue_atom_data 를 받아서 처리하는 방식으로 변경해야 함.

    Args:
        mmcif_file (str): Path to the MMCIF file.
        target_chain (str): Target chain identifier.
        target_residues (List[int]): List of target residue numbers.

    Returns:
        dict: A dictionary where keys are residue numbers, and values are lists of dictionaries containing:
            - atom_index (int): The index of the atom.
            - plddt (float): The per-atom pLDDT value.
            - coordinates (tuple): The 3D coordinates (x, y, z) of the atom.
    """
    # Parse the MMCIF file
    parser = MMCIFParser(QUIET=True)
    structure = parser.get_structure("structure", mmcif_file)

    # Dictionary to store results
    residue_atom_data = {res: [] for res in target_residues}

    # Iterate over all models, chains, residues, and atoms
    for model in structure:
        for chain in model:
            if chain.id != target_chain:
                continue  # Skip chains that do not match the target

            for residue in chain:
                # Check if the residue number is in the target list
                residue_number = residue.id[1]
                if residue_number not in target_residues:
                    continue  # Skip residues that are not in the target list

                # Extract atom information
                for atom in residue:
                    atom_index = atom.serial_number  # Atom index
                    plddt = atom.bfactor  # Bio.PDB uses `bfactor` to store pLDDT
                    coordinates = atom.coord  # Atom coordinates (x, y, z)

                    # Append the data as a dictionary
                    residue_atom_data[residue_number].append({
                        "atom_index": atom_index,
                        "plddt": plddt,
                        "coords": tuple(coordinates)  # Convert numpy array to tuple
                    })

    return residue_atom_data


def calculate_interface_pae(
    pdb_file: str,  # Path to structure file.
    contact_atoms: List[int],  # Indices of atoms in the interface
    E1: dict, E2: dict,  # Domain data structures
    target_chain_data: dict,  # Data for the target chain
    other_chains_data: dict,  # Data for all other chains
    confidences_json: dict  # The confidences.json data
) -> Dict[str, Union[int, float]]:
    """
    Calculate Predicted Aligned Error (PAE) statistics for interface residues between protein chains.

    Parameters
    ----------
    pdb_file (str):
        Path to the structure file.
    contact_atoms (List[int]):
        List of atom indices that are part of the interface.
    E1 (dict):
        Data structure representing the first domain.
    E2 (dict):
        Data structure representing the second domain.
    target_chain_data (dict):
        Dictionary containing atomic data for the target chain, organized by residue number.
    other_chains_data (dict):
        Dictionary containing atomic data for all other chains, organized by chain ID and residue number.
    confidences_json (dict):
        Dictionary containing confidence metrics from AlphaFold3, including PAE matrix.

    Returns
    -------
    Dict[str, Union[int, float]]:
        Dictionary containing PAE statistics for the interface:
        - 'min_pae': Minimum PAE value at the interface
        - 'median_pae': Median PAE value at the interface
        - 'mean_pae': Mean PAE value at the interface
        - 'max_pae': Maximum PAE value at the interface
        - 'interface_residues': Number of residues at the interface
    """
    try:
        # Extract PAE matrix from confidences_json
        pae_matrix = np.array(confidences_json['pae'])
        
        # Generate mapping tables for 
        # - atom to residue
        # - residue to chain
        # - chain to residue
        # - residue to atom
        token_res_ids = confidences_json['token_res_ids']
        token_chain_ids = confidences_json['token_chain_ids']
        
        # Create atom-to-residue mapping for all atoms
        atom_to_residue = {}
        
        # Map atoms in target chain to their respective residues
        for res_num, atoms in target_chain_data.items():
            for atom in atoms:
                atom_idx = atom['atom_index'] 
                atom_to_residue[atom_idx] = {'chain': 'A', 'residue': res_num}
        
        # Map atoms in other chains to their respective residues
        for chain_id, chain_data in other_chains_data.items():
            for res_num, atoms in chain_data.items():
                for atom in atoms:
                    atom_idx = atom['atom_index']
                    atom_to_residue[atom_idx] = {'chain': chain_id, 'residue': res_num}
                
        # Identify interface residues from contact atoms (using a set to avoid duplicates)
        # This will be a set of tuples (chain, residue)
        interface_residues = set()
        for atom_idx in contact_atoms:
            if atom_idx in atom_to_residue:
                chain = atom_to_residue[atom_idx]['chain']
                residue = atom_to_residue[atom_idx]['residue']
                interface_residues.add((chain, residue))
        
        # Find corresponding indices in the pAE matrix for each interface residue
        pae_indices = []
        for chain, residue in interface_residues:
            # Find the index of each residue in the token lists
            for i, (res_id, chain_id) in enumerate(zip(token_res_ids, token_chain_ids)):
                if res_id == residue and chain_id == chain:
                    pae_indices.append(i)
                    break
        
        # Extract PAE values between interface residues
        if len(pae_indices) > 1:
            # Create a submatrix containing only pAE values for interface residues
            interface_pae = np.zeros((len(pae_indices), len(pae_indices)))
            for i, idx1 in enumerate(pae_indices):
                for j, idx2 in enumerate(pae_indices):
                    interface_pae[i, j] = pae_matrix[idx1, idx2]
                    interface_pae[j, i] = pae_matrix[idx2, idx1]
            
            # Calculate PAE statistics (excluding diagonal elements which represent self-PAE)
            off_diag_indices = ~np.eye(len(pae_indices), dtype=bool)
            interface_pae_values = interface_pae[off_diag_indices]
            
            # Return rounded statistics
            return {
                'min_pae': round(np.min(interface_pae_values), 3),
                'median_pae': round(np.median(interface_pae_values), 3),
                'mean_pae': round(np.mean(interface_pae_values), 3),
                'max_pae': round(np.max(interface_pae_values), 3),
                'interface_residues': len(interface_residues)
            }
        else:
            return {
                'min_pae': 0, 'median_pae': 0, 'mean_pae': 0,  'max_pae': 0, 'interface_residues': 0
            }
    except Exception as e:
        print(f"[ERROR] calculating interface PAE for {pdb_file}: {e}")
        return {
            'min_pae': 0, 'median_pae': 0, 'mean_pae': 0, 'max_pae': 0, 'interface_residues': 0
        }