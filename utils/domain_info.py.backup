#!/usr/bin/env python3
import os
import json
import logging
from typing import Dict, List, Any, Union, Optional, Tuple

# Define APP isoform segments with domain boundaries
APP_ISOFORM_SEGMENTS = {
    '770': {
        'E1_start': 1, 'E1_end': 162, 
        'KPI_start': 263, 'KPI_end': 313, 'has_KPI': True,
        'OX2_start': 316, 'OX2_end': 336, 'has_OX2': True,
        'E2_start': 347, 'E2_end': 538,
    },
    '751': {
        'E1_start': 1, 'E1_end': 162, 
        'KPI_start': 263, 'KPI_end': 313, 'has_KPI': True,
        'OX2_start': 316, 'OX2_end': 336, 'has_OX2': True,
        'E2_start': 328, 'E2_end': 519,
    },
    '695': {
        'E1_start': 1, 'E1_end': 162, 
        'E2_start': 272, 'E2_end': 463,
        'has_KPI': False, 'has_OX2': False
    },
    None: {
        'E1_start': 0, 'E1_end': 0, 
        'E2_start': 0, 'E2_end': 0,
        'has_KPI': False,
        'has_OX2': False
    }
}


class DomainInfo:
    """
    Class for handling domain information, including APP isoform domains
    and custom user-defined domains.
    """
    
    def __init__(self, 
                 app_isoform: Optional[str] = None, 
                 custom_domains_file: Optional[str] = None,
                 target_chain: str = 'A'):
        """
        Initialize domain information from either APP isoform or custom domains file.
        
        Parameters
        ----------
        app_isoform (Optional[str]):
            APP isoform identifier ('695', '751', '770'). Default is None.
        custom_domains_file (Optional[str]):
            Path to JSON file containing custom domain information. Default is None.
        target_chain (str):
            Default chain ID for APP domains. Default is 'A'.
        """
        self.app_isoform = app_isoform
        self.custom_domains_file = custom_domains_file
        self.target_chain = target_chain
        
        # Initialize domains
        self.app_domains = self._get_app_domains() if app_isoform else None
        self.custom_domains = self._load_custom_domains() if custom_domains_file else None
        
        # Log initialization results
        if self.app_domains:
            logging.info(f"Using APP isoform {app_isoform}: {self.app_domains}")
            has_kpi = self.app_domains.get('has_KPI', False)
            has_ox2 = self.app_domains.get('has_OX2', False)
            logging.info(f"Domain status - KPI: {'Present' if has_kpi else 'Absent'}, OX2: {'Present' if has_ox2 else 'Absent'}")
            
        if self.custom_domains:
            logging.info(f"Using custom domains from {custom_domains_file}")
            for chain_key, chain_info in self.custom_domains.items():
                chain_id = chain_info['chain_id']
                domains = chain_info['domains']
                logging.info(f"Chain {chain_id} has {len(domains)} domains")
    
    def _get_app_domains(self) -> Dict[str, Any]:
        """
        Get domain information for specified APP isoform.
        
        Returns
        -------
        Dict[str, Any]:
            Dictionary containing domain information
        
        Raises
        ------
        ValueError:
            If the specified APP isoform is not valid
        """
        if self.app_isoform not in APP_ISOFORM_SEGMENTS:
            valid_isoforms = [k for k in APP_ISOFORM_SEGMENTS.keys() if k is not None]
            raise ValueError(f"Invalid APP isoform: {self.app_isoform}. Valid options: {valid_isoforms}")
        
        return APP_ISOFORM_SEGMENTS[self.app_isoform]
    
    def _load_custom_domains(self) -> Dict[str, Dict]:
        """
        Load custom domain information from a JSON file.
        
        Returns
        -------
        Dict[str, Dict]:
            Dictionary containing custom domain information
            
        Raises
        ------
        ValueError:
            If the JSON file is invalid or doesn't contain the expected structure
        """
        try:
            # Check if file exists
            if not os.path.exists(self.custom_domains_file):
                raise ValueError(f"Custom domains file not found: {self.custom_domains_file}")
            
            # Load JSON file
            with open(self.custom_domains_file, 'r') as f:
                custom_domains = json.load(f)
            
            # Validate structure
            if not isinstance(custom_domains, dict):
                raise ValueError("Custom domains must be a dictionary")
            
            # Validate each chain
            for chain_key, chain_info in custom_domains.items():
                if not isinstance(chain_info, dict):
                    raise ValueError(f"Chain info for {chain_key} must be a dictionary")
                    
                if 'chain_id' not in chain_info:
                    raise ValueError(f"Missing 'chain_id' for {chain_key}")
                    
                if 'domains' not in chain_info or not isinstance(chain_info['domains'], list):
                    raise ValueError(f"Missing or invalid 'domains' for {chain_key}")
                    
                for idx, domain in enumerate(chain_info['domains']):
                    if not isinstance(domain, dict):
                        raise ValueError(f"Domain {idx} for {chain_key} must be a dictionary")
                    
                    if 'start' not in domain or 'end' not in domain:
                        raise ValueError(f"Domain {idx} for {chain_key} missing 'start' or 'end'")
                    
                    # Validate start and end are integers
                    if not isinstance(domain['start'], int) or not isinstance(domain['end'], int):
                        raise ValueError(f"Domain {idx} for {chain_key} 'start' and 'end' must be integers")
                    
                    # Validate start <= end
                    if domain['start'] > domain['end']:
                        raise ValueError(f"Domain {idx} for {chain_key} 'start' must be <= 'end'")
            
            logging.info(f"Successfully loaded custom domains from {self.custom_domains_file}")
            return custom_domains
        
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in {self.custom_domains_file}: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error loading custom domains from {self.custom_domains_file}: {str(e)}")
    
    def has_custom_domains(self) -> bool:
        """
        Check if custom domains are available.
        
        Returns
        -------
        bool:
            True if custom domains are available, False otherwise
        """
        return self.custom_domains is not None and len(self.custom_domains) > 0
    
    def get_domain_info(self) -> Dict[str, Any]:
        """
        Get domain information to use for processing.
        
        Returns
        -------
        Dict[str, Any]:
            Domain information (either APP domains or custom domains)
        """
        if self.has_custom_domains():
            return {
                'use_custom_domains': True,
                'custom_domains': self.custom_domains,
                'target_chain': self.target_chain
            }
        else:
            return {
                'use_custom_domains': False, 
                'app_domains': self.app_domains,
                'target_chain': self.target_chain
            }


def create_example_custom_domains_file(output_file: str) -> None:
    """
    Create an example custom domains JSON file.
    
    Parameters
    ----------
    output_file (str):
        Path to the output file
    """
    example = {
        "target_chain1": {
            "chain_id": "C",
            "domains": [
                {"start": 1, "end": 5},
                {"start": 6, "end": 14}
            ]
        },
        "target_chain2": {
            "chain_id": "D",
            "domains": [
                {"start": 1, "end": 5},
                {"start": 6, "end": 14}
            ]
        }
    }
    
    with open(output_file, 'w') as f:
        json.dump(example, f, indent=2)
    
    print(f"Example custom domains file created: {output_file}")


if __name__ == "__main__":
    # Create example custom domains file if run directly
    import sys
    
    if len(sys.argv) > 1:
        output_file = sys.argv[1]
    else:
        output_file = "example_custom_domains.json"
    
    create_example_custom_domains_file(output_file) 