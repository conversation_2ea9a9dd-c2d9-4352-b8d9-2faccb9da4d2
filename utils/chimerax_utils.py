import os
import subprocess
import tempfile
from typing import Optional, List, Dict, Any, Tuple
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import logging

def verify_chimerax_installation() -> bool:
    """
    Verify that ChimeraX is installed and accessible.
    
    Returns
    -------
    bool:
        True if ChimeraX is installed and accessible, False otherwise.
    """
    try:
        process = subprocess.run(
            ['which', 'chimerax'], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True
        )
        return process.returncode == 0
    except Exception:
        return False

def align_structure_with_chimerax(
    reference_path: str,  # Path to reference structure file
    model_path: str,      # Path to model structure file to be aligned
    output_path: str,     # Path to save aligned structure
    ref_chain: str = "A", # Chain ID in reference structure
    model_chain: str = "A", # Chain ID in model structure
    pairing_method: str = "bs", # Pairing method: bs (best-score) or la (linear alignment)
    timeout: int = 300    # Timeout in seconds
) -> bool:
    """
    Align a model structure to a reference structure using ChimeraX.
    
    Parameters
    ----------
    reference_path (str):
        Path to the reference structure file (.pdb or .cif format).
    model_path (str):
        Path to the model structure file to be aligned (.pdb or .cif format).
    output_path (str):
        Path where the aligned structure will be saved (.pdb format).
    ref_chain (str, optional):
        Chain ID in the reference structure to use for alignment. Default is "A".
    model_chain (str, optional):
        Chain ID in the model structure to use for alignment. Default is "A".
    pairing_method (str, optional):
        Method for pairing residues: "bs" (best-score) or "la" (linear alignment).
        Default is "bs".
    timeout (int, optional):
        Maximum time in seconds to wait for ChimeraX to complete. Default is 300.
        
    Returns
    -------
    bool:
        True if alignment was successful, False otherwise.
    """
    # Check if input files exist
    if not os.path.exists(reference_path):
        print(f"Error: Reference file does not exist: {reference_path}")
        return False
        
    if not os.path.exists(model_path):
        print(f"Error: Model file does not exist: {model_path}")
        return False
    
    # Create ChimeraX script in a temporary file
    script_content = f"""
# Open reference and model structures
open {reference_path}
open {model_path}

# Align model to reference
matchmaker #2/{model_chain} to #1/{ref_chain} pairing {pairing_method}

# Save aligned model
save {output_path} #2 format pdb
exit
"""
    
    # Create a temporary file for the script
    with tempfile.NamedTemporaryFile(mode='w', suffix='.cxc', delete=False) as temp_file:
        temp_file.write(script_content)
        script_path = temp_file.name
    
    try:
        # Run ChimeraX with the script
        cmd = ['chimerax', '--nogui', '--silent', script_path]
        process = subprocess.run(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            timeout=timeout,
            text=True
        )
        
        # Check if the command was successful and the output file exists
        if process.returncode == 0 and os.path.exists(output_path):
            return True
        else:
            # If something went wrong, log the error
            print(f"ChimeraX error: {process.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"Error: ChimeraX alignment timed out after {timeout} seconds")
        return False
    except Exception as e:
        print(f"Error during ChimeraX alignment: {e}")
        return False
    finally:
        # Clean up the temporary script file
        if os.path.exists(script_path):
            os.unlink(script_path)

def align_worker(args: Tuple) -> Tuple[str, bool, str]:
    """
    Worker function for parallel alignment.
    
    Parameters
    ----------
    args (tuple):
        Tuple containing (model_path, reference_path, output_path, ref_chain, model_chain, pairing_method)
        
    Returns
    -------
    tuple:
        (model_path, success, output_path)
    """
    model_path, reference_path, output_path, ref_chain, model_chain, pairing_method, timeout = args
    success = align_structure_with_chimerax(
        reference_path=reference_path,
        model_path=model_path,
        output_path=output_path,
        ref_chain=ref_chain,
        model_chain=model_chain,
        pairing_method=pairing_method,
        timeout=timeout
    )
    return (model_path, success, output_path)

def parallel_align_structures(
    model_paths: List[str],
    reference_path: str,
    output_dir: str = None,
    ref_chain: str = "A",
    model_chain: str = "A",
    pairing_method: str = "bs",
    max_workers: int = None,
    timeout: int = 300,
    use_processes: bool = False
) -> Dict[str, str]:
    """
    Align multiple structures in parallel using ThreadPoolExecutor or ProcessPoolExecutor.
    
    Parameters
    ----------
    model_paths (List[str]):
        List of paths to model structure files to be aligned.
    reference_path (str):
        Path to the reference structure file.
    output_dir (str, optional):
        Directory to save aligned structures. If None, will use the same directory as model files.
    ref_chain (str, optional):
        Chain ID in the reference structure to use for alignment. Default is "A".
    model_chain (str, optional):
        Chain ID in model structures to use for alignment. Default is "A".
    pairing_method (str, optional):
        Method for pairing residues. Default is "bs" (best-score).
    max_workers (int, optional):
        Maximum number of worker threads or processes. If None, will use default (CPU count).
    timeout (int, optional):
        Maximum time in seconds to wait for each ChimeraX process. Default is 300.
    use_processes (bool, optional):
        If True, use ProcessPoolExecutor instead of ThreadPoolExecutor. Default is False.
        Note: ProcessPoolExecutor may be faster for CPU-bound tasks, but uses more memory.
        
    Returns
    -------
    Dict[str, str]:
        Dictionary mapping original model paths to aligned model paths of successfully aligned models.
    """
    if not verify_chimerax_installation():
        print("Error: ChimeraX is not installed or not accessible")
        return {}
    
    # Prepare alignment tasks
    tasks = []
    aligned_models = {}
    
    # Create output directory if specified and doesn't exist
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    for model_path in model_paths:
        # Skip if model file doesn't exist
        if not os.path.exists(model_path):
            print(f"Warning: Model file {model_path} does not exist, skipping")
            continue
        
        # Determine output path
        if output_dir:
            # Use specified output directory with original filename
            base_name = os.path.basename(model_path)
            output_path = os.path.join(output_dir, base_name.replace('.cif', '_align.pdb'))
        else:
            # Use the same directory as model file
            output_path = model_path.replace('.cif', '_align.pdb')
        
        # Skip if output file already exists (already aligned)
        if os.path.exists(output_path):
            print(f"Info: Output file {output_path} already exists, skipping alignment")
            aligned_models[model_path] = output_path
            continue
        
        # Add to tasks list
        tasks.append((model_path, reference_path, output_path, ref_chain, model_chain, pairing_method, timeout))
    
    # If all models are already aligned, return early
    if not tasks:
        print("Info: All models are already aligned or don't exist")
        return aligned_models
    
    # Start parallel alignment
    total_tasks = len(tasks)
    successful = 0
    failed = 0
    start_time = time.time()
    
    print(f"Starting parallel alignment of {total_tasks} models...")
    
    # Choose executor based on use_processes flag
    executor_class = concurrent.futures.ProcessPoolExecutor if use_processes else concurrent.futures.ThreadPoolExecutor
    
    with executor_class(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_model = {executor.submit(align_worker, task): task[0] for task in tasks}
        
        # Process results as they complete
        for i, future in enumerate(as_completed(future_to_model), 1):
            model_path = future_to_model[future]
            try:
                model_path, success, output_path = future.result()
                if success:
                    aligned_models[model_path] = output_path
                    successful += 1
                    print(f"[{i}/{total_tasks}] Successfully aligned {os.path.basename(model_path)}")
                else:
                    failed += 1
                    print(f"[{i}/{total_tasks}] Failed to align {os.path.basename(model_path)}")
            except Exception as e:
                failed += 1
                print(f"[{i}/{total_tasks}] Error aligning {os.path.basename(model_path)}: {e}")
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    print(f"\nAlignment completed in {elapsed:.2f} seconds")
    print(f"  Successfully aligned: {successful} models")
    print(f"  Failed: {failed} models")
    
    return aligned_models

def write_chimera_cxc(
    model_path: str,
    output_cxc: str = "clash.cxc",
    output_log: str = "clash.log",
    clash_cutoff: float = 0.6,
    hbond_distance: float = 0.4,
    overlap_cutoff: float = 0.6
) -> str:
    """
    Write a ChimeraX script to check for clashes in a structure model.
    
    Parameters
    ----------
    model_path (str):
        Path to the structure model file (.pdb or .cif format).
    output_cxc (str, optional):
        Path to save the ChimeraX script. Default is "clash.cxc".
    output_log (str, optional):
        Path to save the clash detection log. Default is "clash.log".
    clash_cutoff (float, optional):
        Clash detection cutoff value. Default is 0.6.
    hbond_distance (float, optional):
        Hydrogen bond distance for clash detection. Default is 0.4.
    overlap_cutoff (float, optional):
        Atom overlap cutoff for clash detection. Default is 0.6.
        
    Returns
    -------
    str:
        Path to the generated ChimeraX script file.
    """
    script_content = f"""
# Open model
open "{model_path}"

# Check for clashes
clashes #1 overlapCutoff {overlap_cutoff} hbondAllowance {hbond_distance} log true saveFile "{output_log}"

# Exit ChimeraX
exit
"""
    
    with open(output_cxc, 'w') as f:
        f.write(script_content)
    
    return output_cxc

def check_clash_chimeraX(
    model_path: str,
    output_dir: str = None,
    output_log: str = None,
    clash_cutoff: float = 0.6,
    hbond_distance: float = 0.4,
    overlap_cutoff: float = 0.6,
    timeout: int = 300
) -> Tuple[bool, str]:
    """
    Check for clashes in a structure model using ChimeraX.
    
    Parameters
    ----------
    model_path (str):
        Path to the structure model file (.pdb or .cif format).
    output_dir (str, optional):
        Directory to save output files. If None, uses the model's directory.
    output_log (str, optional):
        Path to save the clash detection log file. If None, a default path is used.
    clash_cutoff (float, optional):
        Clash detection cutoff value. Default is 0.6.
    hbond_distance (float, optional):
        Hydrogen bond distance for clash detection. Default is 0.4.
    overlap_cutoff (float, optional):
        Atom overlap cutoff for clash detection. Default is 0.6.
    timeout (int, optional):
        Maximum time in seconds to wait for ChimeraX to complete. Default is 300.
        
    Returns
    -------
    Tuple[bool, str]:
        (success, output_log_path) where success is True if the check was successful,
        and output_log_path is the path to the clash detection log file.
    """
    if not verify_chimerax_installation():
        print("Error: ChimeraX is not installed or not accessible")
        return (False, "")
    
    # Determine output directory
    if output_dir is None:
        output_dir = os.path.dirname(model_path)
        if not output_dir:
            output_dir = "."
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Set output paths
    model_name = os.path.basename(model_path).split('.')[0]
    output_cxc = os.path.join(output_dir, f"{model_name}_clash.cxc")
    
    if output_log is None:
        output_log = os.path.join(output_dir, f"{model_name}_clash.log")
    
    # Write ChimeraX script
    write_chimera_cxc(
        model_path=model_path,
        output_cxc=output_cxc,
        output_log=output_log,
        clash_cutoff=clash_cutoff,
        hbond_distance=hbond_distance,
        overlap_cutoff=overlap_cutoff
    )
    
    try:
        # Run ChimeraX with the script
        cmd = ['chimerax', '--nogui', '--silent', output_cxc]
        process = subprocess.run(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            timeout=timeout,
            text=True
        )
        
        # Check if the command was successful and the output file exists
        if process.returncode == 0 and os.path.exists(output_log):
            return (True, output_log)
        else:
            # If something went wrong, log the error
            print(f"ChimeraX error: {process.stderr}")
            return (False, output_log if os.path.exists(output_log) else "")
            
    except subprocess.TimeoutExpired:
        print(f"Error: ChimeraX clash detection timed out after {timeout} seconds")
        return (False, output_log if os.path.exists(output_log) else "")
    except Exception as e:
        print(f"Error during ChimeraX clash detection: {e}")
        return (False, output_log if os.path.exists(output_log) else "")


def process_model_clash(pdb_file: str) -> Tuple[str, str, bool]:
    """
    Process a PDB file and check for clashes, with clash count threshold of 200.
    If a clash log file already exists, it will use that instead of running ChimeraX again.
    
    Parameters
    ----------
    pdb_file (str):
        Path to the PDB file to check.
        
    Returns
    -------
    Tuple[str, str, bool]:
        (msa_dir, pdb_fname, clash_flag) where:
        - msa_dir: Directory containing the PDB file
        - pdb_fname: Filename of the PDB file
        - clash_flag: True if clash count is less than 200, False otherwise
    """
    # Extract directory and filename
    msa_dir = os.path.basename(os.path.dirname(pdb_file))
    pdb_fname = os.path.basename(pdb_file)
    
    # Get output directory and create unique output filenames based on the model name
    output_dir = os.path.dirname(os.path.abspath(pdb_file))
    model_prefix = os.path.splitext(pdb_fname)[0]
    custom_log_path = os.path.join(output_dir, f"{model_prefix}_clash.log")
    
    # Default clash flag to False
    clash_flag = False
    clash_count = 0
    
    # Check if clash log already exists (cache hit)
    if os.path.exists(custom_log_path):
        #print(f"[INFO] Found existing clash log for {pdb_fname} - using cached result") # for debug
        try:
            with open(custom_log_path, 'r') as f:
                for line in f:
                    if ":" in line and "overlap =" in line:
                        clash_count += 1
            
            # Set clash_flag to True if clash count is less than 200
            if clash_count < 200:
                clash_flag = True
                
            #print(f"[INFO] {pdb_fname} has {clash_count} clashes from cache - Passed: {clash_flag}")
            return (msa_dir, pdb_fname, clash_flag)
        except Exception as e:
            print(f"[WARNING] Error reading cached clash log for {pdb_file}: {e}")
            print(f"[INFO] Will recalculate clash detection")
            # Fall through to recalculate if there's an error reading the cache
    
    # No cache or cache read error - run ChimeraX
    success, log_path = check_clash_chimeraX(
        model_path=os.path.abspath(pdb_file),
        output_dir=output_dir,
        output_log=custom_log_path
    )
    
    # Check if there are clashes
    if success and os.path.exists(log_path):
        try: # Count the number of clashes
            with open(log_path, 'r') as f:
                for line in f:
                    if ":" in line and "overlap =" in line:
                        clash_count += 1
            
            # Set clash_flag to True if clash count is less than 200
            if clash_count < 200:
                clash_flag = True
                
            # print(f"[INFO] {pdb_fname} has {clash_count} clashes - Passed: {clash_flag}")
        except Exception as e:
            print(f"[ERROR] Error processing clash log for {pdb_file}: {e}")
    else:
        print(f"[WARNING] Failed to generate clash log for {pdb_file}")
    
    return (msa_dir, pdb_fname, clash_flag)

