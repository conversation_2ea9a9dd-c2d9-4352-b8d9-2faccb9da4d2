# MSA Clustering Pipeline Configuration
# =================================

# General Settings
general:
  output_base_dir: "./clustering_results"
  chain_poly_type: "protein"
  max_depth: null  # null means no limit
  verbose: false
  preserve_directory_structure: true  # Keep {filename}_{encoding}_{clustering} naming

# Encoding Methods Configuration
encoding_methods:
  available: ["onehot", "MSAtransformer"]
  default: ["onehot"]
  
  # One-hot encoding settings
  onehot:
    alphabet: "ACDEFGHIKLMNPQRSTVWY-X"
    
  # MSA Transformer settings  
  MSAtransformer:
    model_name: "esm_msa1b_t12_100M_UR50S"
    repr_layer: 12
    device: null  # null means auto-detect
    remove_lowercase: false
    remove_special_chars: true
    batch_size: 100

# Clustering Methods Configuration
clustering_methods:
  available: ["dbscan", "hierarchical", "hdbscan"]
  default: ["dbscan"]

# Algorithm-specific Parameters
algorithm_parameters:
  
  # DBSCAN Configuration
  dbscan:
    gap_cutoff: 0.25
    encoding_method: "onehot"
    remove_lowercase: false
    remove_special_chars: true
    
    # Parameter optimization ranges
    optimization:
      min_eps: 3.0
      max_eps: 20.0
      eps_step: 0.5
      min_samples: [3, 5, 7, 10]
      
    # Default parameters (used if optimization fails)
    defaults:
      eps: 6.0
      min_samples: 3

  # Hierarchical Clustering Configuration  
  hierarchical:
    gap_cutoff: 0.25
    encoding_method: "onehot"
    remove_lowercase: false
    remove_special_chars: true
    
    # Parameter optimization
    optimization:
      min_cluster_size: 19
      max_clusters_to_test: 50
      
    # Default parameters
    defaults:
      n_clusters: 10
      linkage: "ward"
      metric: "euclidean"

  # HDBSCAN Configuration
  hdbscan:
    gap_cutoff: 0.25
    encoding_method: "onehot"
    
    # Default parameters (no optimization implemented yet)
    defaults:
      min_cluster_size: 15
      min_samples: null
      cluster_selection_epsilon: 0.0
      cluster_selection_method: "eom"

# Output Configuration
output_settings:
  save_cluster_a3m_files: true
  save_analysis_results: true
  save_visualizations: true
  create_summary_report: true
  
  # File naming
  cluster_filename_format: "{filename_base}_{cluster_id:03d}.a3m"
  
  # Directory structure
  subdirectories:
    clusters: "clusters"
    analysis: "analysis" 
    visualizations: "visualizations"

# Analysis Configuration
analysis_settings:
  calculate_quality_metrics: true
  generate_composition_analysis: true
  create_parameter_optimization_log: true
  
  # Quality metrics
  quality_metrics:
    silhouette_score: true
    calinski_harabasz_score: true
    davies_bouldin_score: true

# Visualization Configuration  
visualization_settings:
  create_msa_plots: true
  similarity_method: "identity"
  cluster_sequences: true
  
  # Error handling for visualization failures
  fallback_to_text_summary: true

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  date_format: "%Y-%m-%d %H:%M:%S" 