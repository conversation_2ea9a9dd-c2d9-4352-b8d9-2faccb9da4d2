general:
  output_base_dir: './pipeline_test_results'
  chain_poly_type: 'protein'
  max_depth: 100  # Limit for faster testing

encoding_methods:
  default: ['onehot']

clustering_methods:
  default: ['dbscan']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 10.0
      min_samples: 5

output_settings:
  save_cluster_a3m_files: true
  create_summary_report: true

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'

logging:
  level: 'INFO' 