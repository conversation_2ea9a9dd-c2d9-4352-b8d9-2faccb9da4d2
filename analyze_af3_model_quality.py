import os, sys
import logging
import argparse
import itertools

import numpy as np
import pandas as pd

import collections
from collections import defaultdict
from typing import Optional, Dict, Any, List, Tuple

import matplotlib.pyplot as plt
import seaborn as sns

from scipy import stats

# Import logger configuration
from logger_config import create_hierarchical_logger, MAIN_INFO, SUB_INFO

# Initialize logger with hierarchical logging capabilities
logger = create_hierarchical_logger(
    target_dir=None,  # Will be set in main
    console_level=MAIN_INFO
)


def find_protein_system_dir(
    target_dir: str, # The target directory to search for the protein system directory. (e.g. .../AF3/)
    path_suffix: str # The suffix of the directory to search for (e.g., 'Full_wo_tmpl').
) -> str:
    """
    Dynamically find the protein system directory in the target directory.
    
    Parameters
    ----------
    target_dir (str):
        The target directory to search for the protein system directory.
    path_suffix (str):
        The suffix of the directory to search for (e.g., 'Full_wo_tmpl').   
        
    Returns
    -------
    protein_system_dir (str):
        The protein system directory.
    """
    logger.main_info(f"Finding protein system directory in {target_dir}")
    full_dir_path = os.path.join(target_dir, f'Full_{path_suffix}')
    logger.sub_info(f"Checking directory: {full_dir_path}")
    
    if os.path.exists(full_dir_path):
        # List all subdirectories in Full_{path_suffix}
        subdirs = [
            d for d in os.listdir(full_dir_path) if os.path.isdir(os.path.join(full_dir_path, d))
        ]
        
        if subdirs:
            if len(subdirs) == 1:
                # If there's only one subdirectory, use it
                protein_system_dir = subdirs[0]
                logger.sub_info(f"Auto-detected protein system directory: {protein_system_dir}")
            else:
                # If multiple subdirectories exist, ask user to select or use the first one
                logger.warning(f"Multiple protein system directories found: {', '.join(subdirs)}")
                logger.sub_info(f"Using the first one: {subdirs[0]}")
                protein_system_dir = subdirs[0]
        else:
            logger.error(f"No protein system directories found in {full_dir_path}")
            sys.exit(1)
    else:
        logger.error(f"Full_{path_suffix} directory not found in {target_dir}")
        sys.exit(1)
        
    return protein_system_dir

def collect_model_metrics(
    metric_file_path: str,  # Path to the combined metric file (e.g., 'metric_sorted_combined.tsv')
    normalize: bool = False,  # Option to normalize numeric metrics
    scale_method: str = 'minmax',  # Scaling method: 'minmax' or 'standard'
    compute_summary: bool = True  # Compute summary statistics
) -> Optional[pd.DataFrame]:
    """
    Reads the combined model metrics TSV file into a pandas DataFrame.

    This function loads the TSV file generated by Rank_AF3.py, which contains
    various quality metrics for each predicted model across different MSA subsets.
    It performs basic validation and type conversion.

    Parameters
    ----------
    metric_file_path (str):
        The full path to the 'metric_sorted_combined.tsv' file.
    normalize (bool, optional):
        Whether to normalize numeric metrics. Default is False.
    scale_method (str, optional):
        Method for scaling: 'minmax' (0-1) or 'standard' (z-score). Default is 'minmax'.
    compute_summary (bool, optional):
        Whether to compute summary statistics. Default is True.

    Returns
    -------
    Optional[pd.DataFrame]:
        A pandas DataFrame containing the model metrics, or None if the file
        cannot be read or is empty.
    """
    
    if not os.path.exists(metric_file_path):
        logger.error(f"Metric file not found: {metric_file_path}")
        return None

    try:
        logger.main_info(f'Reading the TSV file: {os.path.basename(metric_file_path)}')
        df_metrics = pd.read_csv(metric_file_path, sep='\t', engine='python')

        if df_metrics.empty:
            logger.warning(f"Metric file is empty: {metric_file_path}")
            return None
        logger.sub_info(f"Successfully loaded {len(df_metrics)} records from {metric_file_path}")


        logger.main_info(f'Converting columns to numeric types')
        numeric_cols = [
            'Mean_ipAE', 'Median_ipAE', 'Min_ipAE', 'Max_ipAE', 'avg_ipLDDT', 'avg_domain_pLDDT', 'ipTM', 'pTM'
        ]
        for col in numeric_cols:
            if col in df_metrics.columns:
                df_metrics[col] = pd.to_numeric(df_metrics[col], errors='coerce')
                # Check if conversion introduced NaNs where they shouldn't be
                if df_metrics[col].isnull().any():
                    logger.warning(f"Column '{col}' contains non-numeric values or NaNs after conversion.")

        ## [TODO] 이후에 수정 -------------------- ##
        # Extract MSA subset index if needed, assuming 'msasub' column exists
        if 'msasub' in df_metrics.columns:
            # Extracts the number from 'msasub_X'
            df_metrics['msasub_index'] = df_metrics['msasub'].str.extract(r'(\d+)$').astype(int)
            logger.sub_info(f"Added 'msasub_index' column derived from 'msasub'.")

        # Add derived quality score - a combination of ipLDDT and pTM
        quality_cols = ['avg_ipLDDT', 'ipTM']
        if all(col in df_metrics.columns for col in quality_cols):
            # Create combined quality score (average of normalized ipLDDT and ipTM)
            df_metrics['combined_quality'] = (df_metrics['avg_ipLDDT'] / 100 + df_metrics['ipTM']) / 2
            logger.sub_info(f"Added 'combined_quality' score derived from ipLDDT and ipTM.")
        ## [TODO] 이후에 수정 -------------------- ##

        if compute_summary:
            logger.main_info(f'Computing summary statistics')
            try:
                summary_stats = {}
                for col in numeric_cols:
                    if col in df_metrics.columns:
                        summary_stats[f"{col}_mean"] = df_metrics[col].mean()
                        summary_stats[f"{col}_median"] = df_metrics[col].median()
                        summary_stats[f"{col}_std"] = df_metrics[col].std()
                        summary_stats[f"{col}_min"] = df_metrics[col].min()
                        summary_stats[f"{col}_max"] = df_metrics[col].max()
                
                # Add summary stats as attributes to the DataFrame
                df_metrics.attrs['summary_stats'] = summary_stats
                logger.sub_info(f"Added summary statistics as DataFrame attributes")
            except Exception as e:
                logger.error(f"Error computing summary statistics: {e}")

        logger.sub_info(f"DataFrame columns: {df_metrics.columns.tolist()}")
        return df_metrics

    except pd.errors.EmptyDataError:
        logger.error(f"Metric file is empty or contains no data: {metric_file_path}")
        return None
    except Exception as e:
        logger.error(f"Error reading or processing metric file {metric_file_path}: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def filter_models_by_quality(
    df_metrics: pd.DataFrame,
    quality_threshold: Dict[str, float] = None,
    top_n: int = None,
    selection_criteria: str = 'combined_quality'
) -> pd.DataFrame:
    """
    Filters models based on quality thresholds or selects top N models.
    
    Parameters
    ----------
    df_metrics (pd.DataFrame):
        DataFrame containing model metrics from collect_model_metrics()
    quality_threshold (Dict[str, float], optional):
        Dictionary mapping column names to minimum threshold values
    top_n (int, optional):
        Number of top models to select based on selection_criteria
    selection_criteria (str, optional):
        Column to use for ranking when selecting top_n models

    Returns
    -------
    pd.DataFrame:
        Filtered DataFrame containing models meeting the criteria
    """
    if df_metrics is None or df_metrics.empty:
        logger.error("Cannot filter: DataFrame is None or empty")
        return pd.DataFrame()
        
    logger.main_info(f"Filtering models based on quality criteria")
    filtered_df = df_metrics.copy()
    
    # Apply quality thresholds if provided
    if quality_threshold:
        logger.sub_info(f"Applying quality thresholds: {quality_threshold}")
        for col, threshold in quality_threshold.items():
            if col in filtered_df.columns:
                filtered_df = filtered_df[filtered_df[col] >= threshold]
                logger.sub_info(f"Applied filter: {col} >= {threshold}, {len(filtered_df)} models remaining")
            else:
                logger.warning(f"Column {col} not found in DataFrame, skipping this filter")
    
    # Select top N models if specified
    if top_n and selection_criteria in filtered_df.columns:
        logger.sub_info(f"Selecting top {top_n} models based on {selection_criteria}")
        filtered_df = filtered_df.sort_values(by=selection_criteria, ascending=False).head(top_n)
        logger.sub_info(f"Selected {len(filtered_df)} models")
    
    logger.main_info(f"Filtering complete: {len(filtered_df)} models meet criteria")
    return filtered_df


def create_cluster_model_mapping(
    cluster_a3m_path: str,  # Path to the directory containing chain subdirectories (e.g., 'AF_Cluster/Complex_APP695_A_B')
    chain_ids: List[str],   # List of chain identifiers (e.g., ['A', 'B'])
    n_clusters: int,        # Number of cluster .a3m files used per chain type for combinations
    output_prefix: str,     # Prefix used for the output MSAsub directories (e.g., "MSAsub")
    use_paired: bool        # Flag indicating if paired MSAs were used in combinations (matches combinate_a3m_json.py)
) -> Dict[str, Tuple[Tuple[str, str], ...]]:
    """
    Reconstructs the mapping between MSA subset indices and the specific
    combination of paired/unpaired clustered MSA (.a3m) files used.

    This function mimics the combination logic of 'combinate_a3m_json.py'
    to determine which cluster files correspond to each MSAsub_X directory.

    Parameters
    ----------
    cluster_a3m_path (str):
        Path to the directory containing chain-specific subdirectories
        (e.g., 'chainA_paired', 'chainA_unpaired').
    chain_ids (List[str]):
        List of chain identifiers used in the project (e.g., ['A', 'B']).
    n_clusters (int):
        The maximum number of cluster .a3m files considered from each
        paired/unpaired directory per chain during combination generation.
    output_prefix (str):
        The prefix used when creating the MSAsub_X directories (e.g., "MSAsub").
    use_paired (bool):
        Flag indicating whether paired MSAs were included in the combination
        process (should match the setting used in combinate_a3m_json.py).
        Note: Current logic assumes pairs of (paired, unpaired) files per chain.

    Returns
    -------
    Dict[str, Tuple[Tuple[str, str], ...]]:
        A dictionary mapping the MSA subset directory name (e.g., "MSAsub_0")
        to the tuple of (paired_a3m_file, unpaired_a3m_file) paths used for
        that combination, ordered by chain_ids.
        Returns an empty dict if directories or files are not found as expected.

    Raises
    -------
    FileNotFoundError:
        If the base cluster_a3m_path does not exist.
    ValueError:
        If expected subdirectories or .a3m files are missing for a chain.
    """
    logger.main_info(f'Creating cluster-model mapping for path: {cluster_a3m_path}')
    logger.sub_info(f'Parameters: chain_ids={chain_ids}, n_clusters={n_clusters}, output_prefix="{output_prefix}", use_paired={use_paired}')
    if not os.path.exists(cluster_a3m_path):
        logger.error(f"Cluster A3M path not found: {cluster_a3m_path}")
        raise FileNotFoundError(f"Cluster A3M path not found: {cluster_a3m_path}")

    chain_combinations_by_id = defaultdict(list)
    processed_chain_ids = [] # Keep track of the order chains are processed

    # Expected directory naming convention based on combinate_a3m_json.py and typical usage
    # [TODO ] Modify if your naming convention is different
    mapping = {chr(65 + i): 101 + i for i in range(26)}
    dir_patterns = {
        'paired': "../../MSA_JKHmmer/chain{}_paired",
        'unpaired': "../../MSA_JKHmmer/chain{}"
    }    

    for chain_id in sorted(chain_ids): # Process chains in a consistent order
        logger.main_info(f'Processing chain {chain_id}')
        paired_dir_name = dir_patterns['paired'].format(mapping[chain_id])
        unpaired_dir_name = dir_patterns['unpaired'].format(mapping[chain_id])

        paired_dir_path = os.path.join(cluster_a3m_path, paired_dir_name)
        unpaired_dir_path = os.path.join(cluster_a3m_path, unpaired_dir_name)        

        if not os.path.isdir(paired_dir_path):
            raise ValueError(f"Paired directory not found for chain {chain_id}: {paired_dir_path}")
        if not os.path.isdir(unpaired_dir_path):
            raise ValueError(f"Unpaired directory not found for chain {chain_id}: {unpaired_dir_path}")


        logger.sub_info(f'Getting .a3m files from paired_dir, sorted, limited by n_clusters')
        paired_files = sorted([
            os.path.join(paired_dir_path, f) for f in os.listdir(paired_dir_path)
                if f.endswith('.a3m') and "U" not in f
        ])[:n_clusters]
        for f in paired_files:
            logger.sub_info(f'- {f}')

        logger.sub_info(f'Getting .a3m files from unpaired_dir, sorted, limited by n_clusters')
        unpaired_files = sorted([
            os.path.join(unpaired_dir_path, f) for f in os.listdir(unpaired_dir_path)
                if f.endswith('.a3m') and "U" not in f
        ])[:n_clusters]
        for f in unpaired_files:
            logger.sub_info(f'- {f}')
        
        assert len(paired_files) >= 1, f"No valid paired .a3m files found for chain {chain_id} in {paired_dir_path}"
        assert len(unpaired_files) >= 1, f"No valid unpaired .a3m files found for chain {chain_id} in {unpaired_dir_path}"

        # Generate combinations of (paired_file, unpaired_file) for this chain
        # This matches the inner loop structure implied by itertools.product in combinate_a3m_json.py
        chain_combinations = list(itertools.product(paired_files, unpaired_files))
        chain_combinations_by_id[chain_id].extend(chain_combinations)
        processed_chain_ids.append(chain_id)
        logger.sub_info(f"Generated {len(chain_combinations)} (paired, unpaired) combinations for chain {chain_id}")

    # Check if we found combinations for any chains
    if not chain_combinations_by_id:
        logger.error("No valid chain combinations could be generated. Check directory structure and file names.")
        return {}

    logger.main_info(f"Successfully processed chains: {processed_chain_ids}")

    # Generate final combinations across all processed chains using itertools.product
    # The order matches the sorted(chain_ids) used above
    final_combinations = list(itertools.product(*[chain_combinations_by_id[chain_id] for chain_id in processed_chain_ids]))
    logger.main_info(f"Total cross-chain combinations generated: {len(final_combinations)}")

    # Create the final mapping from MSAsub index to the combination tuple
    msasub_mapping = {}
    for idx, combination in enumerate(final_combinations):
        msasub_name = f"{output_prefix}_{idx}"
        # combination will be like: ((chainA_paired_path, chainA_unpaired_path), (chainB_paired_path, chainB_unpaired_path), ...)
        msasub_mapping[msasub_name] = combination
        logger.sub_info(f"Mapping {msasub_name} -> {combination}")

    logger.main_info(f"Successfully created mapping for {len(msasub_mapping)} MSA subsets.")
    return msasub_mapping


def plot_model_quality_histograms(
    df_metrics: pd.DataFrame, 
    msa_clustering: bool = False,  # Flag to indicate if clustering is used
) -> None:
    """
    Plots histograms for selected quality metric columns with distribution annotations.
    
    This function plots a histogram for each quality metric column:
      - 'Mean_ipAE', 'Median_ipAE', 'Max_ipAE', 'avg_ipLDDT', 'avg_domain_pLDDT', 'ipTM', 'pTM'
    
    Each plot is annotated with the mean, median, and Q3 values.
    
    The histogram binning is calculated using the Freedman-Diaconis Rule.
    """
    
    mode_str = "clustered MSA" if msa_clustering else "full MSA"
    logger.main_info(f"Creating quality metric histograms for {mode_str}")
    
    quality_cols = [
        'Mean_ipAE', 'Median_ipAE', 'Max_ipAE', 'avg_ipLDDT', 'avg_domain_pLDDT', 'ipTM', 'pTM'
    ]
    for col in quality_cols:
        if col in df_metrics.columns:
            try:
                logger.sub_info(f"Plotting histogram for {col}")
                plt.figure(figsize=(8, 6))
                data = df_metrics[col] # .dropna()
                
                # Calculate bin number using Freedman-Diaconis Rule
                q75, q25 = np.percentile(data, [75, 25])
                iqr = q75 - q25
                bin_width = 2 * iqr / (len(data) ** (1/3))
                bins = int((data.max() - data.min()) / bin_width) if bin_width > 0 else 'auto'
                
                sns.histplot(data, kde=True, bins=bins)
                plt.title(f"{col} distribution from AF3Cluster")
                plt.xlabel(col)
                plt.ylabel("Frequency")
                
                # Calculate statistics
                mean_val = np.mean(data)
                median_val = np.median(data)
                annotation_text = f"Mean: {mean_val:.2f}\nMedian: {median_val:.2f},\nQ3: {q75:.2f}"
                
                # Add annotation at the upper right corner
                plt.text(0.95, 0.95, annotation_text, horizontalalignment='right',
                        verticalalignment='top', transform=plt.gca().transAxes,
                        bbox=dict(facecolor='white', alpha=0.5))
                
                plt.tight_layout()
                fig_name = f"{col}_histogram_cluster.png" if msa_clustering else f"{col}_histogram_whole.png"
                plt.savefig(fig_name)
                logger.sub_info(f"Histogram saved as '{fig_name}'")
                plt.close()
            except Exception as e:
                logger.error(f"Failed to plot histogram for {col}: {e}")
                logger.sub_info(f"{col} values: {df_metrics[col].head()}")
        else:
            logger.warning(f"Column {col} not found in DataFrame.")
    
    logger.main_info(f"Completed quality metric histograms for {mode_str}")


def plot_quality_scatter_matrix(
    df_metrics: pd.DataFrame, 
    msa_clustering: bool = False,  # Flag to indicate if clustering is used
) -> None:
    """
    Plots an N x N scatter matrix for the selected quality metric columns using hexbin plots.
    
    Each subplot is a hexbin plot between two metrics, annotated with Pearson correlation 
    coefficient (r) and coefficient of determination (R²).
    Models with clashes (if 'clash' column exists and is True) are skipped.
    
    Parameters
    ----------
    df_metrics (pd.DataFrame):
        DataFrame containing model quality metrics
    msa_clustering (bool, optional):
        Flag to indicate if MSA clustering was used, affects output filename
    
    """
    mode_str = "clustered MSA" if msa_clustering else "full MSA"
    logger.main_info(f"Creating quality scatter matrix for {mode_str}")
    
    # Filter out clash models if column exists
    if 'clash' in df_metrics.columns:
        orig_count = len(df_metrics)
        df_metrics = df_metrics[df_metrics['clash'] != True]
        logger.sub_info(f"Filtered out clash models: {orig_count - len(df_metrics)} removed, {len(df_metrics)} remaining")
    
    quality_cols = [
        'Mean_ipAE', 'Median_ipAE', 'Min_ipAE', 'Max_ipAE', 'avg_ipLDDT', 'avg_domain_pLDDT', 'ipTM', 'pTM'
    ]

    # Check only available columns
    available_cols = [col for col in quality_cols if col in df_metrics.columns]
    n = len(available_cols)
    if n == 0:
        logger.error("No quality metric columns available for scatter matrix.")
        return
    
    logger.sub_info(f"Creating {n}x{n} scatter matrix with columns: {available_cols}")
    fig, axes = plt.subplots(n, n, figsize=(4*n, 4*n))
    for i, col_x in enumerate(available_cols):
        for j, col_y in enumerate(available_cols):
            ax = axes[i, j] if n > 1 else axes
            
            # If same column, display histogram on diagonal
            if i == j:
                sns.histplot(df_metrics[col_x].dropna(), ax=ax, kde=False)
                ax.set_xlabel(col_x)
                ax.set_ylabel("Count")
            else:
                # Drop NaN values for correlation calculation
                valid_data = df_metrics[[col_x, col_y]].dropna()
                
                # Calculate Pearson correlation coefficient and p-value
                if len(valid_data) >= 2:  # Need at least 2 points for correlation
                    r, p_value = stats.pearsonr(valid_data[col_x], valid_data[col_y])
                    # Calculate R² (coefficient of determination) - square of Pearson's r
                    r_squared = r**2
                    
                    # Format the correlation values
                    corr_text = f"r = {r:.2f}\nR² = {r_squared:.2f}"
                    
                    # Use hexbin for density-based scatter plot
                    hb = ax.hexbin(
                        valid_data[col_x], valid_data[col_y], 
                        gridsize=50, cmap='viridis', mincnt=1, edgecolors='none'
                    )
                    # Add correlation text in the upper left corner
                    ax.text(0.05, 0.95, corr_text, transform=ax.transAxes, 
                           va='top', ha='left', fontsize=9,
                           bbox=dict(facecolor='white', alpha=0.7))
                    
                    # # Add significance asterisks based on p-value
                    # if p_value < 0.05:
                    #     stars = '*' * sum([p_value < cutoff for cutoff in [0.05, 0.01, 0.001]])
                    #     ax.text(0.05, 0.85, stars, transform=ax.transAxes, 
                    #            va='top', ha='left', fontsize=12)
                else:
                    logger.warning(f"Not enough valid data points for correlation between {col_x} and {col_y}")
                    # Use hexbin for density-based scatter plot without correlation
                    hb = ax.hexbin(
                        df_metrics[col_x], df_metrics[col_y], 
                        gridsize=50, cmap='viridis', mincnt=1, edgecolors='none'
                    )
                
                ax.set_xlabel(col_x)
                ax.set_ylabel(col_y)

    plt.tight_layout()
    fig_name = f"quality_scatter_matrix_cluster.png" if msa_clustering else f"quality_scatter_matrix_whole.png"
    plt.savefig(fig_name)
    plt.close()
    logger.main_info(f"Scatter matrix saved as '{fig_name}'")


def plot_quality_split_violin_plots(
    full_msa_df: pd.DataFrame, # DataFrame containing metrics for models without MSA clustering
    cluster_msa_df: pd.DataFrame, # DataFrame containing metrics for models with MSA clustering
    metrics: List[str] = None, # List of metrics to plot. If None, uses default metrics.
    save_path: str = "quality_metrics_violin_comparison.png" # Path to save the output figure.
) -> None:
    """
    Creates subplots with split violin plots comparing quality metrics between
    non-clustered and clustered MSA approaches. Each metric gets its own subplot.
    
    Parameters:
        full_msa_df (pd.DataFrame): DataFrame containing metrics for models without MSA clustering
        cluster_msa_df (pd.DataFrame): DataFrame containing metrics for models with MSA clustering
        metrics (List[str], optional): List of metrics to plot. If None, uses default metrics.
        save_path (str, optional): Path to save the output figure.
    
    Returns:
        None: The function saves the plot as a file.
    """
    import matplotlib.pyplot as plt
    import seaborn as sns
    from scipy import stats
    import math
    
    # Add clustering flag to each DataFrame
    full_msa_df = full_msa_df.copy()
    cluster_msa_df = cluster_msa_df.copy()
    full_msa_df['clustering'] = 'Full MSA'
    cluster_msa_df['clustering'] = 'Clustered MSA'
    
    # Combine DataFrames
    combined_df = pd.concat([full_msa_df, cluster_msa_df], ignore_index=True)
    
    # Default metrics if none provided
    if metrics is None:
        metrics = [
            'Mean_ipAE', 'Median_ipAE', 'Min_ipAE', 'Max_ipAE', 'avg_ipLDDT',  'avg_domain_pLDDT', 'ipTM', 'pTM', 'combined_quality'
        ]
    
    # Filter only available metrics
    available_metrics = [m for m in metrics if m in combined_df.columns]
    
    if not available_metrics:
        print("[ERROR] No valid metrics found for plotting")
        return
    
    # Define custom color palette
    palette = {"Full MSA": "#3498db", "Clustered MSA": "#e74c3c"}
    
    # Calculate grid dimensions for subplots
    n_metrics = len(available_metrics)
    n_cols = min(3, n_metrics)  # Maximum 3 columns
    n_rows = math.ceil(n_metrics / n_cols)
    
    # Create a figure with subplots
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(n_cols*5, n_rows*4))
    fig.suptitle('Quality Metrics Comparison: Non-Clustered vs. Clustered MSA', fontsize=16, y=0.98)
    
    # Handle different axis array shapes
    if n_rows > 1 or n_cols > 1:
        axes = axes.flatten()
    else:
        axes = [axes]  # Make single axis into list for iteration
    
    # Create violin plots for each metric in its own subplot
    for i, metric in enumerate(available_metrics):
        if i >= len(axes):
            break
            
        ax = axes[i]
        
        # Create violin plot
        sns.violinplot(
            x='clustering', y=metric, 
            data=combined_df,
            ax=ax,
            palette=palette,
            split=True,
            inner='quartile',
            linewidth=1.0
        )
        
        ax.set_title(f"{metric}", fontsize=12)
        
        # Calculate statistics for each group
        full_msa = combined_df[combined_df['clustering'] == 'Full MSA'][metric].dropna()
        clustered_msa = combined_df[combined_df['clustering'] == 'Clustered MSA'][metric].dropna()
        
        # Add summary statistics to plot
        full_msa_mean = full_msa.mean()
        full_msa_median = full_msa.median()
        clustered_msa_mean = clustered_msa.mean()
        clustered_msa_median = clustered_msa.median()
        
        ax.text(0, combined_df[metric].min(), 
                f"Mean: {full_msa_mean:.2f}\nMedian: {full_msa_median:.2f}", 
                ha='center', va='bottom', fontsize=9,
                bbox=dict(facecolor='white', alpha=0.7))
                
        ax.text(1, combined_df[metric].min(), 
                f"Mean: {clustered_msa_mean:.2f}\nMedian: {clustered_msa_median:.2f}", 
                ha='center', va='bottom', fontsize=9,
                bbox=dict(facecolor='white', alpha=0.7))
        
        # Perform t-test
        t_stat, p_val = stats.ttest_ind(full_msa, clustered_msa, equal_var=False)
        
        # Add significance annotation if p-value is significant
        if p_val < 0.05:
            y_max = combined_df[metric].max()
            y_range = combined_df[metric].max() - combined_df[metric].min()
            y_pos = y_max + y_range * 0.05
            
            # Draw a line connecting the violins
            ax.plot([0, 1], [y_pos, y_pos], 'k-', linewidth=1.5)
            
            # Add asterisks based on significance level
            stars = '*' * sum([p_val < cutoff for cutoff in [0.05, 0.01, 0.001]])
            ax.text(0.5, y_pos + y_range * 0.02, stars, ha='center', va='bottom', fontsize=12)
            
            # Add p-value text
            ax.text(0.5, y_pos - y_range * 0.02, f"p={p_val:.3e}", 
                   ha='center', va='top', fontsize=8,
                   bbox=dict(facecolor='white', alpha=0.7))
    
    # Hide unused subplots
    for j in range(len(available_metrics), len(axes)):
        axes[j].set_visible(False)
        
    # Adjust layout
    plt.tight_layout(rect=[0, 0, 1, 0.96])  # Make room for suptitle
    
    # Save figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"[INFO] Split violin plots saved as '{save_path}'")
    plt.close()
    
    # Generate statistics table as a separate figure
    plt.figure(figsize=(len(available_metrics)*1.2, 4))
    plt.axis('tight')
    plt.axis('off')
    
    # Calculate summary statistics for table
    summary_data = []
    for metric in available_metrics:
        full_msa = combined_df[combined_df['clustering'] == 'Full MSA'][metric].dropna()
        clustered_msa = combined_df[combined_df['clustering'] == 'Clustered MSA'][metric].dropna()
        
        t_stat, p_val = stats.ttest_ind(full_msa, clustered_msa, equal_var=False)
        
        row = [
            metric,
            f"{full_msa.mean():.2f} ± {full_msa.std():.2f}",
            f"{clustered_msa.mean():.2f} ± {clustered_msa.std():.2f}",
            f"{p_val:.3e}",
            "*" * sum([p_val < cutoff for cutoff in [0.05, 0.01, 0.001]])
        ]
        summary_data.append(row)
    
    # Create the table
    table = plt.table(
        cellText=summary_data,
        colLabels=['Metric', 'Full MSA (Mean±SD)', 'Clustered MSA (Mean±SD)', 'p-value', 'Sig.'],
        loc='center',
        cellLoc='center'
    )
    
    # Style the table
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 1.5)
    
    plt.savefig(f"{save_path.split('.')[0]}_stats_table.png", dpi=300, bbox_inches='tight')
    print(f"[INFO] Statistical summary table saved as '{save_path.split('.')[0]}_stats_table.png'")
    plt.close()

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Run analysis utilities for AF3 model metrics.")
    parser.add_argument('--target_dir', type=str, required=True, 
                        help="Target directory for inputs (e.g., ~/PROject/APP-complex/Initial_Input/a-secretase/Asec_695E1E2_clvg/AF3/Cluster_wo_tmpl)")
    parser.add_argument('--metric_file', type=str, default="", 
                        help="Path to metric_sorted_combined.tsv file; if not provided, it is assumed to be located in target_dir")    
    parser.add_argument('--use_template', action='store_true', 
                        help="Use custom metric file (if set, use template version; default: False)")
    parser.add_argument('--protein_system', type=str, default="", 
                        help="Protein system name (e.g., a5b1-app695); if not provided, will be auto-detected")
    parser.add_argument('--log_level', type=str, default='INFO',
                        help="Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL")
    args = parser.parse_args()
    
    # Target directory and metric file
    target_dir = os.path.expanduser(args.target_dir)
    
    # Configure logging level
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    console_level = log_level_map.get(args.log_level.upper(), MAIN_INFO)
    
    # Initialize logger with target_dir for log file storage
    logger = create_hierarchical_logger(
        target_dir, 
        "af3_model_quality.log", 
        console_level=console_level
    )
    logger.info(f"Start AF3 model quality analysis")
    logger.main_info(f"Target directory: {target_dir}")
    
    if args.metric_file:
        metric_file = os.path.expanduser(args.metric_file)
    else:
        metric_file = os.path.join(target_dir, 'metric_sorted_combined.tsv')
    logger.sub_info(f"attempting to load metric file: {metric_file}")
    
    logger.info(f"Set w/ or w/o template suffix and Load metrics DataFrame from {metric_file}")
    path_suffix = "w_tmpl" if args.use_template else "wo_tmpl"
    logger.main_info(f"Using path suffix: {path_suffix}")
    
    # Dynamically find the protein system directory if not provided explicitly
    protein_system_dir = args.protein_system
    protein_system_dir = find_protein_system_dir(target_dir, path_suffix) if not protein_system_dir else protein_system_dir
    logger.main_info(f"Use protein system directory: {protein_system_dir}")
        
    # Use the identified protein system directory
    full_msa_metric_file = os.path.join(target_dir, f'Full_{path_suffix}/{protein_system_dir}', 'metric_sorted.tsv')
    if os.path.exists(full_msa_metric_file):
        logger.main_info(f'Loading metrics DataFrame from full MSA file')
        logger.sub_info(f'file path: {full_msa_metric_file}')
        full_msa_metrics_df = collect_model_metrics(full_msa_metric_file)
    else:
        logger.error(f"Metric file '{full_msa_metric_file}' not found.")
        logger.error("Please ensure Rank_AF3.py ran successfully and created the file in the target directory.")
        sys.exit(1)

    cluster_msa_metric_file = os.path.join(target_dir, f'Cluster_{path_suffix}', 'metric_sorted.tsv')
    if os.path.exists(cluster_msa_metric_file):
        logger.main_info(f'Loading metrics DataFrame from clustered MSA file')
        logger.sub_info(f'File path: {cluster_msa_metric_file}')
        cluster_msa_metrics_df = collect_model_metrics(cluster_msa_metric_file)
    else:
        logger.error(f"Metric file '{cluster_msa_metric_file}' not found.")
        logger.error("Please ensure Rank_AF3.py ran successfully and created the file in the target directory.")
        sys.exit(1)
        
    logger.main_info("Plotting quality metric histograms...")
    plot_model_quality_histograms(full_msa_metrics_df, msa_clustering=False)
    plot_model_quality_histograms(cluster_msa_metrics_df, msa_clustering=True)
    
    logger.main_info("Plotting quality metric scatter matrices...")
    plot_quality_scatter_matrix(full_msa_metrics_df, msa_clustering=False)
    plot_quality_scatter_matrix(cluster_msa_metrics_df, msa_clustering=True)

    # Plot split violin plots comparing the two approaches
    logger.main_info("Creating split violin plots to compare clustering vs non-clustering approaches...")
    plot_quality_split_violin_plots(full_msa_metrics_df, cluster_msa_metrics_df)

    # Usage for create_cluster_model_mapping
    logger.main_info("Running cluster model mapping...")
    cluster_a3m_path = target_dir  # The target directory contains chainX_paired/unpaired dirs
    chain_ids = ['A', 'B', 'C', 'D']  # Adjust as needed
    n_clusters = 10  # Adjust if needed
    output_prefix = "MSAsub"  # Default prefix
    use_paired = True  # Assuming paired MSAs were used
    
    logger.sub_info(f"Parameters for create_cluster_model_mapping:")
    logger.sub_info(f"  cluster_a3m_path = {cluster_a3m_path}")
    logger.sub_info(f"  chain_ids = {chain_ids}")
    logger.sub_info(f"  n_clusters = {n_clusters}")
    logger.sub_info(f"  output_prefix = {output_prefix}")
    logger.sub_info(f"  use_paired = {use_paired}")
    logger.sub_info("NOTE: Ensure these parameters match the settings used in combinate_a3m_json.py for this target.")
    
    try:
        mapping = create_cluster_model_mapping(
            cluster_a3m_path=cluster_a3m_path,
            chain_ids=chain_ids,
            n_clusters=n_clusters,
            output_prefix=output_prefix,
            use_paired=use_paired
        )
    
        if mapping:
            logger.main_info(f"Successfully created {len(mapping)} mappings")
        else:
            logger.warning("Failed to generate mapping. Check logs, parameters, and directory contents.")
    
    except FileNotFoundError as e:
        logger.error(f"Error: {e}")
    except ValueError as e:
        logger.error(f"Error: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()
    
    logger.main_info("Analysis completed successfully")

