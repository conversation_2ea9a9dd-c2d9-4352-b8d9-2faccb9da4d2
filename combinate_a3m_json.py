import os, sys
import json
import itertools
import argparse

import collections
from collections import defaultdict

from tqdm import tqdm
from typing import List, Dict, Any, Union, Tuple
from multiprocessing import Pool

from utils.os_utils import read_input
from utils.parallel_utils import get_chunk_size, split_tasks, parallel_process
from struct2seq import StructureSequence<PERSON>apper


def process_task(task):
    return merge_files(*task)

def extract_numbers(input_string):
    return ''.join([char for char in input_string if char.isdigit()])

def seq2data_default():
    return {
        'id': None,
        'a3m': None,
        'templates': [],
        'queryIndices': [],
        'templateIndices': []
    }

def create_a_chain_json(
    sequence: Union[str, List[str]], 
    id: str, 
    modifications: List[Dict[str, Union[str, int]]],  # List of dictionaries with 'ptmType' (str) and 'ptmPosition' (int)
    unpaired_msa: str, 
    paired_msa: str, 
    templates: List[Dict[str, Union[str, float]]],  # List of dictionaries for templates with specific keys
    **kwargs
) -> Dict[str, Any]:
    """
    Create a JSON file for AlphaFold3 input with the specified format.

    Parameters
    -----------
    sequence (str): 
        Protein sequence.
    id (str): 
        Protein ID.
    modifications (List[Dict[str, Union[str, int]]]): 
        List of post-translational modifications. 
        Each dict must contain:
        - "ptmType" (str): Type of modification.
        - "ptmPosition" (int): Position of the modification.
    unpaired_msa (str): 
        Unpaired MSA data as a string.
    paired_msa (str): 
        Paired MSA data as a string.
    templates (List[Dict[str, Union[str, float]]]): 
        List of template information dictionaries. 
        Each dict must contain:
        - Keys like "pdb_id" (str), "chain" (str), and "coverage" (float).
    
    Returns
    -------
        data (Dict[str, Any]): 
            A dictionary representing the AlphaFold3 input in the specified format.
    """    
    return {
        "protein": {
            "id": id,
            "sequence": sequence,
            "modifications": modifications,
            "unpairedMsa": unpaired_msa,
            "pairedMsa": paired_msa,
            "templates": templates,
            **kwargs
        }
    }


def extract_unpaired_msa(
    query_seq: str,  # Query protein sequence
    a3m_file: str,  # Path to the A3M file
) -> List[str]: # List of formatted MSA lines
    """
    Extract unpaired MSA data from an A3M file and format it for AlphaFold3 input.
    
    Parameters
    ----------
    query_seq (str):
        The query protein sequence to be aligned with MSA data.
    a3m_file (str):
        Path to the A3M file containing MSA data.
        
    Returns
    -------
    List[str]:
        A list of formatted MSA lines with the query sequence added as the first entry.
        
    Raises
    ------
    ValueError:
        If the A3M file does not contain valid MSA data (less than 2 lines).
    """
    a3m_content = read_input(a3m_file)
    if len(a3m_content) < 2: 
        raise ValueError(f"The file {a3m_file} does not contain valid MSA data.")

    unpaired_msa = []
    unpaired_msa.append(f'>query\n{query_seq}\n')
    
    # Extract the unpaired MSA from the A3M file
    seq_len = len(query_seq)    
    start_idx = next((idx for idx, token in enumerate(a3m_content[1]) if token != '-'), 0)
    for line in a3m_content:
        line_len = len(line)
        if line.startswith('>'):
            unpaired_msa.append(line)
        else:
            # sequence = line[:-1].strip()
            # for idx, token in enumerate(sequence[::-1]):
            #     if token != '-':
            #         break
            # end_idx = line_len - idx - 1
            
            # query sequence 하고 길이가 같아야 함,  이렇게 안 하면 에러남 
            # 그러면 짧은건 그렇다치고 query sequence보다 길면 어떻게 함? 내가 paired에서 unpaired을 뽑아와서 그런가...?
            #out_line = f'{line[start_idx: start_idx + seq_len]}\n'
            
            out_line = line
            unpaired_msa.append(out_line)    
    return unpaired_msa


def merge_files(
    first_header_line: str,  # First line of the header from MSA file
    seq2data: Dict[str, Dict[str, Any]],  # Dictionary mapping sequences to their data
    use_paired: bool,  # Whether to use paired MSA information
    output_file: str,  # Output file path for the combined data
    use_template: bool,  # Whether to include template information
    model_seeds: List[int],  # List of model seeds for AlphaFold3 simulations
    *input_files: List[Tuple[str, str]]  # List of paired and unpaired MSA file paths
) -> None:
    """
    Merge multiple MSA files and generate JSON output for AlphaFold3.
    
    Parameters
    ----------
    first_header_line (str):
        First line of the header from the original MSA file.
    seq2data (Dict[str, Dict[str, Any]]):
        Dictionary mapping protein sequences to their metadata and template information.
    use_paired (bool):
        Flag indicating whether to include paired MSA data in the output.
    output_file (str):
        Output file path where the merged JSON data will be written.
    use_template (bool):
        Flag indicating whether to include template information in the output.
    model_seeds (List[int]):
        List of model seeds for AlphaFold3 simulations.
    *input_files (List[Tuple[str, str]]):
        Variable length list containing tuples of (paired_msa_file, unpaired_msa_file) paths.
    
    Returns
    -------
    None:
        This function writes output to files and doesn't return any values.
    """
    combined_json = []
    for (seq, data), a3m_f in zip(seq2data.items(), input_files[0]):
        # Read unpaired MSA
        unpaired_msa = extract_unpaired_msa(seq, a3m_f[1])
        unpaired_msa = "".join(unpaired_msa)  # Join lines for the MSA
        
        # Read paired MSA
        #paired_msa = read_input(input_files[0][-1]) if len(input_files[0]) > 1 else ""
        paired_msa = extract_unpaired_msa(seq, a3m_f[0])
        paired_msa = "".join(paired_msa) if use_paired else ""

        # Prepare templates with mmcif, queryIndices, and templateIndices
        templates = [
            {
                "mmcif": template,
                "queryIndices": data.get("queryIndices", []),
                "templateIndices": data.get("templateIndices", [])
            }
            for template in data.get("templates", [])
        ]
        
        # Generate JSON structure
        json_data = create_a_chain_json(
            sequence=seq,
            id=data['id'],
            modifications=[],  # Empty modifications for now
            unpaired_msa=unpaired_msa,
            paired_msa=paired_msa,
            templates=templates
        )
        combined_json.append(json_data)
    
    # Save combined JSON data
    output_json = {
        'version': 1,
        "dialect": 'alphafold3',
        'name': os.path.basename(output_file).replace('.a3m', '').replace('msa', 'MSAsub_'),
        "sequences": combined_json,
        "modelSeeds": model_seeds,
    }
    out_filename = output_file.replace(".a3m", "_tmpl.json") if use_template else output_file.replace(".a3m", ".json")
    with open(out_filename, "w") as json_file:
        json.dump(output_json, json_file, indent=4)        


def main(
    input_path: str,     # Path to directory containing chain subdirectories with MSA files
    input_fasta: str,    # Input FASTA file with protein sequences
    output_prefix: str,  # Prefix for output directories and files
    msa_file: str,       # Path to the MMseqs2 MSA file for extracting header line
    use_paired: bool = False,    # Whether to use clustered paired MSA data
    use_template: bool = False,  # Whether to use template structures
    custom_template_path: Union[List[str], bool] = False,  # Paths to custom template files
    n_clusters: int = 10,  # the # of clusters to use from each chain's MSA files
    model_seeds: List[int] = [1, 100, 1000, 10000, 10000, 100000],  # Model seeds for AlphaFold3
) -> None:
    """
    Process MSA files to generate AlphaFold3 input files by combining paired and unpaired MSAs.    
    """
    mapping = {chr(65 + i): 101 + i for i in range(26)}
    
    print(f'[INFO ] Load sequences in the input fasta')
    input_seqs = [
        ''.join(seq.strip().split()).replace(':', '')
            for seq in read_input(f'{input_path}/../{input_fasta}')[1:] 
            if len(seq) > 5 # minimum length of sequence
    ]
    print(f'  INFO: there are {len(input_seqs)} chains in the {input_fasta}')
    
    # Search for directories containing ".a3m" files
    # [NOTE] keep order of chains!
    a3m_dirs = [ i for i in os.listdir(input_path) if "chain" in i ]      
    a3m_dirs = sorted(a3m_dirs, key=lambda x: (0 if 'paired' in x else 1, x))
    print(f'  INFO: a3m dirs: {a3m_dirs}')
    
    # Initialize seq2data    
    seq2data = defaultdict(seq2data_default)
    for idx, seq in enumerate(input_seqs):
        seq2data[seq]['id'] = chr(ord('A') + idx)
        seq2data[seq]['a3m'] = a3m_dirs[idx] if idx < len(a3m_dirs) else None

    # Use template 
    if use_template and custom_template_path:
        print(f'[INFO ] Use template and custom template structure')
        print(f'[NOTE ] You must match the chain id between sequences and template identifiers!')
        
        mapper = StructureSequenceMapper()
        for idx, (seq, seq_data) in enumerate(seq2data.items()):            
            
            if idx < len(custom_template_path):
                print(f'  INFO: template {custom_template_path[idx]}')
                model_cif = read_input(custom_template_path[idx])
                seq_data['templates'].append("".join(model_cif))                
                
                # aligned_struct, aligned_given, mapping, stats_cif = mapper.map_sequences(
                #     custom_template_path[idx].replace('cif', 'pdb'), seq_data['id'], seq
                # )
                aligned_struct, aligned_given, mapping, stats_cif = mapper.map_sequences(
                    custom_template_path[idx], seq_data['id'], seq
                )
                print(f"  INFO: {seq_data['id']} Mapping Statistics:", stats_cif)
                print(f"   > from template : {aligned_struct} ")
                print(f"   > query sequence: {aligned_given}")
                
                query_idx = [ i[0] for i in mapping ]
                seq_data['queryIndices'].extend(query_idx)
                template_idx = [ i[1] for i in mapping ]
                seq_data['templateIndices'].extend(template_idx)

    # Collect a list of up to "n_clusters" .a3m files from each directory
    chain_to_dirs = defaultdict(list)
    for directory in a3m_dirs:
        chain_id = extract_numbers(directory)
        chain_to_dirs[chain_id].append(directory)

    chain_combinations_by_id = defaultdict(list)
    for chain_id, paired_unpaired_dirs in chain_to_dirs.items():
        print(f'  INFO: chain {chain_id} has {len(paired_unpaired_dirs)} directories')
        if len(paired_unpaired_dirs) < 2:
            print(f"[WARNING] Chain {chain_id} does not have both paired and unpaired directories.")
            continue
        
        # Assign paired and unpaired directories
        paired_dir, unpaired_dir = paired_unpaired_dirs[:2]
        
        # Get .a3m files from paired_dir
        paired_files = [
            f'{input_path}/{paired_dir}/{file}' for file in os.listdir(f'{input_path}/{paired_dir}')
            if file.endswith('.a3m') and "U" not in file
        ]
        paired_files = sorted(paired_files)[:n_clusters]
        #print(f'  INFO: paired files: {len(paired_files)}')
        
        # Get .a3m files from unpaired_dir
        unpaired_files = [
            f'{input_path}/{unpaired_dir}/{file}' for file in os.listdir(f'{input_path}/{unpaired_dir}')
            if file.endswith('.a3m') and "U" not in file
        ]
        unpaired_files = sorted(unpaired_files)[:n_clusters]
        
        if not paired_files or not unpaired_files:
            print(f"[WARNING] No valid .a3m files in {paired_dir} or {unpaired_dir} for chain {chain_id}.")
            continue
        
        # Generate combinations of paired and unpaired files
        chain_combinations = list(itertools.product(paired_files, unpaired_files))
        chain_combinations_by_id[chain_id].extend(chain_combinations)

    # Ensure at least one valid chain is available
    if not chain_combinations_by_id:
        print("[ERROR] No valid chains with combinations found.")
        sys.exit()

    # Generate final combinations dynamically based on available chains
    available_chains = list(chain_combinations_by_id.keys())
    print(f"[INFO ] Generating combinations for chains: {available_chains}")

    # Generate combinations for chains
    combinations = list(itertools.product(*[chain_combinations_by_id[chain_id] for chain_id in available_chains]))
    print(f"[INFO ] Total combinations generated: {len(combinations)}")

    # Read the header file
    print(f'[INFO ] Read {msa_file} mmseq2 file for extracting header line')
    first_header_line = read_input(msa_file)[0]
    print(f'  INFO: first header_line: "{first_header_line[:-1]}"')

    print(f'[INFO ] Create arguments for each file combination')
    tasks = []
    for idx, input_files in enumerate(combinations):
        output_dir = f'{output_prefix}_{idx}'
        os.makedirs(output_dir, exist_ok=True)
        output_file = f'{output_dir}/msa{idx}.a3m'
        tasks.append((first_header_line, seq2data, use_paired, output_file, use_template, model_seeds, input_files))
        
    
    print(f'[INFO ] Save each combination as a file')
    n_workers = min(os.cpu_count(), len(tasks))
    n_workers = 10
    chunk_size = get_chunk_size(tasks, n_workers)
    print(f'  INFO: Using {n_workers} workers with chunk size {chunk_size}')

    results = parallel_process(
        items=tasks,                # 처리할 작업 목록
        func=process_task,          # 각 작업에 적용할 함수
        n_workers=n_workers,        # 워커 수
        use_threads=False,          # CPU 바운드 작업이므로 프로세스 사용
        chunksize=get_chunk_size(tasks, n_workers),  # 최적화된 청크 크기
        show_progress=True,         # 진행 상황 표시
        desc="Processing MSA combinations",  # 진행 상황 설명
        filter_none=True            # None 결과 필터링
    )



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Combine .a3m files from multiple directories.")
    parser.add_argument('input_path', type=str, 
                        help="Path to the input directory containing subdirectories with .a3m files.")
    parser.add_argument('--input_fasta', type=str, 
                        help="Path to the input fasta file.")
    parser.add_argument('--output_prefix', type=str, default="MSAsub", 
                        help="Prefix for output directories and files.")
    # MSA options
    parser.add_argument('--msa_file', type=str, required=True, 
                        help="Path to the mmseq2 MSA file for extracting header line.")
    parser.add_argument('--use_paired', default=False, action='store_true', 
                        help="Use paired information if this flag is set.")
    # Template options
    parser.add_argument('--use_template', action='store_true', 
                        help="Use template information if this flag is set.")
    parser.add_argument('--custom_template_path', type=str, nargs='+', default=False, 
                        help="Custom path to the template files.")
    # AFCluster options
    parser.add_argument('--n_clusters', type=int, default=10, 
                        help="Number of clusters to use in AFCluster.")
    # Model seeds option
    parser.add_argument('--model_seeds', type=int, nargs='+', 
                        default=[1, 100, 1000, 10000, 10000, 100000],
                        help="List of model seeds for AlphaFold3 simulations.")

    args = parser.parse_args()


    # Run main function with parsed arguments
    main(
        args.input_path, 
        args.input_fasta,  # Input FASTA file with protein sequences
        args.output_prefix, 
        args.msa_file, 
        args.use_paired,  # If paired files are available, set to True. Otherwise, set to False.
        args.use_template, # If template
        args.custom_template_path,  # Path to custom template file, if used.
        args.n_clusters,
        args.model_seeds
    )


