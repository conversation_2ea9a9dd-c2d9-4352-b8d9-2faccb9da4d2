{"input_file": "/home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m", "msa_info": {"n_sequences": 500, "avg_sequence_length": 91.0, "chain_poly_type": "protein"}, "methods_tested": {"encoding_methods": ["onehot", "MSAtransformer"], "clustering_methods": ["dbscan", "hierarchical"]}, "results_summary": {"onehot_dbscan": {"algorithm": "dbscan", "encoding_method": "onehot", "clustering_method": "dbscan", "n_clusters": 15, "n_noise": 274, "noise_percentage": 54.90981963927856, "elapsed_time": 7.647747278213501, "optimized_params": {"eps": 5.0, "min_samples": 3, "encoding_method": "onehot", "n_clusters": 9}, "output_directory": "test_clustering_results/KaiB_101_onehot_dbscan"}, "onehot_hierarchical": {"algorithm": "hierarchical", "encoding_method": "onehot", "clustering_method": "hierarchical", "n_clusters": 8, "n_noise": 0, "noise_percentage": 0.0, "elapsed_time": 0.20198297500610352, "optimized_params": {"linkage": "ward", "encoding_method": "onehot"}, "output_directory": "test_clustering_results/KaiB_101_onehot_hierarchical"}, "MSAtransformer_dbscan": {"algorithm": "dbscan", "encoding_method": "MSAtransformer", "clustering_method": "dbscan", "n_clusters": 15, "n_noise": 175, "noise_percentage": 35.07014028056113, "elapsed_time": 35.80569815635681, "optimized_params": {"eps": 5.0, "min_samples": 3, "encoding_method": "MSAtransformer", "n_clusters": 10}, "output_directory": "test_clustering_results/KaiB_101_MSAtransformer_dbscan"}, "MSAtransformer_hierarchical": {"algorithm": "hierarchical", "encoding_method": "MSAtransformer", "clustering_method": "hierarchical", "n_clusters": 8, "n_noise": 0, "noise_percentage": 0.0, "elapsed_time": 68.88261246681213, "optimized_params": {"linkage": "ward", "encoding_method": "MSAtransformer"}, "output_directory": "test_clustering_results/KaiB_101_MSAtransformer_hierarchical"}}}