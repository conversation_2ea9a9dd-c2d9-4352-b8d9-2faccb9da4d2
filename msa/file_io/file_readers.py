"""
MSA file reading utilities.

This module handles reading Multiple Sequence Alignment data from various file formats,
separating file I/O concerns from the core MSA data management.
"""

import os
import logging
from pathlib import Path
from typing import List, Tuple, Union


class MSAFileReader:
    """
    File reader for Multiple Sequence Alignment data.
    
    Supports reading from multiple file formats including A3M, Stockholm, and FASTA.
    This class follows the single responsibility principle by focusing solely on file I/O operations.
    """
    
    @staticmethod
    def read_file(file_path: Union[str, Path]) -> Tuple[List[str], List[str]]:
        """
        Read MSA file and return sequences and descriptions.
        
        Automatically detects file format based on file extension and delegates
        to the appropriate format-specific reader.

        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the MSA file.

        Returns
        -------
        Tuple[List[str], List[str]]
            A tuple containing sequences and descriptions.
            
        Raises
        ------
        FileNotFoundError
            If the specified file doesn't exist.
        ValueError
            If the file format is not supported or parsing fails.
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"MSA file not found: {file_path}")
        
        try:
            file_extension = file_path.suffix.lower()
            
            if file_extension == '.a3m':
                return MSAFileReader.read_a3m_file(file_path)
            elif file_extension == '.sto':
                return MSAFileReader.read_stockholm_file(file_path)
            else:
                # Default to FASTA format for other extensions
                return MSAFileReader.read_fasta_file(file_path)
                
        except Exception as e:
            raise ValueError(f"Failed to parse MSA file {file_path}: {str(e)}") from e

    @staticmethod
    def read_a3m_file(file_path: Union[str, Path]) -> Tuple[List[str], List[str]]:
        """
        Read A3M format file.
        
        A3M format is used by HH-suite and contains sequences with potential
        lowercase insertions that are removed during processing.

        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the A3M file.

        Returns
        -------
        Tuple[List[str], List[str]]
            A tuple containing cleaned sequences and descriptions.
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        sequences, descriptions = MSAFileReader._parse_a3m_content(content)
        
        # Remove lowercase letters (insertions) from A3M format
        cleaned_sequences = []
        for seq in sequences:
            cleaned_seq = ''.join(c for c in seq if not c.islower())
            cleaned_sequences.append(cleaned_seq)
        
        return cleaned_sequences, descriptions

    @staticmethod
    def read_stockholm_file(file_path: Union[str, Path]) -> Tuple[List[str], List[str]]:
        """
        Read Stockholm format file using BioPython.
        
        Stockholm format is used by Pfam and Rfam databases.

        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the Stockholm file.

        Returns
        -------
        Tuple[List[str], List[str]]
            A tuple containing sequences and descriptions.
            
        Raises
        ------
        ImportError
            If BioPython is not installed.
        """
        try:
            from Bio import AlignIO
        except ImportError:
            raise ImportError("BioPython is required to read Stockholm format files")
        
        sequences = []
        descriptions = []
        
        alignment = AlignIO.read(file_path, "stockholm")
        for record in alignment:
            sequences.append(str(record.seq))
            descriptions.append(record.description or record.id)
        
        return sequences, descriptions

    @staticmethod
    def read_fasta_file(file_path: Union[str, Path]) -> Tuple[List[str], List[str]]:
        """
        Read FASTA format file using BioPython.
        
        FASTA is the most common format for sequence data.

        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the FASTA file.

        Returns
        -------
        Tuple[List[str], List[str]]
            A tuple containing sequences and descriptions.
            
        Raises
        ------
        ImportError
            If BioPython is not installed.
        """
        try:
            from Bio import AlignIO
        except ImportError:
            raise ImportError("BioPython is required to read FASTA format files")
        
        sequences = []
        descriptions = []
        
        alignment = AlignIO.read(file_path, "fasta")
        for record in alignment:
            sequences.append(str(record.seq))
            descriptions.append(record.description or record.id)
        
        return sequences, descriptions

    @staticmethod
    def _parse_a3m_content(a3m_string: str) -> Tuple[List[str], List[str]]:
        """
        Parse an A3M formatted string into sequences and descriptions.
        
        This is a helper method extracted from the original MSA class
        to maintain the same A3M parsing logic.

        Parameters
        ----------
        a3m_string : str
            The content of an A3M file as a single string.

        Returns
        -------
        Tuple[List[str], List[str]]
            A tuple containing two lists: sequences and descriptions.
        """
        sequences = []
        descriptions = []
        current_sequence_lines = []

        lines = a3m_string.splitlines()

        # Handle potential #A3M# header
        if lines and lines[0].startswith("#A3M#"):
            lines.pop(0)  # Remove header line

        for line in lines:
            line = line.strip()
            if not line:
                continue
            if line.startswith(">"):
                if current_sequence_lines:  # If there's a sequence buffered
                    sequences.append("".join(current_sequence_lines))
                    current_sequence_lines = []
                descriptions.append(line[1:])  # Store description without '>'
            else:
                current_sequence_lines.append(line)

        # Append the last sequence
        if current_sequence_lines:
            sequences.append("".join(current_sequence_lines))

        # Handle potential mismatch in lengths
        if len(descriptions) != len(sequences):
            if len(descriptions) > len(sequences) and descriptions[-1] and not current_sequence_lines:
                descriptions = descriptions[:-1]  # Last description had no sequence

        return sequences, descriptions 