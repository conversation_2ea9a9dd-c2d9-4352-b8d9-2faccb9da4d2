"""
Statistical analysis for MSA data.

This module provides comprehensive statistical analysis capabilities for MSAs,
extracted and refined from the original msa_analyzer.py file.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from collections import Counter

from ..core.msa_core import MS<PERSON>ore
from ..core.sequence import Sequence


@dataclass
class MSAStats:
    """Data structure for MSA statistics."""
    # Basic statistics
    n_sequences: int
    mean_length: float
    median_length: float
    std_length: float
    min_length: int
    max_length: int
    
    # Gap statistics
    mean_gap_content: float
    median_gap_content: float
    std_gap_content: float
    
    # Composition statistics
    amino_acid_frequencies: Dict[str, float]
    position_wise_entropy: List[float]
    overall_entropy: float
    
    # Diversity measures
    effective_sequences: float
    simpson_diversity: float
    shannon_diversity: float


class MSAStatistics:
    """
    Comprehensive MSA statistical analysis.
    
    This class provides methods for calculating various statistics
    about MSA composition, diversity, and properties.
    """
    
    def __init__(self):
        """Initialize the MSA statistics analyzer."""
        self._cache = {}
    
    def calculate_basic_stats(self, msa: MSACore) -> Dict[str, Any]:
        """
        Calculate basic MSA statistics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, Any]
            Basic statistics
        """
        if not msa.sequences:
            return {}
        
        lengths = [seq.length for seq in msa.sequences]
        
        return {
            'n_sequences': len(msa),
            'mean_length': np.mean(lengths),
            'median_length': np.median(lengths),
            'std_length': np.std(lengths),
            'min_length': min(lengths),
            'max_length': max(lengths),
            'total_residues': sum(lengths)
        }
    
    def calculate_gap_stats(self, msa: MSACore) -> Dict[str, float]:
        """
        Calculate gap-related statistics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, float]
            Gap statistics
        """
        if not msa.sequences:
            return {}
        
        gap_contents = [seq.gap_content for seq in msa.sequences]
        
        # Position-wise gap analysis
        max_length = msa.max_length
        position_gap_counts = np.zeros(max_length)
        
        for seq in msa.sequences:
            for i, char in enumerate(seq.sequence):
                if i < max_length and char in '-.' :
                    position_gap_counts[i] += 1
        
        # Convert to fractions
        position_gap_fractions = position_gap_counts / len(msa) if len(msa) > 0 else position_gap_counts
        
        return {
            'mean_gap_content': np.mean(gap_contents),
            'median_gap_content': np.median(gap_contents),
            'std_gap_content': np.std(gap_contents),
            'min_gap_content': np.min(gap_contents),
            'max_gap_content': np.max(gap_contents),
            'mean_position_gap_fraction': np.mean(position_gap_fractions),
            'max_position_gap_fraction': np.max(position_gap_fractions) if len(position_gap_fractions) > 0 else 0.0,
            'highly_gapped_positions': np.sum(position_gap_fractions > 0.5)
        }
    
    def calculate_composition_stats(self, msa: MSACore) -> Dict[str, Any]:
        """
        Calculate amino acid composition statistics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, Any]
            Composition statistics
        """
        if not msa.sequences:
            return {}
        
        # Count amino acids across all sequences
        amino_acid_counts = Counter()
        total_residues = 0
        
        for seq in msa.sequences:
            for char in seq.sequence:
                if char not in '-.':  # Exclude gaps
                    amino_acid_counts[char] += 1
                    total_residues += 1
        
        # Calculate frequencies
        amino_acid_frequencies = {}
        if total_residues > 0:
            for aa, count in amino_acid_counts.items():
                amino_acid_frequencies[aa] = count / total_residues
        
        # Most and least common amino acids
        sorted_aa = sorted(amino_acid_frequencies.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'amino_acid_frequencies': amino_acid_frequencies,
            'total_residues': total_residues,
            'unique_amino_acids': len(amino_acid_frequencies),
            'most_common_aa': sorted_aa[0] if sorted_aa else None,
            'least_common_aa': sorted_aa[-1] if sorted_aa else None,
            'hydrophobic_fraction': self._calculate_hydrophobic_fraction(amino_acid_frequencies),
            'charged_fraction': self._calculate_charged_fraction(amino_acid_frequencies)
        }
    
    def _calculate_hydrophobic_fraction(self, aa_frequencies: Dict[str, float]) -> float:
        """Calculate fraction of hydrophobic amino acids."""
        hydrophobic_aa = set('AILVFPWM')
        return sum(freq for aa, freq in aa_frequencies.items() if aa in hydrophobic_aa)
    
    def _calculate_charged_fraction(self, aa_frequencies: Dict[str, float]) -> float:
        """Calculate fraction of charged amino acids."""
        charged_aa = set('DEKR')
        return sum(freq for aa, freq in aa_frequencies.items() if aa in charged_aa)
    
    def calculate_entropy_stats(self, msa: MSACore) -> Dict[str, Any]:
        """
        Calculate entropy-based statistics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, Any]
            Entropy statistics
        """
        if not msa.sequences:
            return {}
        
        max_length = msa.max_length
        position_entropies = []
        
        # Calculate entropy for each position
        for pos in range(max_length):
            char_counts = Counter()
            total_valid = 0
            
            for seq in msa.sequences:
                if pos < len(seq.sequence):
                    char = seq.sequence[pos]
                    if char not in '-.':  # Exclude gaps
                        char_counts[char] += 1
                        total_valid += 1
            
            # Calculate entropy
            if total_valid > 0:
                entropy = 0.0
                for count in char_counts.values():
                    p = count / total_valid
                    if p > 0:
                        entropy -= p * np.log2(p)
                position_entropies.append(entropy)
            else:
                position_entropies.append(0.0)
        
        # Overall entropy (average across positions)
        overall_entropy = np.mean(position_entropies) if position_entropies else 0.0
        
        return {
            'position_wise_entropy': position_entropies,
            'overall_entropy': overall_entropy,
            'mean_entropy': overall_entropy,
            'max_entropy': np.max(position_entropies) if position_entropies else 0.0,
            'min_entropy': np.min(position_entropies) if position_entropies else 0.0,
            'highly_conserved_positions': np.sum(np.array(position_entropies) < 0.5),
            'highly_variable_positions': np.sum(np.array(position_entropies) > 3.0)
        }
    
    def calculate_diversity_indices(self, msa: MSACore) -> Dict[str, float]:
        """
        Calculate diversity indices.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, float]
            Diversity indices
        """
        if not msa.sequences:
            return {}
        
        # Get unique sequences and their counts
        seq_counts = Counter(seq.sequence for seq in msa.sequences)
        total_sequences = len(msa)
        
        # Shannon diversity index
        shannon_diversity = 0.0
        for count in seq_counts.values():
            p = count / total_sequences
            if p > 0:
                shannon_diversity -= p * np.log(p)
        
        # Simpson diversity index
        simpson_diversity = 1.0 - sum((count / total_sequences) ** 2 for count in seq_counts.values())
        
        # Effective number of sequences
        effective_sequences = np.exp(shannon_diversity)
        
        # Gini-Simpson index (probability that two randomly selected sequences are different)
        gini_simpson = simpson_diversity
        
        # Inverse Simpson index
        inverse_simpson = 1.0 / (1.0 - simpson_diversity) if simpson_diversity < 1.0 else float('inf')
        
        return {
            'shannon_diversity': shannon_diversity,
            'simpson_diversity': simpson_diversity,
            'effective_sequences': effective_sequences,
            'gini_simpson': gini_simpson,
            'inverse_simpson': inverse_simpson,
            'unique_sequences': len(seq_counts),
            'sequence_redundancy': 1.0 - (len(seq_counts) / total_sequences)
        }
    
    def calculate_pairwise_identity_stats(self, 
                                        msa: MSACore,
                                        sample_size: Optional[int] = 1000) -> Dict[str, float]:
        """
        Calculate pairwise sequence identity statistics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
        sample_size : Optional[int]
            Maximum number of sequence pairs to sample for efficiency
            
        Returns
        -------
        Dict[str, float]
            Pairwise identity statistics
        """
        if len(msa) < 2:
            return {}
        
        sequences = msa.sequences
        n_sequences = len(sequences)
        
        # Sample sequence pairs if MSA is large
        if sample_size and n_sequences * (n_sequences - 1) // 2 > sample_size:
            # Randomly sample sequence pairs
            indices = np.random.choice(n_sequences, size=min(sample_size, n_sequences), replace=False)
            sequences = [sequences[i] for i in indices]
        
        # Calculate pairwise identities
        identities = []
        for i in range(len(sequences)):
            for j in range(i + 1, len(sequences)):
                identity = sequences[i].calculate_identity(sequences[j])
                identities.append(identity)
        
        if not identities:
            return {}
        
        return {
            'mean_pairwise_identity': np.mean(identities),
            'median_pairwise_identity': np.median(identities),
            'std_pairwise_identity': np.std(identities),
            'min_pairwise_identity': np.min(identities),
            'max_pairwise_identity': np.max(identities),
            'pairwise_comparisons': len(identities)
        }
    
    def calculate_comprehensive_stats(self, msa: MSACore) -> MSAStats:
        """
        Calculate comprehensive MSA statistics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        MSAStats
            Comprehensive statistics object
        """
        if not msa.sequences:
            # Return empty stats for empty MSA
            return MSAStats(
                n_sequences=0, mean_length=0.0, median_length=0.0, std_length=0.0,
                min_length=0, max_length=0, mean_gap_content=0.0, median_gap_content=0.0,
                std_gap_content=0.0, amino_acid_frequencies={}, position_wise_entropy=[],
                overall_entropy=0.0, effective_sequences=0.0, simpson_diversity=0.0,
                shannon_diversity=0.0
            )
        
        # Calculate all statistics
        basic_stats = self.calculate_basic_stats(msa)
        gap_stats = self.calculate_gap_stats(msa)
        composition_stats = self.calculate_composition_stats(msa)
        entropy_stats = self.calculate_entropy_stats(msa)
        diversity_stats = self.calculate_diversity_indices(msa)
        
        return MSAStats(
            n_sequences=basic_stats['n_sequences'],
            mean_length=basic_stats['mean_length'],
            median_length=basic_stats['median_length'],
            std_length=basic_stats['std_length'],
            min_length=basic_stats['min_length'],
            max_length=basic_stats['max_length'],
            mean_gap_content=gap_stats['mean_gap_content'],
            median_gap_content=gap_stats['median_gap_content'],
            std_gap_content=gap_stats['std_gap_content'],
            amino_acid_frequencies=composition_stats['amino_acid_frequencies'],
            position_wise_entropy=entropy_stats['position_wise_entropy'],
            overall_entropy=entropy_stats['overall_entropy'],
            effective_sequences=diversity_stats['effective_sequences'],
            simpson_diversity=diversity_stats['simpson_diversity'],
            shannon_diversity=diversity_stats['shannon_diversity']
        )
    
    def generate_summary_report(self, stats: MSAStats) -> str:
        """
        Generate a human-readable summary report.
        
        Parameters
        ----------
        stats : MSAStats
            Statistics to summarize
            
        Returns
        -------
        str
            Summary report text
        """
        report_lines = []
        
        # Header
        report_lines.append("MSA Statistical Summary")
        report_lines.append("=" * 30)
        report_lines.append("")
        
        # Basic statistics
        report_lines.append("Basic Statistics:")
        report_lines.append(f"  Number of sequences: {stats.n_sequences}")
        report_lines.append(f"  Mean length: {stats.mean_length:.1f}")
        report_lines.append(f"  Length range: {stats.min_length} - {stats.max_length}")
        report_lines.append(f"  Length std dev: {stats.std_length:.1f}")
        report_lines.append("")
        
        # Gap statistics
        report_lines.append("Gap Content:")
        report_lines.append(f"  Mean gap content: {stats.mean_gap_content:.1%}")
        report_lines.append(f"  Gap content range: {stats.median_gap_content:.1%} (median)")
        report_lines.append("")
        
        # Diversity
        report_lines.append("Diversity Measures:")
        report_lines.append(f"  Shannon diversity: {stats.shannon_diversity:.3f}")
        report_lines.append(f"  Simpson diversity: {stats.simpson_diversity:.3f}")
        report_lines.append(f"  Effective sequences: {stats.effective_sequences:.1f}")
        report_lines.append("")
        
        # Conservation
        report_lines.append("Conservation:")
        report_lines.append(f"  Overall entropy: {stats.overall_entropy:.3f}")
        if stats.position_wise_entropy:
            conserved_positions = sum(1 for e in stats.position_wise_entropy if e < 0.5)
            variable_positions = sum(1 for e in stats.position_wise_entropy if e > 3.0)
            report_lines.append(f"  Highly conserved positions: {conserved_positions}")
            report_lines.append(f"  Highly variable positions: {variable_positions}")
        report_lines.append("")
        
        # Top amino acids
        if stats.amino_acid_frequencies:
            sorted_aa = sorted(stats.amino_acid_frequencies.items(), 
                             key=lambda x: x[1], reverse=True)[:5]
            report_lines.append("Most Common Amino Acids:")
            for aa, freq in sorted_aa:
                report_lines.append(f"  {aa}: {freq:.1%}")
        
        return '\n'.join(report_lines)
    
    def compare_stats(self, stats1: MSAStats, stats2: MSAStats) -> Dict[str, Any]:
        """
        Compare two sets of MSA statistics.
        
        Parameters
        ----------
        stats1 : MSAStats
            First statistics set
        stats2 : MSAStats
            Second statistics set
            
        Returns
        -------
        Dict[str, Any]
            Comparison results
        """
        comparison = {
            'sequence_count_ratio': stats2.n_sequences / stats1.n_sequences if stats1.n_sequences > 0 else float('inf'),
            'length_difference': stats2.mean_length - stats1.mean_length,
            'gap_content_difference': stats2.mean_gap_content - stats1.mean_gap_content,
            'diversity_difference': stats2.shannon_diversity - stats1.shannon_diversity,
            'entropy_difference': stats2.overall_entropy - stats1.overall_entropy
        }
        
        return comparison 