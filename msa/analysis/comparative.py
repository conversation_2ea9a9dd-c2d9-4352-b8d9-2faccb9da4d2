"""
Comparative analysis for multiple MSAs.

This module provides tools for comparing MSAs, analyzing differences,
and generating detailed comparison reports.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tu<PERSON>, Union
from dataclasses import dataclass
from pathlib import Path

from ..core.msa_core import MS<PERSON>ore
from ..core.sequence import Sequence
from .statistics import MSAStatistics, MSAStats
from .metrics import MSAMetrics, MSAQualityMetrics


@dataclass
class MSAComparison:
    """Data structure for MSA comparison results."""
    msa1_name: str
    msa2_name: str
    
    # Sequence overlap
    common_sequences: int
    unique_to_msa1: int
    unique_to_msa2: int
    overlap_percentage: float
    
    # Quality comparison
    quality_metrics_1: MSAQualityMetrics
    quality_metrics_2: MSAQualityMetrics
    quality_improvement: float
    
    # Statistical comparison
    stats_1: MSAStats
    stats_2: MSAStats
    
    # Position-wise comparison
    position_similarities: List[float]
    divergent_positions: List[int]


@dataclass 
class MultiMSAAnalysis:
    """Data structure for multi-MSA analysis results."""
    msa_names: List[str]
    pairwise_comparisons: List[MSAComparison]
    consensus_analysis: Dict[str, Any]
    quality_ranking: List[Tuple[str, float]]
    diversity_metrics: Dict[str, Any]


class ComparativeAnalyzer:
    """
    Analyzer for comparing multiple MSAs.
    
    This class provides comprehensive tools for comparing MSAs,
    identifying differences, and analyzing relationships between datasets.
    """
    
    def __init__(self):
        """Initialize the comparative analyzer."""
        self.stats_analyzer = MSAStatistics()
        self.metrics_analyzer = MSAMetrics()
    
    def compare_two_msas(self, 
                        msa1: MSACore, 
                        msa2: MSACore,
                        name1: str = "MSA1",
                        name2: str = "MSA2") -> MSAComparison:
        """
        Compare two MSAs comprehensively.
        
        Parameters
        ----------
        msa1 : MSACore
            First MSA to compare
        msa2 : MSACore
            Second MSA to compare
        name1 : str
            Name for first MSA
        name2 : str
            Name for second MSA
            
        Returns
        -------
        MSAComparison
            Comprehensive comparison results
        """
        logging.info(f"Comparing MSAs: {name1} vs {name2}")
        
        # Sequence overlap analysis
        overlap_stats = self._analyze_sequence_overlap(msa1, msa2)
        
        # Quality metrics for both MSAs
        quality_metrics_1 = self.metrics_analyzer.calculate_comprehensive_metrics(msa1)
        quality_metrics_2 = self.metrics_analyzer.calculate_comprehensive_metrics(msa2)
        
        # Statistical analysis
        stats_1 = self.stats_analyzer.calculate_comprehensive_stats(msa1)
        stats_2 = self.stats_analyzer.calculate_comprehensive_stats(msa2)
        
        # Position-wise comparison
        position_similarities, divergent_positions = self._compare_positions(msa1, msa2)
        
        # Quality improvement calculation
        quality_improvement = quality_metrics_2.overall_quality_score - quality_metrics_1.overall_quality_score
        
        return MSAComparison(
            msa1_name=name1,
            msa2_name=name2,
            common_sequences=overlap_stats['common_sequences'],
            unique_to_msa1=overlap_stats['unique_to_msa1'],
            unique_to_msa2=overlap_stats['unique_to_msa2'],
            overlap_percentage=overlap_stats['overlap_percentage'],
            quality_metrics_1=quality_metrics_1,
            quality_metrics_2=quality_metrics_2,
            quality_improvement=quality_improvement,
            stats_1=stats_1,
            stats_2=stats_2,
            position_similarities=position_similarities,
            divergent_positions=divergent_positions
        )
    
    def _analyze_sequence_overlap(self, msa1: MSACore, msa2: MSACore) -> Dict[str, Any]:
        """Analyze sequence overlap between two MSAs."""
        # Extract sequence identifiers or sequence strings for comparison
        seqs1 = set()
        seqs2 = set()
        
        # Use sequence descriptions as identifiers if available, otherwise use sequences
        for seq in msa1.sequences:
            identifier = seq.description if seq.description else seq.sequence
            seqs1.add(identifier)
        
        for seq in msa2.sequences:
            identifier = seq.description if seq.description else seq.sequence
            seqs2.add(identifier)
        
        # Calculate overlap
        common_sequences = len(seqs1.intersection(seqs2))
        unique_to_msa1 = len(seqs1 - seqs2)
        unique_to_msa2 = len(seqs2 - seqs1)
        
        total_unique = len(seqs1.union(seqs2))
        overlap_percentage = common_sequences / total_unique * 100 if total_unique > 0 else 0.0
        
        return {
            'common_sequences': common_sequences,
            'unique_to_msa1': unique_to_msa1,
            'unique_to_msa2': unique_to_msa2,
            'overlap_percentage': overlap_percentage
        }
    
    def _compare_positions(self, msa1: MSACore, msa2: MSACore) -> Tuple[List[float], List[int]]:
        """Compare conservation patterns position by position."""
        # Calculate conservation profiles for both MSAs
        conservation_analyzer = self.metrics_analyzer.conservation_analyzer
        profile1 = conservation_analyzer.calculate_conservation_profile(msa1)
        profile2 = conservation_analyzer.calculate_conservation_profile(msa2)
        
        # Align profiles (use shorter length)
        min_length = min(len(profile1), len(profile2))
        similarities = []
        divergent_positions = []
        
        for i in range(min_length):
            cons1 = profile1[i].conservation
            cons2 = profile2[i].conservation
            
            # Calculate similarity (1 - absolute difference)
            similarity = 1.0 - abs(cons1 - cons2)
            similarities.append(similarity)
            
            # Mark as divergent if difference is large
            if abs(cons1 - cons2) > 0.5:
                divergent_positions.append(i)
        
        return similarities, divergent_positions
    
    def analyze_multiple_msas(self, 
                            msas: List[MSACore],
                            names: Optional[List[str]] = None) -> MultiMSAAnalysis:
        """
        Analyze multiple MSAs comprehensively.
        
        Parameters
        ----------
        msas : List[MSACore]
            List of MSAs to analyze
        names : Optional[List[str]]
            Names for the MSAs
            
        Returns
        -------
        MultiMSAAnalysis
            Comprehensive multi-MSA analysis results
        """
        if not msas:
            raise ValueError("At least one MSA is required")
        
        # Generate names if not provided
        if names is None:
            names = [f"MSA_{i+1}" for i in range(len(msas))]
        elif len(names) != len(msas):
            raise ValueError("Number of names must match number of MSAs")
        
        logging.info(f"Analyzing {len(msas)} MSAs: {', '.join(names)}")
        
        # Pairwise comparisons
        pairwise_comparisons = []
        for i in range(len(msas)):
            for j in range(i + 1, len(msas)):
                comparison = self.compare_two_msas(msas[i], msas[j], names[i], names[j])
                pairwise_comparisons.append(comparison)
        
        # Quality ranking
        quality_ranking = []
        for msa, name in zip(msas, names):
            quality_metrics = self.metrics_analyzer.calculate_comprehensive_metrics(msa)
            quality_ranking.append((name, quality_metrics.overall_quality_score))
        
        quality_ranking.sort(key=lambda x: x[1], reverse=True)
        
        # Consensus analysis
        consensus_analysis = self._analyze_consensus(msas, names)
        
        # Diversity metrics
        diversity_metrics = self._calculate_multi_msa_diversity(msas, names)
        
        return MultiMSAAnalysis(
            msa_names=names,
            pairwise_comparisons=pairwise_comparisons,
            consensus_analysis=consensus_analysis,
            quality_ranking=quality_ranking,
            diversity_metrics=diversity_metrics
        )
    
    def _analyze_consensus(self, msas: List[MSACore], names: List[str]) -> Dict[str, Any]:
        """Analyze consensus across multiple MSAs."""
        # Calculate statistics for each MSA
        all_stats = []
        for msa in msas:
            stats = self.stats_analyzer.calculate_comprehensive_stats(msa)
            all_stats.append(stats)
        
        # Consensus metrics
        consensus_metrics = {
            'mean_sequence_counts': [stats.n_sequences for stats in all_stats],
            'mean_lengths': [stats.mean_length for stats in all_stats],
            'mean_gap_contents': [stats.mean_gap_content for stats in all_stats],
            'shannon_diversities': [stats.shannon_diversity for stats in all_stats],
            'overall_entropies': [stats.overall_entropy for stats in all_stats]
        }
        
        # Calculate consensus statistics
        consensus_analysis = {}
        for metric_name, values in consensus_metrics.items():
            consensus_analysis[f'{metric_name}_mean'] = np.mean(values)
            consensus_analysis[f'{metric_name}_std'] = np.std(values)
            consensus_analysis[f'{metric_name}_range'] = np.max(values) - np.min(values)
        
        # Identify outliers
        outliers = self._identify_outlier_msas(msas, names, all_stats)
        consensus_analysis['outliers'] = outliers
        
        return consensus_analysis
    
    def _identify_outlier_msas(self, 
                              msas: List[MSACore], 
                              names: List[str], 
                              stats: List[MSAStats]) -> List[str]:
        """Identify MSAs that are statistical outliers."""
        outliers = []
        
        # Check for outliers in key metrics
        metrics_to_check = [
            ('n_sequences', [s.n_sequences for s in stats]),
            ('mean_length', [s.mean_length for s in stats]),
            ('shannon_diversity', [s.shannon_diversity for s in stats])
        ]
        
        for metric_name, values in metrics_to_check:
            if len(values) < 3:  # Need at least 3 MSAs for outlier detection
                continue
            
            q1 = np.percentile(values, 25)
            q3 = np.percentile(values, 75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            for i, value in enumerate(values):
                if value < lower_bound or value > upper_bound:
                    if names[i] not in outliers:
                        outliers.append(names[i])
        
        return outliers
    
    def _calculate_multi_msa_diversity(self, msas: List[MSACore], names: List[str]) -> Dict[str, Any]:
        """Calculate diversity metrics across multiple MSAs."""
        # Combine all sequences from all MSAs
        all_sequences = []
        msa_membership = []
        
        for i, msa in enumerate(msas):
            for seq in msa.sequences:
                all_sequences.append(seq)
                msa_membership.append(i)
        
        if not all_sequences:
            return {}
        
        # Calculate inter-MSA diversity
        inter_msa_identities = []
        for i in range(len(all_sequences)):
            for j in range(i + 1, len(all_sequences)):
                if msa_membership[i] != msa_membership[j]:  # From different MSAs
                    identity = all_sequences[i].calculate_identity(all_sequences[j])
                    inter_msa_identities.append(identity)
        
        # Calculate intra-MSA diversity
        intra_msa_identities = []
        for i in range(len(all_sequences)):
            for j in range(i + 1, len(all_sequences)):
                if msa_membership[i] == msa_membership[j]:  # From same MSA
                    identity = all_sequences[i].calculate_identity(all_sequences[j])
                    intra_msa_identities.append(identity)
        
        diversity_metrics = {}
        
        if inter_msa_identities:
            diversity_metrics.update({
                'inter_msa_mean_identity': np.mean(inter_msa_identities),
                'inter_msa_std_identity': np.std(inter_msa_identities),
                'inter_msa_min_identity': np.min(inter_msa_identities),
                'inter_msa_max_identity': np.max(inter_msa_identities)
            })
        
        if intra_msa_identities:
            diversity_metrics.update({
                'intra_msa_mean_identity': np.mean(intra_msa_identities),
                'intra_msa_std_identity': np.std(intra_msa_identities),
                'intra_msa_min_identity': np.min(intra_msa_identities),
                'intra_msa_max_identity': np.max(intra_msa_identities)
            })
        
        # Calculate separation score (how well MSAs are separated)
        if inter_msa_identities and intra_msa_identities:
            separation_score = (np.mean(intra_msa_identities) - np.mean(inter_msa_identities))
            diversity_metrics['separation_score'] = separation_score
        
        return diversity_metrics
    
    def generate_comparison_report(self, comparison: MSAComparison) -> str:
        """
        Generate a detailed comparison report for two MSAs.
        
        Parameters
        ----------
        comparison : MSAComparison
            Comparison results to report
            
        Returns
        -------
        str
            Detailed comparison report
        """
        report_lines = []
        
        # Header
        report_lines.append(f"MSA Comparison Report: {comparison.msa1_name} vs {comparison.msa2_name}")
        report_lines.append("=" * 60)
        report_lines.append("")
        
        # Sequence overlap
        report_lines.append("Sequence Overlap Analysis:")
        report_lines.append(f"  Common sequences: {comparison.common_sequences}")
        report_lines.append(f"  Unique to {comparison.msa1_name}: {comparison.unique_to_msa1}")
        report_lines.append(f"  Unique to {comparison.msa2_name}: {comparison.unique_to_msa2}")
        report_lines.append(f"  Overlap percentage: {comparison.overlap_percentage:.1f}%")
        report_lines.append("")
        
        # Quality comparison
        report_lines.append("Quality Comparison:")
        q1 = comparison.quality_metrics_1.overall_quality_score
        q2 = comparison.quality_metrics_2.overall_quality_score
        report_lines.append(f"  {comparison.msa1_name} quality: {q1:.3f}")
        report_lines.append(f"  {comparison.msa2_name} quality: {q2:.3f}")
        report_lines.append(f"  Quality improvement: {comparison.quality_improvement:+.3f}")
        
        if comparison.quality_improvement > 0.1:
            report_lines.append(f"  → {comparison.msa2_name} shows significant improvement")
        elif comparison.quality_improvement < -0.1:
            report_lines.append(f"  → {comparison.msa1_name} shows significant improvement")
        else:
            report_lines.append("  → Quality scores are comparable")
        report_lines.append("")
        
        # Statistical comparison
        report_lines.append("Statistical Comparison:")
        report_lines.append(f"  Sequence count: {comparison.stats_1.n_sequences} vs {comparison.stats_2.n_sequences}")
        report_lines.append(f"  Mean length: {comparison.stats_1.mean_length:.1f} vs {comparison.stats_2.mean_length:.1f}")
        report_lines.append(f"  Shannon diversity: {comparison.stats_1.shannon_diversity:.3f} vs {comparison.stats_2.shannon_diversity:.3f}")
        report_lines.append(f"  Gap content: {comparison.stats_1.mean_gap_content:.1%} vs {comparison.stats_2.mean_gap_content:.1%}")
        report_lines.append("")
        
        # Position analysis
        if comparison.position_similarities:
            mean_similarity = np.mean(comparison.position_similarities)
            report_lines.append("Position-wise Analysis:")
            report_lines.append(f"  Mean position similarity: {mean_similarity:.3f}")
            report_lines.append(f"  Divergent positions: {len(comparison.divergent_positions)}")
            
            if comparison.divergent_positions:
                # Show first few divergent positions
                shown_positions = comparison.divergent_positions[:10]
                positions_str = ", ".join(map(str, shown_positions))
                if len(comparison.divergent_positions) > 10:
                    positions_str += "..."
                report_lines.append(f"  Divergent positions: {positions_str}")
        
        return '\n'.join(report_lines)
    
    def generate_multi_msa_report(self, analysis: MultiMSAAnalysis) -> str:
        """
        Generate a comprehensive multi-MSA analysis report.
        
        Parameters
        ----------
        analysis : MultiMSAAnalysis
            Multi-MSA analysis results
            
        Returns
        -------
        str
            Comprehensive analysis report
        """
        report_lines = []
        
        # Header
        report_lines.append("Multi-MSA Analysis Report")
        report_lines.append("=" * 30)
        report_lines.append(f"Analyzed MSAs: {', '.join(analysis.msa_names)}")
        report_lines.append("")
        
        # Quality ranking
        report_lines.append("Quality Ranking:")
        for i, (name, score) in enumerate(analysis.quality_ranking, 1):
            report_lines.append(f"  {i}. {name}: {score:.3f}")
        report_lines.append("")
        
        # Consensus analysis
        if analysis.consensus_analysis:
            report_lines.append("Consensus Analysis:")
            consensus = analysis.consensus_analysis
            
            if 'mean_sequence_counts_mean' in consensus:
                report_lines.append(f"  Mean sequence count: {consensus['mean_sequence_counts_mean']:.1f} ± {consensus['mean_sequence_counts_std']:.1f}")
            
            if 'shannon_diversities_mean' in consensus:
                report_lines.append(f"  Mean Shannon diversity: {consensus['shannon_diversities_mean']:.3f} ± {consensus['shannon_diversities_std']:.3f}")
            
            if 'outliers' in consensus and consensus['outliers']:
                report_lines.append(f"  Outlier MSAs: {', '.join(consensus['outliers'])}")
            report_lines.append("")
        
        # Diversity metrics
        if analysis.diversity_metrics:
            report_lines.append("Multi-MSA Diversity:")
            diversity = analysis.diversity_metrics
            
            if 'inter_msa_mean_identity' in diversity:
                report_lines.append(f"  Inter-MSA identity: {diversity['inter_msa_mean_identity']:.3f}")
            
            if 'intra_msa_mean_identity' in diversity:
                report_lines.append(f"  Intra-MSA identity: {diversity['intra_msa_mean_identity']:.3f}")
            
            if 'separation_score' in diversity:
                separation = diversity['separation_score']
                report_lines.append(f"  Separation score: {separation:.3f}")
                if separation > 0.1:
                    report_lines.append("    → Good separation between MSAs")
                elif separation < -0.1:
                    report_lines.append("    → MSAs overlap significantly")
                else:
                    report_lines.append("    → Moderate separation")
        
        return '\n'.join(report_lines)
    
    def identify_best_msa(self, 
                         msas: List[MSACore],
                         names: Optional[List[str]] = None,
                         criteria: str = "overall_quality") -> Tuple[int, str, float]:
        """
        Identify the best MSA based on specified criteria.
        
        Parameters
        ----------
        msas : List[MSACore]
            List of MSAs to evaluate
        names : Optional[List[str]]
            Names for the MSAs
        criteria : str
            Evaluation criteria ('overall_quality', 'diversity', 'conservation')
            
        Returns
        -------
        Tuple[int, str, float]
            Index, name, and score of the best MSA
        """
        if not msas:
            raise ValueError("At least one MSA is required")
        
        if names is None:
            names = [f"MSA_{i+1}" for i in range(len(msas))]
        
        scores = []
        
        for msa in msas:
            if criteria == "overall_quality":
                score = self.metrics_analyzer.calculate_overall_quality_score(msa)
            elif criteria == "diversity":
                stats = self.stats_analyzer.calculate_comprehensive_stats(msa)
                score = stats.shannon_diversity
            elif criteria == "conservation":
                conservation_analysis = self.metrics_analyzer.conservation_analyzer.analyze_conservation_patterns(msa)
                score = conservation_analysis['mean_conservation']
            else:
                raise ValueError(f"Unknown criteria: {criteria}")
            
            scores.append(score)
        
        # Find best score
        best_index = np.argmax(scores)
        best_score = scores[best_index]
        best_name = names[best_index]
        
        logging.info(f"Best MSA by {criteria}: {best_name} (score: {best_score:.3f})")
        
        return best_index, best_name, best_score 