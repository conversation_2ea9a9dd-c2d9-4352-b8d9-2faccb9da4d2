"""
Diversity analysis for MSA sequences.

This module provides specialized tools for analyzing sequence diversity,
phylogenetic diversity, and clustering-based diversity assessment.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from collections import Counter
from scipy.spatial.distance import pdist, squareform
from scipy.cluster.hierarchy import linkage, fcluster
from sklearn.metrics import pairwise_distances

from ..core.msa_core import MS<PERSON>ore
from ..core.sequence import Sequence


@dataclass
class DiversityMetrics:
    """Data structure for diversity analysis results."""
    # Basic diversity indices
    shannon_index: float
    simpson_index: float
    effective_sequences: float
    
    # Phylogenetic diversity
    phylogenetic_diversity: float
    mean_pairwise_distance: float
    
    # Clustering-based diversity
    functional_diversity: float
    structural_diversity: float
    
    # Sequence composition diversity
    composition_diversity: float
    length_diversity: float
    
    # Advanced metrics
    rarefaction_curve: List[Tuple[int, float]]  # (sample_size, expected_diversity)
    diversity_profile: List[float]  # Diversity at different scales


@dataclass
class FunctionalGroup:
    """Data structure for functional sequence groups."""
    group_id: int
    sequences: List[Sequence]
    representative_sequence: Sequence
    consensus_sequence: str
    conservation_score: float
    intra_group_diversity: float


class DiversityAnalyzer:
    """
    Specialized analyzer for sequence diversity assessment.
    
    This class provides comprehensive tools for analyzing different aspects
    of sequence diversity within MSAs, including phylogenetic, functional,
    and compositional diversity measures.
    """
    
    def __init__(self):
        """Initialize the diversity analyzer."""
        self._cache = {}
    
    def calculate_basic_diversity_indices(self, msa: MSACore) -> Dict[str, float]:
        """
        Calculate basic diversity indices for the MSA.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, float]
            Basic diversity indices
        """
        if not msa.sequences:
            return {}
        
        # Get unique sequences and their frequencies
        sequence_counts = Counter(seq.sequence for seq in msa.sequences)
        n_total = len(msa)
        n_unique = len(sequence_counts)
        
        # Shannon diversity index
        shannon_index = 0.0
        for count in sequence_counts.values():
            p = count / n_total
            if p > 0:
                shannon_index -= p * np.log(p)
        
        # Simpson diversity index (1 - D)
        simpson_index = 1.0 - sum((count / n_total) ** 2 for count in sequence_counts.values())
        
        # Effective number of sequences (exponential of Shannon index)
        effective_sequences = np.exp(shannon_index)
        
        # Evenness (Shannon index normalized by maximum possible)
        max_shannon = np.log(n_unique) if n_unique > 0 else 0
        evenness = shannon_index / max_shannon if max_shannon > 0 else 0
        
        # Richness (number of unique sequences)
        richness = n_unique
        
        return {
            'shannon_index': shannon_index,
            'simpson_index': simpson_index,
            'effective_sequences': effective_sequences,
            'evenness': evenness,
            'richness': richness,
            'redundancy': 1.0 - (richness / n_total)
        }
    
    def calculate_phylogenetic_diversity(self, 
                                       msa: MSACore,
                                       distance_metric: str = 'hamming') -> Dict[str, float]:
        """
        Calculate phylogenetic diversity measures.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
        distance_metric : str
            Distance metric to use ('hamming', 'identity')
            
        Returns
        -------
        Dict[str, float]
            Phylogenetic diversity metrics
        """
        if len(msa) < 2:
            return {}
        
        # Calculate pairwise distances
        distances = self._calculate_pairwise_distances(msa.sequences, distance_metric)
        
        # Mean pairwise distance
        mean_pairwise_distance = np.mean(distances[np.triu_indices_from(distances, k=1)])
        
        # Phylogenetic diversity (sum of unique branch lengths)
        # Approximated as mean distance * effective sequence number
        basic_diversity = self.calculate_basic_diversity_indices(msa)
        phylogenetic_diversity = mean_pairwise_distance * basic_diversity.get('effective_sequences', 1.0)
        
        # Distance-based diversity measures
        distance_variance = np.var(distances[np.triu_indices_from(distances, k=1)])
        max_distance = np.max(distances)
        min_distance = np.min(distances[distances > 0]) if np.any(distances > 0) else 0
        
        return {
            'phylogenetic_diversity': phylogenetic_diversity,
            'mean_pairwise_distance': mean_pairwise_distance,
            'distance_variance': distance_variance,
            'max_pairwise_distance': max_distance,
            'min_pairwise_distance': min_distance,
            'distance_range': max_distance - min_distance
        }
    
    def _calculate_pairwise_distances(self, 
                                    sequences: List[Sequence],
                                    metric: str = 'hamming') -> np.ndarray:
        """Calculate pairwise distances between sequences."""
        n_seqs = len(sequences)
        distances = np.zeros((n_seqs, n_seqs))
        
        for i in range(n_seqs):
            for j in range(i + 1, n_seqs):
                if metric == 'hamming':
                    # Hamming distance (proportion of differing positions)
                    distance = 1.0 - sequences[i].calculate_identity(sequences[j])
                elif metric == 'identity':
                    # Identity-based distance
                    distance = 1.0 - sequences[i].calculate_identity(sequences[j])
                else:
                    raise ValueError(f"Unknown distance metric: {metric}")
                
                distances[i, j] = distance
                distances[j, i] = distance
        
        return distances
    
    def calculate_functional_diversity(self, 
                                     msa: MSACore,
                                     n_clusters: Optional[int] = None) -> Dict[str, Any]:
        """
        Calculate functional diversity using sequence clustering.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
        n_clusters : Optional[int]
            Number of functional clusters (auto-determined if None)
            
        Returns
        -------
        Dict[str, Any]
            Functional diversity metrics and cluster information
        """
        if len(msa) < 2:
            return {}
        
        # Calculate distance matrix
        distances = self._calculate_pairwise_distances(msa.sequences)
        
        # Hierarchical clustering
        linkage_matrix = linkage(squareform(distances), method='average')
        
        # Determine optimal number of clusters if not provided
        if n_clusters is None:
            n_clusters = self._determine_optimal_clusters(distances, max_clusters=min(10, len(msa) // 2))
        
        # Get cluster assignments
        cluster_labels = fcluster(linkage_matrix, n_clusters, criterion='maxclust')
        
        # Create functional groups
        functional_groups = self._create_functional_groups(msa.sequences, cluster_labels)
        
        # Calculate functional diversity metrics
        functional_diversity = self._calculate_functional_diversity_score(functional_groups)
        
        # Inter-group diversity
        inter_group_distances = self._calculate_inter_group_distances(functional_groups)
        
        return {
            'functional_diversity': functional_diversity,
            'n_functional_groups': len(functional_groups),
            'functional_groups': functional_groups,
            'inter_group_distances': inter_group_distances,
            'mean_inter_group_distance': np.mean(inter_group_distances) if inter_group_distances else 0.0,
            'group_size_distribution': [len(group.sequences) for group in functional_groups]
        }
    
    def _determine_optimal_clusters(self, 
                                  distances: np.ndarray,
                                  max_clusters: int = 10) -> int:
        """Determine optimal number of clusters using silhouette analysis."""
        from sklearn.metrics import silhouette_score
        from sklearn.cluster import AgglomerativeClustering
        
        best_score = -1
        best_n_clusters = 2
        
        for n_clusters in range(2, min(max_clusters + 1, len(distances))):
            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                affinity='precomputed',
                linkage='average'
            )
            cluster_labels = clustering.fit_predict(distances)
            
            try:
                score = silhouette_score(distances, cluster_labels, metric='precomputed')
                if score > best_score:
                    best_score = score
                    best_n_clusters = n_clusters
            except:
                continue
        
        return best_n_clusters
    
    def _create_functional_groups(self, 
                                sequences: List[Sequence],
                                cluster_labels: np.ndarray) -> List[FunctionalGroup]:
        """Create functional groups from clustering results."""
        groups = []
        unique_labels = set(cluster_labels)
        
        for group_id in unique_labels:
            # Get sequences in this group
            group_sequences = [seq for i, seq in enumerate(sequences) if cluster_labels[i] == group_id]
            
            if not group_sequences:
                continue
            
            # Find representative sequence (most central)
            if len(group_sequences) == 1:
                representative = group_sequences[0]
            else:
                # Calculate centroid or choose sequence with highest average identity to others
                best_score = -1
                representative = group_sequences[0]
                
                for candidate in group_sequences:
                    avg_identity = np.mean([
                        candidate.calculate_identity(other) 
                        for other in group_sequences if other != candidate
                    ])
                    if avg_identity > best_score:
                        best_score = avg_identity
                        representative = candidate
            
            # Calculate consensus sequence
            consensus = self._calculate_consensus_sequence(group_sequences)
            
            # Calculate conservation score
            conservation_score = self._calculate_group_conservation(group_sequences)
            
            # Calculate intra-group diversity
            intra_diversity = self._calculate_intra_group_diversity(group_sequences)
            
            group = FunctionalGroup(
                group_id=group_id,
                sequences=group_sequences,
                representative_sequence=representative,
                consensus_sequence=consensus,
                conservation_score=conservation_score,
                intra_group_diversity=intra_diversity
            )
            groups.append(group)
        
        return groups
    
    def _calculate_consensus_sequence(self, sequences: List[Sequence]) -> str:
        """Calculate consensus sequence for a group."""
        if not sequences:
            return ""
        
        # Find maximum length
        max_length = max(len(seq.sequence) for seq in sequences)
        consensus = []
        
        for pos in range(max_length):
            # Collect residues at this position
            residues = []
            for seq in sequences:
                if pos < len(seq.sequence):
                    residues.append(seq.sequence[pos])
            
            if not residues:
                consensus.append('-')
                continue
            
            # Find most common residue
            residue_counts = Counter(residues)
            most_common = residue_counts.most_common(1)[0][0]
            consensus.append(most_common)
        
        return ''.join(consensus)
    
    def _calculate_group_conservation(self, sequences: List[Sequence]) -> float:
        """Calculate conservation score for a group."""
        if len(sequences) <= 1:
            return 1.0
        
        # Calculate average pairwise identity within group
        identities = []
        for i in range(len(sequences)):
            for j in range(i + 1, len(sequences)):
                identity = sequences[i].calculate_identity(sequences[j])
                identities.append(identity)
        
        return np.mean(identities) if identities else 0.0
    
    def _calculate_intra_group_diversity(self, sequences: List[Sequence]) -> float:
        """Calculate diversity within a functional group."""
        if len(sequences) <= 1:
            return 0.0
        
        # Use Shannon diversity of unique sequences within group
        sequence_counts = Counter(seq.sequence for seq in sequences)
        n_total = len(sequences)
        
        shannon_diversity = 0.0
        for count in sequence_counts.values():
            p = count / n_total
            if p > 0:
                shannon_diversity -= p * np.log(p)
        
        return shannon_diversity
    
    def _calculate_functional_diversity_score(self, groups: List[FunctionalGroup]) -> float:
        """Calculate overall functional diversity score."""
        if not groups:
            return 0.0
        
        # Weight groups by size
        total_sequences = sum(len(group.sequences) for group in groups)
        
        # Calculate weighted diversity
        weighted_diversity = 0.0
        for group in groups:
            weight = len(group.sequences) / total_sequences
            # Functional diversity combines group diversity with conservation
            group_diversity = group.intra_group_diversity * (1.0 - group.conservation_score)
            weighted_diversity += weight * group_diversity
        
        # Add between-group diversity component
        n_groups = len(groups)
        between_group_component = np.log(n_groups) if n_groups > 1 else 0.0
        
        return weighted_diversity + between_group_component
    
    def _calculate_inter_group_distances(self, groups: List[FunctionalGroup]) -> List[float]:
        """Calculate distances between functional groups."""
        distances = []
        
        for i in range(len(groups)):
            for j in range(i + 1, len(groups)):
                # Distance between representative sequences
                distance = 1.0 - groups[i].representative_sequence.calculate_identity(
                    groups[j].representative_sequence
                )
                distances.append(distance)
        
        return distances
    
    def calculate_compositional_diversity(self, msa: MSACore) -> Dict[str, float]:
        """
        Calculate diversity based on amino acid composition.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, float]
            Compositional diversity metrics
        """
        if not msa.sequences:
            return {}
        
        # Calculate amino acid composition for each sequence
        compositions = []
        amino_acids = 'ACDEFGHIKLMNPQRSTVWY'
        
        for seq in msa.sequences:
            composition = {}
            sequence_str = seq.sequence.replace('-', '').replace('.', '')
            
            if not sequence_str:
                continue
            
            for aa in amino_acids:
                composition[aa] = sequence_str.count(aa) / len(sequence_str)
            compositions.append(composition)
        
        if not compositions:
            return {}
        
        # Calculate diversity of compositions
        composition_vectors = []
        for comp in compositions:
            vector = [comp.get(aa, 0.0) for aa in amino_acids]
            composition_vectors.append(vector)
        
        composition_vectors = np.array(composition_vectors)
        
        # Calculate pairwise compositional distances
        comp_distances = pairwise_distances(composition_vectors, metric='euclidean')
        mean_comp_distance = np.mean(comp_distances[np.triu_indices_from(comp_distances, k=1)])
        
        # Calculate composition entropy for each amino acid
        aa_entropies = {}
        for i, aa in enumerate(amino_acids):
            aa_freqs = composition_vectors[:, i]
            # Bin frequencies for entropy calculation
            bins = np.linspace(0, 1, 11)  # 10 bins
            hist, _ = np.histogram(aa_freqs, bins=bins, density=True)
            hist = hist / np.sum(hist)  # Normalize
            
            entropy = 0.0
            for p in hist:
                if p > 0:
                    entropy -= p * np.log(p)
            aa_entropies[aa] = entropy
        
        overall_comp_diversity = np.mean(list(aa_entropies.values()))
        
        return {
            'compositional_diversity': overall_comp_diversity,
            'mean_compositional_distance': mean_comp_distance,
            'amino_acid_entropies': aa_entropies,
            'most_variable_aa': max(aa_entropies.items(), key=lambda x: x[1])[0],
            'least_variable_aa': min(aa_entropies.items(), key=lambda x: x[1])[0]
        }
    
    def calculate_length_diversity(self, msa: MSACore) -> Dict[str, float]:
        """
        Calculate diversity based on sequence lengths.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, float]
            Length diversity metrics
        """
        if not msa.sequences:
            return {}
        
        lengths = [seq.length for seq in msa.sequences]
        
        # Basic length statistics
        mean_length = np.mean(lengths)
        std_length = np.std(lengths)
        cv_length = std_length / mean_length if mean_length > 0 else 0
        
        # Length diversity using Shannon index
        length_counts = Counter(lengths)
        n_total = len(lengths)
        
        length_shannon = 0.0
        for count in length_counts.values():
            p = count / n_total
            if p > 0:
                length_shannon -= p * np.log(p)
        
        return {
            'length_diversity': length_shannon,
            'length_coefficient_variation': cv_length,
            'unique_lengths': len(length_counts),
            'length_range': max(lengths) - min(lengths),
            'length_entropy': length_shannon
        }
    
    def generate_rarefaction_curve(self, 
                                 msa: MSACore,
                                 max_sample_size: Optional[int] = None,
                                 n_iterations: int = 100) -> List[Tuple[int, float]]:
        """
        Generate rarefaction curve for diversity analysis.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
        max_sample_size : Optional[int]
            Maximum sample size (defaults to MSA size)
        n_iterations : int
            Number of iterations for each sample size
            
        Returns
        -------
        List[Tuple[int, float]]
            Rarefaction curve as (sample_size, expected_diversity) pairs
        """
        if not msa.sequences:
            return []
        
        if max_sample_size is None:
            max_sample_size = len(msa)
        
        rarefaction_points = []
        sample_sizes = range(1, min(max_sample_size + 1, len(msa) + 1))
        
        for sample_size in sample_sizes:
            diversities = []
            
            for _ in range(n_iterations):
                # Random sample without replacement
                sampled_indices = np.random.choice(
                    len(msa), size=sample_size, replace=False
                )
                sampled_sequences = [msa.sequences[i] for i in sampled_indices]
                
                # Create temporary MSA for diversity calculation
                temp_msa = MSACore(sequences=sampled_sequences)
                
                # Calculate Shannon diversity
                diversity_metrics = self.calculate_basic_diversity_indices(temp_msa)
                diversities.append(diversity_metrics.get('shannon_index', 0.0))
            
            expected_diversity = np.mean(diversities)
            rarefaction_points.append((sample_size, expected_diversity))
        
        return rarefaction_points
    
    def calculate_comprehensive_diversity(self, msa: MSACore) -> DiversityMetrics:
        """
        Calculate comprehensive diversity metrics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        DiversityMetrics
            Comprehensive diversity analysis results
        """
        if not msa.sequences:
            return DiversityMetrics(
                shannon_index=0.0, simpson_index=0.0, effective_sequences=0.0,
                phylogenetic_diversity=0.0, mean_pairwise_distance=0.0,
                functional_diversity=0.0, structural_diversity=0.0,
                composition_diversity=0.0, length_diversity=0.0,
                rarefaction_curve=[], diversity_profile=[]
            )
        
        # Basic diversity indices
        basic_diversity = self.calculate_basic_diversity_indices(msa)
        
        # Phylogenetic diversity
        phylo_diversity = self.calculate_phylogenetic_diversity(msa)
        
        # Functional diversity
        func_diversity = self.calculate_functional_diversity(msa)
        
        # Compositional diversity
        comp_diversity = self.calculate_compositional_diversity(msa)
        
        # Length diversity
        length_diversity = self.calculate_length_diversity(msa)
        
        # Rarefaction curve
        rarefaction_curve = self.generate_rarefaction_curve(msa)
        
        # Diversity profile (diversity at different scales)
        diversity_profile = self._calculate_diversity_profile(msa)
        
        return DiversityMetrics(
            shannon_index=basic_diversity.get('shannon_index', 0.0),
            simpson_index=basic_diversity.get('simpson_index', 0.0),
            effective_sequences=basic_diversity.get('effective_sequences', 0.0),
            phylogenetic_diversity=phylo_diversity.get('phylogenetic_diversity', 0.0),
            mean_pairwise_distance=phylo_diversity.get('mean_pairwise_distance', 0.0),
            functional_diversity=func_diversity.get('functional_diversity', 0.0),
            structural_diversity=0.0,  # Placeholder for future structural analysis
            composition_diversity=comp_diversity.get('compositional_diversity', 0.0),
            length_diversity=length_diversity.get('length_diversity', 0.0),
            rarefaction_curve=rarefaction_curve,
            diversity_profile=diversity_profile
        )
    
    def _calculate_diversity_profile(self, msa: MSACore) -> List[float]:
        """Calculate diversity profile at different scales."""
        # This would typically involve calculating diversity at different
        # hierarchical levels or with different distance thresholds
        # For now, return a simple profile based on clustering at different levels
        
        if len(msa) < 2:
            return [0.0]
        
        distances = self._calculate_pairwise_distances(msa.sequences)
        linkage_matrix = linkage(squareform(distances), method='average')
        
        profile = []
        max_clusters = min(10, len(msa))
        
        for n_clusters in range(1, max_clusters + 1):
            cluster_labels = fcluster(linkage_matrix, n_clusters, criterion='maxclust')
            
            # Calculate diversity based on cluster distribution
            cluster_counts = Counter(cluster_labels)
            n_total = len(cluster_labels)
            
            diversity = 0.0
            for count in cluster_counts.values():
                p = count / n_total
                if p > 0:
                    diversity -= p * np.log(p)
            
            profile.append(diversity)
        
        return profile 