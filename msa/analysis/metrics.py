"""
MSA metrics and conservation analysis.

This module provides specialized metrics calculation and conservation analysis
for MSA data, extracted from the original msa_analyzer.py file.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass
from collections import Counter
import matplotlib.pyplot as plt
from scipy import stats

from ..core.msa_core import MSACore
from ..core.sequence import Sequence


@dataclass
class ConservationScore:
    """Data structure for conservation scores."""
    position: int
    entropy: float
    conservation: float
    gap_fraction: float
    most_common_residue: str
    most_common_frequency: float


@dataclass
class MSAQualityMetrics:
    """Data structure for MSA quality metrics."""
    # Coverage metrics
    mean_coverage: float
    position_coverage: List[float]
    
    # Conservation metrics  
    mean_conservation: float
    conservation_scores: List[ConservationScore]
    
    # Identity metrics
    mean_pairwise_identity: float
    identity_distribution: List[float]
    
    # Quality scores
    overall_quality_score: float
    position_quality_scores: List[float]


class ConservationAnalyzer:
    """
    Analyzer for sequence conservation in MSAs.
    
    This class provides detailed analysis of conservation patterns,
    entropy calculations, and position-specific conservation metrics.
    """
    
    def __init__(self):
        """Initialize the conservation analyzer."""
        self._cache = {}
    
    def calculate_position_conservation(self, 
                                      msa: MSACore,
                                      position: int) -> ConservationScore:
        """
        Calculate conservation score for a specific position.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
        position : int
            Position to analyze (0-indexed)
            
        Returns
        -------
        ConservationScore
            Conservation metrics for the position
        """
        if position >= msa.max_length:
            raise ValueError(f"Position {position} exceeds MSA length {msa.max_length}")
        
        # Collect residues at this position
        residues = []
        gap_count = 0
        
        for seq in msa.sequences:
            if position < len(seq.sequence):
                residue = seq.sequence[position]
                if residue in '-.':
                    gap_count += 1
                else:
                    residues.append(residue)
        
        if not residues:
            # All gaps at this position
            return ConservationScore(
                position=position,
                entropy=0.0,
                conservation=0.0,
                gap_fraction=1.0,
                most_common_residue='-',
                most_common_frequency=1.0
            )
        
        # Calculate residue frequencies
        residue_counts = Counter(residues)
        total_residues = len(residues)
        
        # Calculate entropy
        entropy = 0.0
        for count in residue_counts.values():
            p = count / total_residues
            if p > 0:
                entropy -= p * np.log2(p)
        
        # Conservation score (1 - normalized entropy)
        max_entropy = np.log2(min(20, len(residue_counts)))  # 20 amino acids max
        conservation = 1.0 - (entropy / max_entropy) if max_entropy > 0 else 1.0
        
        # Most common residue
        most_common_residue, most_common_count = residue_counts.most_common(1)[0]
        most_common_frequency = most_common_count / total_residues
        
        # Gap fraction
        gap_fraction = gap_count / len(msa)
        
        return ConservationScore(
            position=position,
            entropy=entropy,
            conservation=conservation,
            gap_fraction=gap_fraction,
            most_common_residue=most_common_residue,
            most_common_frequency=most_common_frequency
        )
    
    def calculate_conservation_profile(self, msa: MSACore) -> List[ConservationScore]:
        """
        Calculate conservation profile for entire MSA.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        List[ConservationScore]
            Conservation scores for each position
        """
        conservation_profile = []
        
        for pos in range(msa.max_length):
            score = self.calculate_position_conservation(msa, pos)
            conservation_profile.append(score)
        
        return conservation_profile
    
    def identify_conserved_regions(self, 
                                 conservation_profile: List[ConservationScore],
                                 min_conservation: float = 0.8,
                                 min_region_length: int = 3) -> List[Tuple[int, int]]:
        """
        Identify conserved regions in the MSA.
        
        Parameters
        ----------
        conservation_profile : List[ConservationScore]
            Conservation scores for each position
        min_conservation : float
            Minimum conservation score to consider as conserved
        min_region_length : int
            Minimum length of conserved regions
            
        Returns
        -------
        List[Tuple[int, int]]
            List of (start, end) positions of conserved regions
        """
        conserved_positions = [
            i for i, score in enumerate(conservation_profile)
            if score.conservation >= min_conservation
        ]
        
        if not conserved_positions:
            return []
        
        # Group consecutive positions into regions
        regions = []
        current_start = conserved_positions[0]
        current_end = conserved_positions[0]
        
        for pos in conserved_positions[1:]:
            if pos == current_end + 1:
                # Consecutive position
                current_end = pos
            else:
                # Gap found, save current region if long enough
                if current_end - current_start + 1 >= min_region_length:
                    regions.append((current_start, current_end))
                current_start = pos
                current_end = pos
        
        # Add final region
        if current_end - current_start + 1 >= min_region_length:
            regions.append((current_start, current_end))
        
        return regions
    
    def calculate_conservation_windows(self, 
                                     conservation_profile: List[ConservationScore],
                                     window_size: int = 5) -> List[float]:
        """
        Calculate sliding window conservation scores.
        
        Parameters
        ----------
        conservation_profile : List[ConservationScore]
            Conservation scores for each position
        window_size : int
            Size of sliding window
            
        Returns
        -------
        List[float]
            Mean conservation scores for each window
        """
        if len(conservation_profile) < window_size:
            return []
        
        window_scores = []
        for i in range(len(conservation_profile) - window_size + 1):
            window_conservation = [
                score.conservation 
                for score in conservation_profile[i:i + window_size]
            ]
            window_scores.append(np.mean(window_conservation))
        
        return window_scores
    
    def analyze_conservation_patterns(self, 
                                    msa: MSACore) -> Dict[str, Any]:
        """
        Comprehensive conservation pattern analysis.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, Any]
            Comprehensive conservation analysis results
        """
        # Calculate conservation profile
        conservation_profile = self.calculate_conservation_profile(msa)
        
        # Extract conservation values
        conservation_values = [score.conservation for score in conservation_profile]
        entropy_values = [score.entropy for score in conservation_profile]
        gap_fractions = [score.gap_fraction for score in conservation_profile]
        
        # Identify conserved regions
        conserved_regions = self.identify_conserved_regions(conservation_profile)
        
        # Calculate window scores
        window_scores = self.calculate_conservation_windows(conservation_profile)
        
        # Statistics
        analysis_results = {
            'conservation_profile': conservation_profile,
            'mean_conservation': np.mean(conservation_values),
            'std_conservation': np.std(conservation_values),
            'median_conservation': np.median(conservation_values),
            'min_conservation': np.min(conservation_values),
            'max_conservation': np.max(conservation_values),
            
            'mean_entropy': np.mean(entropy_values),
            'std_entropy': np.std(entropy_values),
            
            'mean_gap_fraction': np.mean(gap_fractions),
            'max_gap_fraction': np.max(gap_fractions),
            
            'conserved_regions': conserved_regions,
            'n_conserved_regions': len(conserved_regions),
            'total_conserved_positions': sum(end - start + 1 for start, end in conserved_regions),
            
            'window_scores': window_scores,
            
            'highly_conserved_positions': sum(1 for c in conservation_values if c > 0.9),
            'moderately_conserved_positions': sum(1 for c in conservation_values if 0.7 <= c <= 0.9),
            'poorly_conserved_positions': sum(1 for c in conservation_values if c < 0.5),
        }
        
        return analysis_results


class MSAMetrics:
    """
    Comprehensive MSA quality metrics calculator.
    
    This class provides various metrics for assessing MSA quality,
    including coverage, conservation, and identity measures.
    """
    
    def __init__(self):
        """Initialize the MSA metrics calculator."""
        self.conservation_analyzer = ConservationAnalyzer()
    
    def calculate_coverage_metrics(self, msa: MSACore) -> Dict[str, Any]:
        """
        Calculate position-wise and overall coverage metrics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        Dict[str, Any]
            Coverage metrics
        """
        if not msa.sequences:
            return {}
        
        max_length = msa.max_length
        position_coverage = []
        
        # Calculate coverage for each position
        for pos in range(max_length):
            non_gap_count = 0
            for seq in msa.sequences:
                if pos < len(seq.sequence) and seq.sequence[pos] not in '-.':
                    non_gap_count += 1
            
            coverage = non_gap_count / len(msa) if len(msa) > 0 else 0.0
            position_coverage.append(coverage)
        
        # Overall coverage statistics
        mean_coverage = np.mean(position_coverage) if position_coverage else 0.0
        
        return {
            'position_coverage': position_coverage,
            'mean_coverage': mean_coverage,
            'median_coverage': np.median(position_coverage) if position_coverage else 0.0,
            'std_coverage': np.std(position_coverage) if position_coverage else 0.0,
            'min_coverage': np.min(position_coverage) if position_coverage else 0.0,
            'max_coverage': np.max(position_coverage) if position_coverage else 0.0,
            'well_covered_positions': sum(1 for c in position_coverage if c > 0.8),
            'poorly_covered_positions': sum(1 for c in position_coverage if c < 0.3)
        }
    
    def calculate_identity_metrics(self, 
                                 msa: MSACore,
                                 sample_size: Optional[int] = 1000) -> Dict[str, Any]:
        """
        Calculate pairwise identity metrics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
        sample_size : Optional[int]
            Maximum number of pairs to sample for efficiency
            
        Returns
        -------
        Dict[str, Any]
            Identity metrics
        """
        if len(msa) < 2:
            return {}
        
        sequences = msa.sequences
        n_sequences = len(sequences)
        
        # Sample pairs if MSA is large
        if sample_size and n_sequences * (n_sequences - 1) // 2 > sample_size:
            # Random sampling of sequence pairs
            indices = np.random.choice(n_sequences, size=min(sample_size, n_sequences), replace=False)
            sequences = [sequences[i] for i in indices]
        
        # Calculate pairwise identities
        identities = []
        for i in range(len(sequences)):
            for j in range(i + 1, len(sequences)):
                identity = sequences[i].calculate_identity(sequences[j])
                identities.append(identity)
        
        if not identities:
            return {}
        
        # Calculate identity to query (first sequence)
        query_identities = []
        if msa.query_sequence:
            for seq in sequences[1:]:  # Skip query itself
                identity = seq.calculate_identity(msa.query_sequence)
                query_identities.append(identity)
        
        results = {
            'identity_distribution': identities,
            'mean_pairwise_identity': np.mean(identities),
            'median_pairwise_identity': np.median(identities),
            'std_pairwise_identity': np.std(identities),
            'min_pairwise_identity': np.min(identities),
            'max_pairwise_identity': np.max(identities),
            'pairwise_comparisons': len(identities)
        }
        
        if query_identities:
            results.update({
                'mean_query_identity': np.mean(query_identities),
                'median_query_identity': np.median(query_identities),
                'std_query_identity': np.std(query_identities),
                'min_query_identity': np.min(query_identities),
                'max_query_identity': np.max(query_identities)
            })
        
        return results
    
    def calculate_position_quality_scores(self, msa: MSACore) -> List[float]:
        """
        Calculate quality score for each position.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        List[float]
            Quality scores for each position (0-1, higher is better)
        """
        if not msa.sequences:
            return []
        
        # Get coverage and conservation data
        coverage_metrics = self.calculate_coverage_metrics(msa)
        conservation_profile = self.conservation_analyzer.calculate_conservation_profile(msa)
        
        position_coverage = coverage_metrics['position_coverage']
        
        # Calculate composite quality score for each position
        quality_scores = []
        for i, (coverage, conservation_score) in enumerate(zip(position_coverage, conservation_profile)):
            # Weighted combination of coverage and conservation
            quality = (0.6 * coverage + 0.4 * conservation_score.conservation)
            
            # Penalty for high gap content
            gap_penalty = max(0, conservation_score.gap_fraction - 0.5) * 0.5
            quality = max(0, quality - gap_penalty)
            
            quality_scores.append(quality)
        
        return quality_scores
    
    def calculate_overall_quality_score(self, msa: MSACore) -> float:
        """
        Calculate overall MSA quality score.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        float
            Overall quality score (0-1, higher is better)
        """
        if not msa.sequences:
            return 0.0
        
        # Component scores
        coverage_metrics = self.calculate_coverage_metrics(msa)
        identity_metrics = self.calculate_identity_metrics(msa)
        conservation_analysis = self.conservation_analyzer.analyze_conservation_patterns(msa)
        
        # Normalize and weight components
        coverage_score = coverage_metrics.get('mean_coverage', 0.0)
        conservation_score = conservation_analysis.get('mean_conservation', 0.0)
        
        # Identity diversity (prefer moderate identity, not too high or too low)
        mean_identity = identity_metrics.get('mean_pairwise_identity', 0.5)
        identity_score = 1.0 - abs(mean_identity - 0.7)  # Optimal around 70% identity
        identity_score = max(0, identity_score)
        
        # Sequence count factor (more sequences generally better, but diminishing returns)
        n_sequences = len(msa)
        sequence_score = min(1.0, np.log(n_sequences + 1) / np.log(100))  # Normalized to log scale
        
        # Weighted combination
        overall_quality = (
            0.3 * coverage_score +
            0.3 * conservation_score +
            0.2 * identity_score +
            0.2 * sequence_score
        )
        
        return overall_quality
    
    def calculate_comprehensive_metrics(self, msa: MSACore) -> MSAQualityMetrics:
        """
        Calculate comprehensive MSA quality metrics.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        MSAQualityMetrics
            Comprehensive quality metrics
        """
        if not msa.sequences:
            return MSAQualityMetrics(
                mean_coverage=0.0, position_coverage=[],
                mean_conservation=0.0, conservation_scores=[],
                mean_pairwise_identity=0.0, identity_distribution=[],
                overall_quality_score=0.0, position_quality_scores=[]
            )
        
        # Calculate component metrics
        coverage_metrics = self.calculate_coverage_metrics(msa)
        identity_metrics = self.calculate_identity_metrics(msa)
        conservation_profile = self.conservation_analyzer.calculate_conservation_profile(msa)
        position_quality_scores = self.calculate_position_quality_scores(msa)
        overall_quality_score = self.calculate_overall_quality_score(msa)
        
        return MSAQualityMetrics(
            mean_coverage=coverage_metrics['mean_coverage'],
            position_coverage=coverage_metrics['position_coverage'],
            mean_conservation=np.mean([s.conservation for s in conservation_profile]),
            conservation_scores=conservation_profile,
            mean_pairwise_identity=identity_metrics.get('mean_pairwise_identity', 0.0),
            identity_distribution=identity_metrics.get('identity_distribution', []),
            overall_quality_score=overall_quality_score,
            position_quality_scores=position_quality_scores
        )
    
    def generate_quality_report(self, metrics: MSAQualityMetrics) -> str:
        """
        Generate a human-readable quality report.
        
        Parameters
        ----------
        metrics : MSAQualityMetrics
            Quality metrics to report
            
        Returns
        -------
        str
            Quality report text
        """
        report_lines = []
        
        # Header
        report_lines.append("MSA Quality Assessment Report")
        report_lines.append("=" * 35)
        report_lines.append("")
        
        # Overall quality
        quality_grade = self._get_quality_grade(metrics.overall_quality_score)
        report_lines.append(f"Overall Quality Score: {metrics.overall_quality_score:.3f} ({quality_grade})")
        report_lines.append("")
        
        # Coverage assessment
        report_lines.append("Coverage Assessment:")
        report_lines.append(f"  Mean coverage: {metrics.mean_coverage:.1%}")
        
        well_covered = sum(1 for c in metrics.position_coverage if c > 0.8)
        poorly_covered = sum(1 for c in metrics.position_coverage if c < 0.3)
        
        report_lines.append(f"  Well-covered positions (>80%): {well_covered}")
        report_lines.append(f"  Poorly-covered positions (<30%): {poorly_covered}")
        report_lines.append("")
        
        # Conservation assessment
        report_lines.append("Conservation Assessment:")
        report_lines.append(f"  Mean conservation: {metrics.mean_conservation:.3f}")
        
        highly_conserved = sum(1 for s in metrics.conservation_scores if s.conservation > 0.9)
        poorly_conserved = sum(1 for s in metrics.conservation_scores if s.conservation < 0.5)
        
        report_lines.append(f"  Highly conserved positions (>0.9): {highly_conserved}")
        report_lines.append(f"  Poorly conserved positions (<0.5): {poorly_conserved}")
        report_lines.append("")
        
        # Identity assessment
        report_lines.append("Identity Assessment:")
        report_lines.append(f"  Mean pairwise identity: {metrics.mean_pairwise_identity:.1%}")
        
        if metrics.identity_distribution:
            identity_std = np.std(metrics.identity_distribution)
            report_lines.append(f"  Identity diversity (std): {identity_std:.3f}")
        report_lines.append("")
        
        # Recommendations
        report_lines.append("Recommendations:")
        if metrics.overall_quality_score > 0.8:
            report_lines.append("  Excellent MSA quality. Suitable for downstream analysis.")
        elif metrics.overall_quality_score > 0.6:
            report_lines.append("  Good MSA quality. Minor improvements possible.")
        elif metrics.overall_quality_score > 0.4:
            report_lines.append("  Moderate MSA quality. Consider filtering or refinement.")
        else:
            report_lines.append("  Poor MSA quality. Significant improvement needed.")
        
        if metrics.mean_coverage < 0.7:
            report_lines.append("  - Consider removing poorly covered positions")
        
        if poorly_conserved > len(metrics.conservation_scores) * 0.5:
            report_lines.append("  - High variability detected. Check alignment quality")
        
        if metrics.mean_pairwise_identity < 0.3:
            report_lines.append("  - Very low sequence identity. Verify homology")
        elif metrics.mean_pairwise_identity > 0.95:
            report_lines.append("  - Very high sequence identity. Consider removing redundancy")
        
        return '\n'.join(report_lines)
    
    def _get_quality_grade(self, score: float) -> str:
        """Convert quality score to letter grade."""
        if score >= 0.9:
            return "A+"
        elif score >= 0.8:
            return "A"
        elif score >= 0.7:
            return "B+"
        elif score >= 0.6:
            return "B"
        elif score >= 0.5:
            return "C+"
        elif score >= 0.4:
            return "C"
        elif score >= 0.3:
            return "D"
        else:
            return "F"
    
    def compare_msa_quality(self, 
                          metrics1: MSAQualityMetrics, 
                          metrics2: MSAQualityMetrics) -> Dict[str, Any]:
        """
        Compare quality metrics between two MSAs.
        
        Parameters
        ----------
        metrics1 : MSAQualityMetrics
            First MSA metrics
        metrics2 : MSAQualityMetrics
            Second MSA metrics
            
        Returns
        -------
        Dict[str, Any]
            Comparison results
        """
        comparison = {
            'overall_quality_difference': metrics2.overall_quality_score - metrics1.overall_quality_score,
            'coverage_difference': metrics2.mean_coverage - metrics1.mean_coverage,
            'conservation_difference': metrics2.mean_conservation - metrics1.mean_conservation,
            'identity_difference': metrics2.mean_pairwise_identity - metrics1.mean_pairwise_identity,
            
            'better_overall': metrics2.overall_quality_score > metrics1.overall_quality_score,
            'better_coverage': metrics2.mean_coverage > metrics1.mean_coverage,
            'better_conservation': metrics2.mean_conservation > metrics1.mean_conservation,
            
            'improvement_percentage': (
                (metrics2.overall_quality_score - metrics1.overall_quality_score) / 
                metrics1.overall_quality_score * 100
            ) if metrics1.overall_quality_score > 0 else float('inf')
        }
        
        return comparison 