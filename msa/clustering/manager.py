"""
Manager for MSA clustering operations.

This module provides the ClusteringManager class that coordinates between
MSA data and clustering algorithms, providing a high-level interface for
performing clustering operations.
"""

import logging
from typing import Dict, Any, List, Optional, TYPE_CHECKING, Union, Tuple
from .interfaces import ClusteringResult
from .factory import ClusteringFactory

# Type checking imports - support both legacy and new MSA types
if TYPE_CHECKING:
    try:
        from msa_pipeline import MSA as LegacyMSA
        from ..core.msa_core import MSACore
        MSAType = Union[LegacyMSA, MSACore]
    except ImportError:
        from ..core.msa_core import MSACore
        MSAType = MSACore


class ClusteringManager:
    """
    Manager for MSA clustering operations.
    
    This class provides a high-level interface for performing clustering
    on MSA data using various algorithms. It handles algorithm selection,
    parameter validation, and result standardization. Supports both
    legacy MSA objects and new MSACore objects.
    """
    
    def __init__(self, msa: 'MSAType'):
        """
        Initialize the clustering manager.
        
        Parameters
        ----------
        msa : Union[MSA, MSACore]
            The MSA instance to perform clustering on.
            Supports both legacy MSA and new MSACore objects.
        """
        self.msa = msa
        self._factory = ClusteringFactory()
        self._last_result: Optional[ClusteringResult] = None
        
        # Detect MSA type for proper handling
        self._msa_type = self._detect_msa_type(msa)
    
    def _detect_msa_type(self, msa) -> str:
        """
        Detect the type of MSA object to handle appropriately.
        
        Parameters
        ----------
        msa : Union[MSA, MSACore]
            The MSA object to analyze
            
        Returns
        -------
        str
            Type of MSA object ('legacy' or 'core')
        """
        # Check for MSACore first (preferred)
        if hasattr(msa, 'sequence_strings') and hasattr(msa, 'sequences'):
            # MSACore has sequence_strings property and sequences returns Sequence objects
            if hasattr(msa.sequences[0], 'sequence') if msa.sequences else True:
                return 'core'
        
        # Check for legacy MSA
        if hasattr(msa, 'sequences') and hasattr(msa, 'descriptions'):
            # Legacy MSA has sequences and descriptions as simple lists
            return 'legacy'
        
        raise TypeError(f"Unsupported MSA type: {type(msa)}")
    
    def _extract_sequences_and_descriptions(self) -> tuple[List[str], List[str]]:
        """
        Extract sequence strings and descriptions from MSA object.
        
        Returns
        -------
        tuple[List[str], List[str]]
            Tuple of (sequence_strings, descriptions)
        """
        if self._msa_type == 'core':
            # MSACore object - use sequence_strings property and descriptions property
            sequences = self.msa.sequence_strings  # Returns List[str]
            descriptions = self.msa.descriptions   # Returns List[str]
        elif self._msa_type == 'legacy':
            # Legacy MSA object - sequences and descriptions are already string lists
            sequences = list(self.msa.sequences)    # Ensure it's a list
            descriptions = list(self.msa.descriptions)  # Ensure it's a list
        else:
            raise RuntimeError(f"Unknown MSA type: {self._msa_type}")
        
        return sequences, descriptions
    
    def cluster(self, 
        algorithm: str = 'dbscan',
        sequences: Optional[List[str]] = None,
        descriptions: Optional[List[str]] = None,
        gap_cutoff: float = 0.4,  # Updated to 40% as per requirements
        encoding_method: str = 'onehot',
        **kwargs
    ) -> ClusteringResult:
        """
        Perform clustering using specified algorithm.
        
        This method implements the exact logic from dbscan_cluster.py, preserving
        lowercase characters and handling them correctly for different encoding methods.
        
        Parameters
        ----------
        algorithm : str, optional
            Name of the clustering algorithm to use.
            Defaults to 'dbscan'.
        sequences : List[str], optional
            Sequences to cluster. If None, extracts from MSA object.
            Defaults to None.
        descriptions : List[str], optional
            Descriptions for sequences. If None, extracts from MSA object.
            Defaults to None.
        gap_cutoff : float, optional
            Maximum fraction of gaps allowed in a sequence.
            Defaults to 0.4 (40%).
        encoding_method : str, optional
            Encoding method ('onehot' or 'MSAtransformer').
            Defaults to 'onehot'.
        **kwargs
            Algorithm-specific parameters.
            
        Returns
        -------
        ClusteringResult
            Clustering results in standard format.
            
        Raises
        ------
        ValueError
            If algorithm is unknown or parameters are invalid.
        RuntimeError
            If clustering fails.
        """
        # Extract sequences and descriptions from MSA if not provided
        if sequences is None or descriptions is None:
            msa_sequences, msa_descriptions = self._extract_sequences_and_descriptions()
        if sequences is None:
                sequences = msa_sequences
        if descriptions is None:
                descriptions = msa_descriptions
        
        # Validate input
        if len(sequences) != len(descriptions):
            raise ValueError(
                f"Sequences and descriptions must have the same length: "
                f"{len(sequences)} vs {len(descriptions)}"
            )
        
        if not sequences:
            raise ValueError("No sequences provided for clustering")
        
        try:
            logging.info(
                f"Starting {algorithm} clustering on {len(sequences)} sequences "
                f"with gap_cutoff={gap_cutoff}, encoding_method={encoding_method}"
            )
            
            # Step 1: Filter sequences by gap content (following dbscan_cluster.py logic)
            original_sequences, filtered_descriptions, filtered_indices = self._filter_sequences_by_gap_content(
                sequences, descriptions, gap_cutoff
            )
            
            logging.info(f"Sequences after gap filtering: {len(sequences)} -> {len(original_sequences)}")
            
            # Step 2: Create sequences without lowercase for clustering (following dbscan_cluster.py logic)
            filtered_sequences = [
                ''.join([char for char in seq if not char.islower()])
                for seq in original_sequences
            ]
            
            if not filtered_sequences:
                raise RuntimeError(f"No sequences remain after gap filtering with gap_cutoff={gap_cutoff}")
            
            # Step 3: Prepare data for clustering (following dbscan_cluster.py DataFrame structure)
            import pandas as pd
            
            df = pd.DataFrame({
                'SequenceName': filtered_descriptions,
                'sequence': filtered_sequences,  # Lowercase removed for clustering
                'original_sequence': original_sequences,  # Original sequences preserved
                'original_index': filtered_indices
            })
            
            # Separate query sequence (first sequence)
            query_df = df.iloc[:1].copy()
            df = df.iloc[1:].copy()
            
            if df.empty:
                logging.warning("No sequences to cluster after removing query")
                # Return minimal result
                return ClusteringResult(
                    clusters=[],
                    cluster_metadata={},
                    clustering_assignments=[],
                    best_params={'eps': None, 'min_samples': None, 'n_clusters': 0}
                )
            
            # Step 4: Encode sequences based on method (following dbscan_cluster.py logic)
            if encoding_method == 'onehot':
                # Use lowercase-removed sequences for onehot encoding
                sequences_to_encode = df['sequence'].tolist()
            elif encoding_method in ['MSAtransformer', 'esm_embedding']:
                # Use original sequences (with lowercase) for transformer models
                sequences_to_encode = df['original_sequence'].tolist()
            else:
                raise ValueError(f"Unsupported encoding method: {encoding_method}")
            
            # Check sequence length consistency for onehot encoding
            if encoding_method == 'onehot':
                seq_lens = set(len(seq) for seq in sequences_to_encode)
                if len(seq_lens) > 1:
                    raise ValueError(
                        f"All sequences must be of the same length for onehot encoding. "
                        f"Found lengths: {seq_lens}"
                    )
                max_seq_len = max(seq_lens) if seq_lens else 0
            else:
                # For transformer models, calculate max length but allow padding
                max_seq_len = max(len(seq) for seq in sequences_to_encode) if sequences_to_encode else 0
            
            # Encode sequences using the unified SequenceEncoder
            from ..encoding.sequence_encoder import SequenceEncoder
            encoder = SequenceEncoder()
            
            encoded_seqs = encoder.encode(
                sequences=sequences_to_encode,
                method=encoding_method,
                max_seq_len=max_seq_len,
                **kwargs
            )
            
            logging.info(f"Encoded sequences shape: {encoded_seqs.shape}")
            
            # Step 5: Perform clustering
            clusterer = self._factory.create_clusterer(algorithm)
            
            # Prepare parameters for clustering algorithm
            cluster_params = kwargs.copy()
            cluster_params['gap_cutoff'] = gap_cutoff
            cluster_params['encoding_method'] = encoding_method
            
            # Use the clustering algorithm's cluster method instead of _perform_clustering
            cluster_result = clusterer.cluster(
                sequences=sequences_to_encode,
                descriptions=df['SequenceName'].tolist(),
                **cluster_params
            )
            
            # Extract cluster labels from result
            # The cluster_result includes all sequences (including query), but df excludes query
            # So we need to map the labels correctly
            all_cluster_labels = cluster_result.cluster_labels
            
            # Skip the first label (query sequence) and take the rest for df
            if len(all_cluster_labels) > len(df):
                cluster_labels_for_df = all_cluster_labels[1:len(df)+1]  # Skip query, take df length
            else:
                cluster_labels_for_df = all_cluster_labels[:len(df)]  # Take up to df length
            
            # Ensure we have the right number of labels
            if len(cluster_labels_for_df) != len(df):
                # Pad with noise labels if needed
                while len(cluster_labels_for_df) < len(df):
                    cluster_labels_for_df.append(-1)
                # Truncate if too many
                cluster_labels_for_df = cluster_labels_for_df[:len(df)]
            
            # Step 6: Process results (following dbscan_cluster.py result structure)
            df['cluster_label'] = cluster_labels_for_df
            
            # Calculate cluster sizes and sort
            cluster_sizes = df['cluster_label'].value_counts().to_dict()
            df['cluster_size'] = df['cluster_label'].map(cluster_sizes)
            df = df.sort_values(by=['cluster_size'], ascending=[False])
            
            # Generate cluster metadata
            clusters = [x for x in df['cluster_label'].unique() if x >= 0]
            unclustered = len(df.loc[df['cluster_label'] == -1])
            
            logging.info(
                f"Found {len(clusters)} clusters, {unclustered} of {len(df)} not clustered "
                f"({unclustered/len(df):.2f})"
            )
            
            # Create clustering result following dbscan_cluster.py format
            result = ClusteringResult(
                cluster_labels=df['cluster_label'].tolist(),
                cluster_centers=None,  # DBSCAN doesn't have centers
                cluster_probabilities=None,  # DBSCAN is hard clustering
                metadata={
                    'n_clusters': len(clusters),
                    'n_noise': unclustered,
                    'algorithm_type': algorithm,
                    'encoding_method': encoding_method,
                    'gap_cutoff': gap_cutoff,
                    'cluster_metadata': self._create_cluster_metadata(df, query_df),
                    'clustering_assignments': df,
                    'original_sequences': df['original_sequence'].tolist(),
                    'processed_sequences': df['sequence'].tolist()
                },
                algorithm_params=kwargs,
                algorithm_name=algorithm,
                sequences=df['original_sequence'].tolist(),  # Use original sequences with lowercase
                descriptions=df['SequenceName'].tolist()
            )
            
            # Store result for later access
            self._last_result = result
            
            logging.info(f"Clustering completed: {len(clusters)} clusters, {unclustered} noise points")
            
            return result
            
        except Exception as e:
            logging.error(f"Clustering failed with {algorithm}: {str(e)}")
            raise RuntimeError(f"Clustering failed: {str(e)}") from e
    
    def available_algorithms(self) -> List[str]:
        """
        Get list of available clustering algorithms.
        
        Returns
        -------
        List[str]
            List of available algorithm names.
        """
        return self._factory.list_algorithms()
    
    def get_algorithm_params(self, algorithm: str) -> Dict[str, Any]:
        """
        Get default parameters for specified algorithm.
        
        Parameters
        ----------
        algorithm : str
            Name of the algorithm.
            
        Returns
        -------
        Dict[str, Any]
            Default parameters for the algorithm.
            
        Raises
        ------
        ValueError
            If algorithm is unknown.
        """
        try:
            clusterer = self._factory.create_clusterer(algorithm)
            return clusterer.get_default_params()
        except Exception as e:
            raise ValueError(f"Could not get parameters for algorithm '{algorithm}': {e}")
    
    def get_algorithm_param_descriptions(self, algorithm: str) -> Dict[str, str]:
        """
        Get parameter descriptions for specified algorithm.
        
        Parameters
        ----------
        algorithm : str
            Name of the algorithm.
            
        Returns
        -------
        Dict[str, str]
            Parameter descriptions for the algorithm.
            
        Raises
        ------
        ValueError
            If algorithm is unknown.
        """
        try:
            clusterer = self._factory.create_clusterer(algorithm)
            return clusterer.get_param_descriptions()
        except Exception as e:
            raise ValueError(f"Could not get parameter descriptions for algorithm '{algorithm}': {e}")
    
    def get_algorithm_info(self, algorithm: str) -> Dict[str, Any]:
        """
        Get comprehensive information about an algorithm.
        
        Parameters
        ----------
        algorithm : str
            Name of the algorithm.
            
        Returns
        -------
        Dict[str, Any]
            Information about the algorithm including parameters and descriptions.
        """
        return self._factory.get_algorithm_info(algorithm)
    
    def compare_algorithms(self, 
        algorithms: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, ClusteringResult]:
        """
        Compare multiple clustering algorithms on the same data.
        
        Parameters
        ----------
        algorithms : List[str], optional
            List of algorithms to compare. If None, uses all available algorithms.
            Defaults to None.
        **kwargs
            Common parameters to use for all algorithms.
            
        Returns
        -------
        Dict[str, ClusteringResult]
            Results for each algorithm.
        """
        if algorithms is None:
            algorithms = self.available_algorithms()
        
        results = {}
        for algorithm in algorithms:
            try:
                logging.info(f"Running comparison clustering with {algorithm}")
                result = self.cluster(algorithm, **kwargs)
                results[algorithm] = result
            except Exception as e:
                logging.warning(f"Algorithm {algorithm} failed in comparison: {e}")
                continue
                
        return results
    
    def get_last_result(self) -> Optional[ClusteringResult]:
        """
        Get the result of the last clustering operation.
        
        Returns
        -------
        ClusteringResult or None
            Last clustering result, or None if no clustering has been performed.
        """
        return self._last_result
    
    def save_clustering_result(self, 
        result: ClusteringResult, 
        output_path: str,
        format: str = 'tsv'
    ) -> None:
        """
        Save clustering result to file.
        
        Parameters
        ----------
        result : ClusteringResult
            Clustering result to save.
        output_path : str
            Path to save the result.
        format : str, optional
            Output format ('tsv', 'csv', 'json').
            Defaults to 'tsv'.
            
        Raises
        ------
        ValueError
            If format is not supported.
        NotImplementedError
            If format is not yet implemented.
        """
        # This is a placeholder for future implementation
        # Could save cluster assignments, metadata, etc.
        supported_formats = ['tsv', 'csv', 'json']
        if format not in supported_formats:
            raise ValueError(f"Unsupported format: {format}. Supported: {supported_formats}")
        
        # TODO: Implement actual saving logic
        raise NotImplementedError("Saving clustering results is not yet implemented")
    
    @property
    def msa_info(self) -> Dict[str, Any]:
        """
        Get information about the associated MSA.
        
        Returns
        -------
        Dict[str, Any]
            MSA information including sequence count, chain type, etc.
        """
        sequences, descriptions = self._extract_sequences_and_descriptions()
        
        base_info = {
            'n_sequences': len(sequences),
            'sequence_lengths': [len(seq) for seq in sequences],
            'avg_sequence_length': sum(len(seq) for seq in sequences) / len(sequences) if sequences else 0,
            'max_sequence_length': max(len(seq) for seq in sequences) if sequences else 0,
            'min_sequence_length': min(len(seq) for seq in sequences) if sequences else 0,
            'msa_type': self._msa_type
        }
        
        # Add type-specific information
        if self._msa_type == 'core':
            base_info.update({
                'chain_poly_type': str(self.msa.chain_poly_type),
                'query_sequence': str(self.msa.query_sequence.sequence) if self.msa.query_sequence else None,
            })
        elif self._msa_type == 'legacy':
            base_info.update({
                'chain_poly_type': getattr(self.msa, 'chain_poly_type', 'unknown'),
                'query_sequence': self.msa.query_sequence if hasattr(self.msa, 'query_sequence') else None,
            })
        
        return base_info 
    
    def _filter_sequences_by_gap_content(self, 
                                        sequences: List[str], 
                                        descriptions: List[str], 
                                        gap_cutoff: float) -> Tuple[List[str], List[str], List[int]]:
        """
        Filter sequences based on gap content.
        
        Following the exact logic from dbscan_cluster.py _filter_sequences method.
        
        Parameters
        ----------
        sequences : List[str]
            Input sequences
        descriptions : List[str]
            Input descriptions
        gap_cutoff : float
            Maximum fraction of gaps allowed
            
        Returns
        -------
        Tuple[List[str], List[str], List[int]]
            Filtered sequences, descriptions, and original indices
        """
        filtered_indices = []
        filtered_sequences = []
        filtered_descriptions = []
        
        for i, (seq, desc) in enumerate(zip(sequences, descriptions)):
            seq_len = len(seq)
            if seq_len == 0:
                continue  # Skip empty sequences
            
            # Calculate fraction of gaps
            gap_count = seq.count('-')
            gap_fraction = gap_count / seq_len
            
            # Include sequence if gap fraction is below cutoff
            if gap_fraction <= gap_cutoff:  # Use <= to match requirement
                filtered_indices.append(i)
                filtered_sequences.append(seq)
                filtered_descriptions.append(desc)
        
        return filtered_sequences, filtered_descriptions, filtered_indices
    
    def _create_cluster_metadata(self, df, query_df) -> Dict[str, Any]:
        """
        Create cluster metadata following dbscan_cluster.py format.
        
        Parameters
        ----------
        df : pd.DataFrame
            Clustering results dataframe
        query_df : pd.DataFrame
            Query sequence dataframe
            
        Returns
        -------
        Dict[str, Any]
            Cluster metadata
        """
        import pandas as pd
        import numpy as np
        
        cluster_metadata = []
        
        # Get sorted clusters by size (excluding noise)
        cluster_sizes = df['cluster_label'].value_counts().to_dict()
        sorted_clusters = sorted(
            [label for label in cluster_sizes if label != -1],
            key=lambda x: cluster_sizes[x],
            reverse=True
        )
        
        for idx, clust in enumerate(sorted_clusters):
            # Get sequences in this cluster
            tmp = df.loc[df['cluster_label'] == clust]
            
            # Generate consensus sequence (using uppercase sequences for consistency)
            consensus_seq = self._generate_consensus_sequence(tmp['sequence'].tolist())
            
            # Calculate similarities
            avg_similarity_within_cluster = np.mean([
                self._calculate_sequence_similarity(x, consensus_seq) 
                for x in tmp['sequence'].tolist()
            ])
            
            avg_similarity_to_query = np.mean([
                self._calculate_sequence_similarity(x, query_df['sequence'].iloc[0]) 
                for x in tmp['sequence'].tolist()
            ])
            
            # Add to metadata
            cluster_metadata.append({
                'cluster_id': clust,
                'cluster_rank': idx,
                'consensus_sequence': consensus_seq,
                'avg_similarity_within_cluster': avg_similarity_within_cluster,
                'avg_similarity_to_query': avg_similarity_to_query,
                'size': len(tmp)
            })
        
        return {'clusters': cluster_metadata, 'total_clusters': len(sorted_clusters)}
    
    def _generate_consensus_sequence(self, sequences: List[str]) -> str:
        """
        Generate consensus sequence from a list of sequences.
        
        Following the logic from msa_pipeline.py.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences
            
        Returns
        -------
        str
            Consensus sequence
        """
        if not sequences:
            return ""
        
        # Determine the length of sequences
        seq_len = len(sequences[0])
        
        # Check if all sequences have the same length
        if not all(len(seq) == seq_len for seq in sequences):
            logging.warning("Sequences have different lengths for consensus generation")
        
        # Define the alphabet (standard amino acids + gap)
        alphabet = "ACDEFGHIKLMNPQRSTVWY-"
        
        # Generate consensus sequence
        consensus = ""
        for i in range(seq_len):
            # Get all characters at this position
            column = [seq[i] if i < len(seq) else '-' for seq in sequences]
            
            # Count occurrences of each character
            counts = {char: column.count(char) for char in alphabet}
            
            # Find the most common character
            most_common = max(counts.items(), key=lambda x: x[1])[0]
            consensus += most_common
        
        return consensus
    
    def _calculate_sequence_similarity(self, seq1: str, seq2: str) -> float:
        """
        Calculate similarity between two sequences.
        
        Following the logic from msa_pipeline.py.
        
        Parameters
        ----------
        seq1 : str
            First sequence
        seq2 : str
            Second sequence
            
        Returns
        -------
        float
            Similarity score between 0 and 1
        """
        # Ensure sequences are of the same length
        max_len = max(len(seq1), len(seq2))
        if max_len == 0:
            return 1.0  # Empty sequences are considered identical
        
        # Count matching positions
        matches = sum(1 for a, b in zip(seq1.ljust(max_len, '-'), seq2.ljust(max_len, '-')) if a == b)
        
        # Return similarity score
        return matches / max_len 