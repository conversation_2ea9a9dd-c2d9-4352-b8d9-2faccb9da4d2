"""
Factory for creating clustering algorithm instances.

This module implements the factory pattern for creating clustering algorithms,
allowing dynamic registration and instantiation of different clustering methods.
"""

import importlib
import logging
from typing import Dict, List, Type
from .interfaces import ClusteringAlgorithm


class ClusteringFactory:
    """
    Factory for creating clustering algorithm instances.
    
    This class implements the factory pattern to create instances of clustering
    algorithms based on their names. It supports dynamic registration of new
    algorithms and provides introspection capabilities.
    """
    
    # Registry of available clustering algorithms
    _algorithms: Dict[str, str] = {
        'dbscan': 'msa.clustering.algorithms.dbscan.DBSCANClusterer',
        'hierarchical': 'msa.clustering.algorithms.hierarchical.HierarchicalClusterer',
        'hdbscan': 'msa.clustering.algorithms.hdbscan.HDBSCANClusterer',
    }
    
    def __init__(self):
        """Initialize the factory."""
        self._instances_cache: Dict[str, ClusteringAlgorithm] = {}
    
    def create_clusterer(self, algorithm: str, use_cache: bool = True) -> ClusteringAlgorithm:
        """
        Create a clustering algorithm instance.
        
        Parameters
        ----------
        algorithm : str
            Name of the clustering algorithm to create.
        use_cache : bool, optional
            Whether to use cached instances for better performance.
            Defaults to True.
            
        Returns
        -------
        ClusteringAlgorithm
            Instance of the requested clustering algorithm.
            
        Raises
        ------
        ValueError
            If the algorithm is not registered.
        ImportError
            If the algorithm module cannot be imported.
        AttributeError
            If the algorithm class cannot be found in the module.
        """
        if algorithm not in self._algorithms:
            available = ', '.join(self.list_algorithms())
            raise ValueError(
                f"Unknown clustering algorithm: '{algorithm}'. "
                f"Available algorithms: {available}"
            )
        
        # Return cached instance if available and caching is enabled
        if use_cache and algorithm in self._instances_cache:
            return self._instances_cache[algorithm]
        
        try:
            module_path = self._algorithms[algorithm]
            module_name, class_name = module_path.rsplit('.', 1)
            
            # Import the module
            module = importlib.import_module(module_name)
            
            # Get the class from the module
            clusterer_class = getattr(module, class_name)
            
            # Create instance
            instance = clusterer_class()
            
            # Verify it implements the required interface
            if not isinstance(instance, ClusteringAlgorithm):
                raise TypeError(
                    f"Class {class_name} does not implement ClusteringAlgorithm interface"
                )
            
            # Cache the instance if caching is enabled
            if use_cache:
                self._instances_cache[algorithm] = instance
            
            logging.debug(f"Created clustering algorithm instance: {algorithm}")
            return instance
            
        except ImportError as e:
            raise ImportError(
                f"Could not import clustering algorithm '{algorithm}': {e}"
            ) from e
        except AttributeError as e:
            raise AttributeError(
                f"Could not find class {class_name} in module {module_name}: {e}"
            ) from e
    
    def list_algorithms(self) -> List[str]:
        """
        Get list of available clustering algorithms.
        
        Returns
        -------
        List[str]
            List of registered algorithm names.
        """
        return list(self._algorithms.keys())
    
    def is_algorithm_available(self, algorithm: str) -> bool:
        """
        Check if a clustering algorithm is available.
        
        Parameters
        ----------
        algorithm : str
            Name of the algorithm to check.
            
        Returns
        -------
        bool
            True if the algorithm is available, False otherwise.
        """
        return algorithm in self._algorithms
    
    def get_algorithm_info(self, algorithm: str) -> Dict[str, str]:
        """
        Get information about a clustering algorithm.
        
        Parameters
        ----------
        algorithm : str
            Name of the algorithm.
            
        Returns
        -------
        Dict[str, str]
            Dictionary containing algorithm information.
            
        Raises
        ------
        ValueError
            If the algorithm is not registered.
        """
        if not self.is_algorithm_available(algorithm):
            raise ValueError(f"Unknown algorithm: {algorithm}")
        
        try:
            clusterer = self.create_clusterer(algorithm)
            return {
                'name': algorithm,
                'class_path': self._algorithms[algorithm],
                'algorithm_name': clusterer.algorithm_name,
                'default_params': str(clusterer.get_default_params()),
                'param_descriptions': str(clusterer.get_param_descriptions())
            }
        except Exception as e:
            return {
                'name': algorithm,
                'class_path': self._algorithms[algorithm],
                'error': str(e)
            }
    
    @classmethod
    def register_algorithm(cls, name: str, module_path: str) -> None:
        """
        Register a new clustering algorithm.
        
        Parameters
        ----------
        name : str
            Name to register the algorithm under.
        module_path : str
            Full module path to the algorithm class (e.g., 'package.module.ClassName').
            
        Raises
        ------
        ValueError
            If the algorithm name is already registered.
        """
        if name in cls._algorithms:
            raise ValueError(f"Algorithm '{name}' is already registered")
        
        cls._algorithms[name] = module_path
        logging.info(f"Registered new clustering algorithm: {name}")
    
    @classmethod
    def unregister_algorithm(cls, name: str) -> None:
        """
        Unregister a clustering algorithm.
        
        Parameters
        ----------
        name : str
            Name of the algorithm to unregister.
            
        Raises
        ------
        ValueError
            If the algorithm is not registered.
        """
        if name not in cls._algorithms:
            raise ValueError(f"Algorithm '{name}' is not registered")
        
        del cls._algorithms[name]
        logging.info(f"Unregistered clustering algorithm: {name}")
    
    def clear_cache(self) -> None:
        """Clear the instances cache."""
        self._instances_cache.clear()
        logging.debug("Cleared clustering algorithm instances cache")


# Global factory instance for convenience
default_factory = ClusteringFactory() 