"""
DBSCAN clustering algorithm implementation.

This module provides a wrapper around the existing DBSCANClusterMSA functionality
to conform to the new clustering interface.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from ..interfaces import ClusteringAlgorithm, ClusteringResult
import time


class DBSCAN<PERSON>lusterer(ClusteringAlgorithm):
    """
    DBSCAN clustering implementation for MSA sequences.
    
    This class wraps the existing DBSCANClusterMSA functionality to provide
    a consistent interface with other clustering algorithms.
    """
    
    @property
    def algorithm_name(self) -> str:
        """Return algorithm name."""
        return "DBSCAN"
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for DBSCAN clustering."""
        return {
            'eps': 5.0,
            'min_samples': 3,
            'gap_cutoff': 0.25,
            'encoding_method': 'onehot',
            'min_eps': 3.0,
            'max_eps': 20.0,
            'eps_step': 0.5,
            'optimize_eps': True,
            'metric': 'euclidean'
        }
    
    def get_param_descriptions(self) -> Dict[str, str]:
        """Get descriptions of DBSCAN parameters."""
        return {
            'eps': 'Maximum distance between samples for them to be considered neighbors',
            'min_samples': 'Minimum number of samples in a neighborhood for a core point',
            'gap_cutoff': 'Maximum fraction of gaps allowed in sequences before filtering',
            'encoding_method': 'Method for encoding sequences (onehot or MSAtransformer)',
            'min_eps': 'Minimum eps value for parameter optimization',
            'max_eps': 'Maximum eps value for parameter optimization',
            'eps_step': 'Step size for eps parameter optimization',
            'optimize_eps': 'Whether to optimize eps parameter automatically',
            'metric': 'Distance metric to use for clustering'
        }
    
    def cluster(self, 
        sequences: List[str], 
        descriptions: List[str],
        **kwargs
    ) -> ClusteringResult:
        """
        Perform DBSCAN clustering on sequences.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to cluster.
        descriptions : List[str]
            Corresponding descriptions for sequences.
        **kwargs
            DBSCAN-specific parameters.
            
        Returns
        -------
        ClusteringResult
            Clustering results in standard format.
        """
        # Validate and merge parameters
        params = self.validate_params(**kwargs)
        
        try:
            # Import here to avoid circular imports
            try:
                from ...dbscan_cluster import DBSCANClusterMSA
                from ...msa_pipeline import MSA
            except ImportError:
                # Fallback for different import contexts
                from msa.dbscan_cluster import DBSCANClusterMSA
                from msa.msa_pipeline import MSA
            
            # Create temporary MSA object for clustering
            # Use 'protein' as default chain type - this could be made configurable
            temp_msa = MSA(
                query_sequence=sequences[0],
                chain_poly_type='protein',
                sequences=sequences,
                descriptions=descriptions,
                deduplicated=False  # Don't deduplicate as input is already processed
            )
            
            # Convert to DBSCANClusterMSA
            dbscan_msa = temp_msa.to_dbscan_cluster_msa()
            
            # Remove n_clusters from params as DBSCAN doesn't accept it as an input parameter
            clustering_params = params.copy()
            clustering_params.pop('n_clusters', None)  # Remove if present
            
            # Perform clustering using the existing implementation
            logging.info(f"Starting DBSCAN clustering with params: {clustering_params}")
            clustering_results = dbscan_msa.cluster(
                gap_cutoff=clustering_params['gap_cutoff'],
                min_eps=clustering_params['min_eps'] if clustering_params['optimize_eps'] else clustering_params['eps'],
                max_eps=clustering_params['max_eps'] if clustering_params['optimize_eps'] else clustering_params['eps'],
                eps_step=clustering_params['eps_step'],
                min_samples=clustering_params['min_samples'],
                save_a3m_files=False,  # Don't save files in this context
                keyword="cluster"
            )
            
            # Extract clustering_assignments DataFrame from results
            if hasattr(clustering_results, 'clustering_assignments') and clustering_results.clustering_assignments is not None:
                assignments_df = clustering_results.clustering_assignments
                logging.info(f"Extracted clustering assignments DataFrame with {len(assignments_df)} entries")
                
                # Initialize cluster labels array for all sequences (including query)
                cluster_labels = [-1] * len(sequences)
                
                # Map filtered sequence results back to original sequence indices
                # assignments_df contains filtered sequences with 'original_index' and 'cluster_label' columns
                for _, row in assignments_df.iterrows():
                    original_idx = row.get('original_index', None)
                    cluster_label = row.get('cluster_label', -1)
                    
                    # Convert cluster label to integer if it's a string
                    if isinstance(cluster_label, str) and cluster_label.lstrip('-').isdigit():
                        cluster_label = int(cluster_label)
                    elif cluster_label is None:
                        cluster_label = -1  # Noise
                    else:
                        try:
                            cluster_label = int(cluster_label) if isinstance(cluster_label, (int, float)) else -1
                        except (ValueError, TypeError):
                            cluster_label = -1
                    
                    # Map back to original sequence index
                    # original_index should be 1-based (1, 2, 3, ...) since query is at index 0
                    if original_idx is not None and 0 <= original_idx < len(cluster_labels):
                        cluster_labels[original_idx] = cluster_label
                
                # The query sequence (index 0) should remain as noise (-1) for DBSCAN
                cluster_labels[0] = -1
                
                # Count actual clusters and noise
                n_clusters = len(set(label for label in cluster_labels if label >= 0))
                n_noise = sum(1 for label in cluster_labels if label < 0)
                
                logging.info(f"Successfully mapped cluster labels: {n_clusters} clusters, {n_noise} noise points")
                
            else:
                logging.warning("Could not extract cluster assignments from clustering results")
                # Try alternative extraction method using clusters directly
                cluster_labels = [-1] * len(sequences)
                
                if hasattr(clustering_results, 'clusters') and clustering_results.clusters:
                    logging.info(f"Attempting alternative extraction from {len(clustering_results.clusters)} cluster MSAs")
                    
                    for cluster_id, cluster_msa in enumerate(clustering_results.clusters):
                        if hasattr(cluster_msa, 'descriptions') and len(cluster_msa.descriptions) > 1:
                            # Skip the first description (query) in each cluster
                            cluster_descriptions = cluster_msa.descriptions[1:]  # Skip query
                            
                            for desc in cluster_descriptions:
                                # Find matching description in original sequences
                                for seq_idx, seq_desc in enumerate(descriptions):
                                    if desc == seq_desc and seq_idx > 0:  # Skip query (index 0)
                                        cluster_labels[seq_idx] = cluster_id
                                        break
                    
                    n_clusters = len(set(label for label in cluster_labels if label >= 0))
                    n_noise = sum(1 for label in cluster_labels if label < 0)
                    logging.info(f"Alternative extraction completed: {n_clusters} clusters, {n_noise} noise points")
                else:
                    # If all else fails, treat everything as noise
                    n_clusters = 0
                    n_noise = len(sequences)
                    logging.warning("No clustering results found, treating all sequences as noise")
            
            # Calculate cluster centers if possible (for one-hot encoding)
            cluster_centers = None
            if params['encoding_method'] == 'onehot' and n_clusters > 0:
                try:
                    # Encode sequences to get feature vectors
                    max_len = max(len(seq) for seq in sequences)
                    encoded_seqs = temp_msa.encode_sequences(
                        sequences=sequences,
                        max_seq_len=max_len,
                        encoding_method='onehot'
                    )
                    
                    # Calculate cluster centers
                    unique_labels = set(label for label in cluster_labels if label >= 0)
                    if unique_labels:
                        centers = []
                        for label in sorted(unique_labels):
                            mask = np.array(cluster_labels) == label
                            if np.any(mask):
                                center = np.mean(encoded_seqs[mask], axis=0)
                                centers.append(center)
                        if centers:
                            cluster_centers = np.array(centers)
                            
                except Exception as e:
                    logging.warning(f"Could not calculate cluster centers: {e}")
            
            # Extract metadata
            metadata = {
                'n_clusters': n_clusters,
                'n_noise': n_noise,
                'silhouette_score': None,  # Could be calculated if needed
            }
            
            # Add algorithm-specific metadata if available
            if hasattr(clustering_results, 'clustering_params'):
                metadata.update(clustering_results.clustering_params)
            if hasattr(clustering_results, 'cluster_metadata') and clustering_results.cluster_metadata is not None:
                metadata['cluster_sizes'] = clustering_results.cluster_metadata.to_dict('records')
            
            logging.info(f"DBSCAN clustering completed successfully: {n_clusters} clusters, {n_noise} noise points")
            
            return ClusteringResult(
                cluster_labels=cluster_labels,
                cluster_centers=cluster_centers,
                cluster_probabilities=None,  # DBSCAN doesn't provide probabilities
                metadata=metadata,
                algorithm_params=params,
                algorithm_name=self.algorithm_name,
                sequences=sequences,
                descriptions=descriptions
            )
            
        except Exception as e:
            logging.error(f"DBSCAN clustering failed: {str(e)}")
            raise RuntimeError(f"DBSCAN clustering failed: {str(e)}") from e 