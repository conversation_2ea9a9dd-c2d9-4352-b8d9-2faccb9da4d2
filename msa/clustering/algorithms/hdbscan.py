"""
HDBSCAN clustering algorithm implementation.

This module provides HDBSCAN (Hierarchical Density-Based Spatial Clustering of 
Applications with Noise) clustering for MSA sequences using scikit-learn.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from ..interfaces import ClusteringAlgorithm, ClusteringResult


class HDBSCANClusterer(ClusteringAlgorithm):
    """
    HDBSCAN clustering implementation for MSA sequences.
    
    HDBSCAN extends DBSCAN by converting it into a hierarchical clustering algorithm,
    and then extracting flat clusters based on the stability of clusters.
    This provides more robust clustering than DBSCAN, especially for varying densities.
    
    Based on scikit-learn's HDBSCAN implementation:
    https://scikit-learn.org/stable/modules/generated/sklearn.cluster.HDBSCAN.html
    """
    
    @property
    def algorithm_name(self) -> str:
        """Return algorithm name."""
        return "HDBSCAN"
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for HDBSCAN clustering."""
        return {
            'min_cluster_size': 10,
            'min_samples': None,
            'cluster_selection_epsilon': 0.0,
            'max_cluster_size': None,
            'metric': 'euclidean',
            'alpha': 1.0,
            'algorithm': 'auto',
            'leaf_size': 40,
            'n_jobs': None,
            'cluster_selection_method': 'eom',
            'allow_single_cluster': False,
            'store_centers': None,
            'copy': False,
            'gap_cutoff': 0.25,
            'encoding_method': 'onehot'
        }
    
    def get_param_descriptions(self) -> Dict[str, str]:
        """Get descriptions of HDBSCAN parameters."""
        return {
            'min_cluster_size': 'Minimum size of clusters; single linkage tree is cut at this size',
            'min_samples': 'Number of samples in a neighborhood for a point to be core (None = min_cluster_size)',
            'cluster_selection_epsilon': 'Distance threshold for cluster extraction from hierarchy',
            'max_cluster_size': 'Maximum size that can be considered a cluster',
            'metric': 'Distance metric to use for clustering',
            'alpha': 'Robustness parameter for mutual reachability distance computation',
            'algorithm': 'Algorithm used to compute core distances (auto, ball_tree, kd_tree, brute)',
            'leaf_size': 'Leaf size for tree algorithms (ball_tree/kd_tree)',
            'n_jobs': 'Number of parallel jobs (-1 for all processors)',
            'cluster_selection_method': 'Method for selecting clusters (eom or leaf)',
            'allow_single_cluster': 'Whether to allow single cluster solutions',
            'store_centers': 'Compute and store cluster centroids (None for auto)',
            'copy': 'Whether to copy input data',
            'gap_cutoff': 'Maximum fraction of gaps allowed in sequences before filtering',
            'encoding_method': 'Method for encoding sequences (onehot or MSAtransformer)'
        }
    
    def cluster(self, 
        sequences: List[str], 
        descriptions: List[str],
        **kwargs
    ) -> ClusteringResult:
        """
        Perform HDBSCAN clustering on sequences.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to cluster.
        descriptions : List[str]
            Corresponding descriptions for sequences.
        **kwargs
            HDBSCAN-specific parameters.
            
        Returns
        -------
        ClusteringResult
            Clustering results in standard format.
        """
        # Validate and merge parameters
        params = self.validate_params(**kwargs)
        
        try:
            # Import scikit-learn HDBSCAN
            from sklearn.cluster import HDBSCAN
            from sklearn.preprocessing import StandardScaler
            
            # Import local components
            from msa_pipeline import MSA
            
            # Create temporary MSA object for encoding
            temp_msa = MSA(
                query_sequence=sequences[0],
                chain_poly_type='protein',  # Default to protein, could be configurable
                sequences=sequences,
                descriptions=descriptions,
                deduplicated=False
            )
            
            # Filter sequences based on gap cutoff
            filtered_sequences, filtered_descriptions, valid_indices = temp_msa._filter_sequences(
                gap_cutoff=params['gap_cutoff']
            )
            
            if len(filtered_sequences) < params['min_cluster_size']:
                raise ValueError(
                    f"Not enough sequences after filtering ({len(filtered_sequences)}) "
                    f"for min_cluster_size ({params['min_cluster_size']})"
                )
            
            # Encode sequences
            max_len = max(len(seq) for seq in filtered_sequences)
            encoded_seqs = temp_msa.encode_sequences(
                sequences=filtered_sequences,
                max_seq_len=max_len,
                encoding_method=params['encoding_method']
            )
            
            # Standardize features for better clustering
            if params['encoding_method'] == 'onehot':
                scaler = StandardScaler()
                encoded_seqs = scaler.fit_transform(encoded_seqs)
            
            # Prepare HDBSCAN parameters
            hdbscan_params = {
                'min_cluster_size': params['min_cluster_size'],
                'min_samples': params['min_samples'],
                'cluster_selection_epsilon': params['cluster_selection_epsilon'],
                'max_cluster_size': params['max_cluster_size'],
                'metric': params['metric'],
                'alpha': params['alpha'],
                'algorithm': params['algorithm'],
                'leaf_size': params['leaf_size'],
                'n_jobs': params['n_jobs'],
                'cluster_selection_method': params['cluster_selection_method'],
                'allow_single_cluster': params['allow_single_cluster'],
                'store_centers': params['store_centers'],
                'copy': params['copy']
            }
            
            # Remove None values
            hdbscan_params = {k: v for k, v in hdbscan_params.items() if v is not None}
            
            # Perform clustering
            clusterer = HDBSCAN(**hdbscan_params)
            cluster_labels_filtered = clusterer.fit_predict(encoded_seqs)
            
            # Map back to original sequence indices
            cluster_labels = [-1] * len(sequences)  # Initialize as noise
            for i, original_idx in enumerate(valid_indices):
                cluster_labels[original_idx] = cluster_labels_filtered[i]
            
            # Get cluster probabilities for filtered sequences
            cluster_probabilities = [0.0] * len(sequences)  # Initialize as low probability
            if hasattr(clusterer, 'probabilities_'):
                for i, original_idx in enumerate(valid_indices):
                    cluster_probabilities[original_idx] = clusterer.probabilities_[i]
            
            # Get cluster centers
            cluster_centers = None
            if hasattr(clusterer, 'centroids_') and clusterer.centroids_ is not None:
                cluster_centers = clusterer.centroids_
            elif hasattr(clusterer, 'medoids_') and clusterer.medoids_ is not None:
                cluster_centers = clusterer.medoids_
            else:
                # Calculate centers manually for one-hot encoding
                unique_labels = set(label for label in cluster_labels if label >= 0)
                if unique_labels and params['encoding_method'] == 'onehot':
                    try:
                        # Re-encode all sequences for center calculation
                        all_encoded = temp_msa.encode_sequences(
                            sequences=sequences,
                            max_seq_len=max_len,
                            encoding_method='onehot'
                        )
                        
                        centers = []
                        for label in sorted(unique_labels):
                            mask = np.array(cluster_labels) == label
                            if np.any(mask):
                                center = np.mean(all_encoded[mask], axis=0)
                                centers.append(center)
                        if centers:
                            cluster_centers = np.array(centers)
                            
                    except Exception as e:
                        logging.warning(f"Could not calculate cluster centers: {e}")
            
            # Extract metadata
            metadata = {
                'n_clusters': len(set(label for label in cluster_labels if label >= 0)),
                'n_noise': sum(1 for label in cluster_labels if label < 0),
                'n_filtered': len(sequences) - len(filtered_sequences),
                'silhouette_score': None,  # Could be calculated if needed
            }
            
            # Add HDBSCAN-specific metadata
            if hasattr(clusterer, 'cluster_persistence_'):
                metadata['cluster_persistence'] = clusterer.cluster_persistence_.tolist()
            if hasattr(clusterer, 'condensed_tree_'):
                metadata['condensed_tree_info'] = {
                    'n_clusters': len(np.unique(clusterer.labels_[clusterer.labels_ >= 0]))
                }
            if hasattr(clusterer, 'single_linkage_tree_'):
                metadata['single_linkage_tree_info'] = {
                    'tree_size': len(clusterer.single_linkage_tree_)
                }
            
            # Add performance metrics
            if hasattr(clusterer, 'probabilities_'):
                valid_probs = clusterer.probabilities_[clusterer.probabilities_ > 0]
                if len(valid_probs) > 0:
                    metadata['avg_cluster_probability'] = float(np.mean(valid_probs))
                    metadata['min_cluster_probability'] = float(np.min(valid_probs))
                    metadata['max_cluster_probability'] = float(np.max(valid_probs))
            
            return ClusteringResult(
                cluster_labels=cluster_labels,
                cluster_centers=cluster_centers,
                cluster_probabilities=cluster_probabilities,
                metadata=metadata,
                algorithm_params=params,
                algorithm_name=self.algorithm_name,
                sequences=sequences,
                descriptions=descriptions
            )
            
        except ImportError as e:
            raise ImportError(
                "HDBSCAN requires scikit-learn >= 1.3.0. "
                f"Please upgrade scikit-learn: {e}"
            ) from e
        except Exception as e:
            logging.error(f"HDBSCAN clustering failed: {str(e)}")
            raise RuntimeError(f"HDBSCAN clustering failed: {str(e)}") from e 