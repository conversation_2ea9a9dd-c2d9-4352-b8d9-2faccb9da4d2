"""
Hierarchical clustering algorithm implementation.

This module provides a wrapper around the existing hierarchical clustering functionality
to conform to the new clustering interface.
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from ..interfaces import ClusteringAlgorithm, ClusteringResult


class HierarchicalClusterer(ClusteringAlgorithm):
    """
    Hierarchical clustering implementation for MSA sequences.
    
    This class wraps the existing hierarchical clustering functionality to provide
    a consistent interface with other clustering algorithms.
    """
    
    @property
    def algorithm_name(self) -> str:
        """Return algorithm name."""
        return "Hierarchical"
    
    def get_default_params(self) -> Dict[str, Any]:
        """Get default parameters for hierarchical clustering."""
        return {
            'n_clusters': 8,
            'linkage': 'ward',
            'distance_threshold': None,
            'gap_cutoff': 0.25,
            'encoding_method': 'onehot',
            'metric': 'euclidean',
            'compute_full_tree': 'auto'
        }
    
    def get_param_descriptions(self) -> Dict[str, str]:
        """Get descriptions of hierarchical clustering parameters."""
        return {
            'n_clusters': 'Number of clusters to form (ignored if distance_threshold is not None)',
            'linkage': 'Linkage criterion to use (ward, complete, average, single)',
            'distance_threshold': 'Linkage distance threshold above which clusters will not be merged',
            'gap_cutoff': 'Maximum fraction of gaps allowed in sequences before filtering',
            'encoding_method': 'Method for encoding sequences (onehot or MSAtransformer)',
            'metric': 'Distance metric to use (only used with linkage=ward requires euclidean)',
            'compute_full_tree': 'Whether to compute the full tree or stop early for n_clusters'
        }
    
    def cluster(self, 
        sequences: List[str], 
        descriptions: List[str],
        **kwargs
    ) -> ClusteringResult:
        """
        Perform hierarchical clustering on sequences.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to cluster.
        descriptions : List[str]
            Corresponding descriptions for sequences.
        **kwargs
            Hierarchical clustering-specific parameters.
            
        Returns
        -------
        ClusteringResult
            Clustering results in standard format.
        """
        # Validate and merge parameters
        params = self.validate_params(**kwargs)
        
        try:
            # Import scikit-learn components
            from sklearn.cluster import AgglomerativeClustering
            from sklearn.preprocessing import StandardScaler
            
            # Import local components - use new MSA structure
            from ...encoding.sequence_encoder import SequenceEncoder
            
            # Simple gap filtering function
            def filter_sequences_by_gap(sequences, descriptions, gap_cutoff):
                """Filter sequences by gap content."""
                valid_indices = []
                filtered_seqs = []
                filtered_descs = []
                
                for i, seq in enumerate(sequences):
                    gap_content = seq.count('-') / len(seq) if len(seq) > 0 else 1.0
                    if gap_content <= gap_cutoff:
                        valid_indices.append(i)
                        filtered_seqs.append(seq)
                        filtered_descs.append(descriptions[i])
                
                return {
                    'sequences': filtered_seqs,
                    'descriptions': filtered_descs,
                    'valid_indices': valid_indices
                }
            
            # Filter sequences based on gap cutoff
            filtered_data = filter_sequences_by_gap(
                sequences=sequences,
                descriptions=descriptions,
                gap_cutoff=params['gap_cutoff']
            )
            filtered_sequences = filtered_data['sequences']
            filtered_descriptions = filtered_data['descriptions']
            valid_indices = filtered_data['valid_indices']
            
            if len(filtered_sequences) < 2:
                raise ValueError("Not enough sequences after filtering for clustering")
            
            # Encode sequences using new encoder
            encoder = SequenceEncoder()
            max_len = max(len(seq) for seq in filtered_sequences)
            encoded_seqs = encoder.encode(
                sequences=filtered_sequences,
                method=params['encoding_method'],
                max_seq_len=max_len
            )
            
            # Standardize features for better clustering
            if params['encoding_method'] == 'onehot':
                scaler = StandardScaler()
                encoded_seqs = scaler.fit_transform(encoded_seqs)
            
            # Determine clustering parameters
            clustering_kwargs = {
                'linkage': params['linkage'],
                'compute_full_tree': params['compute_full_tree']
            }
            
            # Set either n_clusters or distance_threshold
            if params['distance_threshold'] is not None:
                clustering_kwargs['distance_threshold'] = params['distance_threshold']
                clustering_kwargs['n_clusters'] = None
            else:
                clustering_kwargs['n_clusters'] = min(params['n_clusters'], len(filtered_sequences))
            
            # Add metric if not using ward linkage
            if params['linkage'] != 'ward':
                clustering_kwargs['metric'] = params['metric']
            
            # Perform clustering
            clusterer = AgglomerativeClustering(**clustering_kwargs)
            cluster_labels_filtered = clusterer.fit_predict(encoded_seqs)
            
            # Map back to original sequence indices
            cluster_labels = [-1] * len(sequences)  # Initialize as noise
            for i, original_idx in enumerate(valid_indices):
                cluster_labels[original_idx] = cluster_labels_filtered[i]
            
            # Calculate cluster centers
            cluster_centers = None
            unique_labels = set(label for label in cluster_labels if label >= 0)
            if unique_labels and params['encoding_method'] == 'onehot':
                try:
                    # Re-encode all sequences (not just filtered) for center calculation
                    all_encoded = encoder.encode(
                        sequences=sequences,
                        method='onehot',
                        max_seq_len=max_len
                    )
                    
                    centers = []
                    for label in sorted(unique_labels):
                        mask = np.array(cluster_labels) == label
                        if np.any(mask):
                            center = np.mean(all_encoded[mask], axis=0)
                            centers.append(center)
                    if centers:
                        cluster_centers = np.array(centers)
                        
                except Exception as e:
                    logging.warning(f"Could not calculate cluster centers: {e}")
            
            # Calculate metadata
            metadata = {
                'n_clusters': len(unique_labels),
                'n_noise': sum(1 for label in cluster_labels if label < 0),
                'n_filtered': len(sequences) - len(filtered_sequences),
                'linkage_matrix': None,  # Could store dendrogram info if needed
                'silhouette_score': None,  # Could be calculated if needed
            }
            
            # Add clustering object info if available
            if hasattr(clusterer, 'children_'):
                dendrogram_info = {
                    'n_leaves': getattr(clusterer, 'n_leaves_', None),
                    'n_connected_components': getattr(clusterer, 'n_connected_components_', None)
                }
                # Only add non-None values
                metadata['dendrogram_info'] = {k: v for k, v in dendrogram_info.items() if v is not None}
            
            return ClusteringResult(
                cluster_labels=cluster_labels,
                cluster_centers=cluster_centers,
                cluster_probabilities=None,  # Hierarchical clustering doesn't provide probabilities
                metadata=metadata,
                algorithm_params=params,
                algorithm_name=self.algorithm_name,
                sequences=sequences,
                descriptions=descriptions
            )
            
        except Exception as e:
            logging.error(f"Hierarchical clustering failed: {str(e)}")
            raise RuntimeError(f"Hierarchical clustering failed: {str(e)}") from e 