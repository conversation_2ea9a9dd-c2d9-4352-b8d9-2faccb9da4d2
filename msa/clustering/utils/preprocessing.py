"""
Preprocessing utilities for clustering operations.

This module provides functions for preparing MSA data before clustering,
including filtering, encoding, and normalization operations.
"""

import numpy as np
import logging
from typing import List, Tuple, Optional, Dict, Any
from sklearn.preprocessing import StandardScaler, MinMaxScaler


def filter_sequences_by_gaps(
    sequences: List[str], 
    descriptions: List[str],
    gap_cutoff: float = 0.25,
    gap_chars: str = '-.'
) -> Tuple[List[str], List[str], List[int]]:
    """
    Filter sequences based on gap content.
    
    Parameters
    ----------
    sequences : List[str]
        Input sequences to filter.
    descriptions : List[str]
        Corresponding descriptions.
    gap_cutoff : float, optional
        Maximum fraction of gaps allowed in sequences.
        Defaults to 0.25.
    gap_chars : str, optional
        Characters considered as gaps.
        Defaults to '-.'.
        
    Returns
    -------
    <PERSON><PERSON>[List[str], List[str], List[int]]
        Filtered sequences, descriptions, and original indices.
    """
    filtered_sequences = []
    filtered_descriptions = []
    valid_indices = []
    
    for i, (seq, desc) in enumerate(zip(sequences, descriptions)):
        gap_count = sum(1 for char in seq if char in gap_chars)
        gap_fraction = gap_count / len(seq) if len(seq) > 0 else 1.0
        
        if gap_fraction <= gap_cutoff:
            filtered_sequences.append(seq)
            filtered_descriptions.append(desc)
            valid_indices.append(i)
    
    logging.info(
        f"Filtered {len(sequences)} sequences to {len(filtered_sequences)} "
        f"({len(sequences) - len(filtered_sequences)} removed due to gaps > {gap_cutoff})"
    )
    
    return filtered_sequences, filtered_descriptions, valid_indices


def filter_sequences_by_length(
    sequences: List[str], 
    descriptions: List[str],
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
    length_tolerance: float = 0.5
) -> Tuple[List[str], List[str], List[int]]:
    """
    Filter sequences based on length criteria.
    
    Parameters
    ----------
    sequences : List[str]
        Input sequences to filter.
    descriptions : List[str]
        Corresponding descriptions.
    min_length : int, optional
        Minimum sequence length allowed.
        If None, uses mean - length_tolerance * std.
    max_length : int, optional
        Maximum sequence length allowed.
        If None, uses mean + length_tolerance * std.
    length_tolerance : float, optional
        Standard deviation multiplier for automatic min/max calculation.
        Defaults to 0.5.
        
    Returns
    -------
    Tuple[List[str], List[str], List[int]]
        Filtered sequences, descriptions, and original indices.
    """
    lengths = [len(seq) for seq in sequences]
    
    if min_length is None or max_length is None:
        mean_length = np.mean(lengths)
        std_length = np.std(lengths)
        
        if min_length is None:
            min_length = max(1, int(mean_length - length_tolerance * std_length))
        if max_length is None:
            max_length = int(mean_length + length_tolerance * std_length)
    
    filtered_sequences = []
    filtered_descriptions = []
    valid_indices = []
    
    for i, (seq, desc) in enumerate(zip(sequences, descriptions)):
        if min_length <= len(seq) <= max_length:
            filtered_sequences.append(seq)
            filtered_descriptions.append(desc)
            valid_indices.append(i)
    
    logging.info(
        f"Filtered {len(sequences)} sequences to {len(filtered_sequences)} "
        f"(length range: {min_length}-{max_length})"
    )
    
    return filtered_sequences, filtered_descriptions, valid_indices


def encode_sequences(
    sequences: List[str],
    encoding_method: str = 'onehot',
    max_seq_len: Optional[int] = None,
    alphabet: str = "ACDEFGHIKLMNPQRSTVWY-X"
) -> np.ndarray:
    """
    Encode sequences for clustering.
    
    Parameters
    ----------
    sequences : List[str]
        Sequences to encode.
    encoding_method : str, optional
        Encoding method ('onehot' or 'MSAtransformer').
        Defaults to 'onehot'.
    max_seq_len : int, optional
        Maximum sequence length for padding/truncation.
        If None, uses the maximum length in sequences.
    alphabet : str, optional
        Alphabet for one-hot encoding.
        Defaults to standard amino acid alphabet with gaps.
        
    Returns
    -------
    np.ndarray
        Encoded sequences as feature matrix.
        
    Raises
    ------
    ValueError
        If encoding_method is not supported.
    """
    if not sequences:
        raise ValueError("No sequences provided for encoding")
    
    if max_seq_len is None:
        max_seq_len = max(len(seq) for seq in sequences)
    
    if encoding_method == 'onehot':
        return _encode_onehot(sequences, max_seq_len, alphabet)
    elif encoding_method == 'MSAtransformer':
        return _encode_msa_transformer(sequences, max_seq_len)
    else:
        raise ValueError(f"Unsupported encoding method: {encoding_method}")


def _encode_onehot(
    sequences: List[str], 
    max_seq_len: int, 
    alphabet: str
) -> np.ndarray:
    """One-hot encode sequences."""
    n_sequences = len(sequences)
    n_features = len(alphabet)
    
    # Create encoding matrix
    encoded = np.zeros((n_sequences, max_seq_len * n_features))
    
    # Create character to index mapping
    char_to_idx = {char: i for i, char in enumerate(alphabet)}
    
    for seq_idx, sequence in enumerate(sequences):
        for pos, char in enumerate(sequence[:max_seq_len]):
            if char in char_to_idx:
                feature_idx = pos * n_features + char_to_idx[char]
                encoded[seq_idx, feature_idx] = 1.0
            # Unknown characters are left as zeros (could be treated as gaps)
    
    return encoded


def _encode_msa_transformer(sequences: List[str], max_seq_len: int) -> np.ndarray:
    """
    Encode sequences using MSA Transformer embeddings.
    
    This is a placeholder implementation - actual MSA Transformer
    encoding would require the model and more complex processing.
    """
    # Placeholder: for now, fall back to a simplified encoding
    # In a real implementation, this would use the actual MSA Transformer
    logging.warning("MSA Transformer encoding not fully implemented, using simplified encoding")
    
    # Simple amino acid index encoding as placeholder
    aa_chars = "ACDEFGHIKLMNPQRSTVWY"
    char_to_idx = {char: i+1 for i, char in enumerate(aa_chars)}
    char_to_idx['-'] = 0  # Gap
    char_to_idx['X'] = len(aa_chars) + 1  # Unknown
    
    encoded = np.zeros((len(sequences), max_seq_len))
    
    for seq_idx, sequence in enumerate(sequences):
        for pos, char in enumerate(sequence[:max_seq_len]):
            encoded[seq_idx, pos] = char_to_idx.get(char.upper(), len(aa_chars) + 1)
    
    return encoded


def normalize_features(
    features: np.ndarray,
    method: str = 'standard'
) -> Tuple[np.ndarray, Any]:
    """
    Normalize feature matrix for clustering.
    
    Parameters
    ----------
    features : np.ndarray
        Feature matrix to normalize.
    method : str, optional
        Normalization method ('standard', 'minmax', 'none').
        Defaults to 'standard'.
        
    Returns
    -------
    Tuple[np.ndarray, Any]
        Normalized features and the scaler object.
    """
    if method == 'standard':
        scaler = StandardScaler()
        normalized = scaler.fit_transform(features)
    elif method == 'minmax':
        scaler = MinMaxScaler()
        normalized = scaler.fit_transform(features)
    elif method == 'none':
        scaler = None
        normalized = features.copy()
    else:
        raise ValueError(f"Unsupported normalization method: {method}")
    
    return normalized, scaler


def calculate_sequence_weights(
    sequences: List[str],
    method: str = 'position_based'
) -> np.ndarray:
    """
    Calculate sequence weights for clustering.
    
    Parameters
    ----------
    sequences : List[str]
        Sequences to calculate weights for.
    method : str, optional
        Weight calculation method ('uniform', 'position_based', 'length_based').
        Defaults to 'position_based'.
        
    Returns
    -------
    np.ndarray
        Sequence weights.
    """
    n_sequences = len(sequences)
    
    if method == 'uniform':
        return np.ones(n_sequences)
    
    elif method == 'position_based':
        # Weight based on sequence diversity at each position
        if not sequences:
            return np.array([])
        
        max_len = max(len(seq) for seq in sequences)
        weights = np.zeros(n_sequences)
        
        for pos in range(max_len):
            chars_at_pos = [seq[pos] if pos < len(seq) else '-' for seq in sequences]
            unique_chars = set(chars_at_pos)
            
            for i, char in enumerate(chars_at_pos):
                # Weight inversely proportional to frequency of character at position
                char_freq = chars_at_pos.count(char) / len(chars_at_pos)
                weights[i] += 1.0 / char_freq if char_freq > 0 else 0
        
        # Normalize weights
        weights = weights / np.sum(weights) * n_sequences
        return weights
    
    elif method == 'length_based':
        # Weight based on sequence length (longer sequences get more weight)
        lengths = np.array([len(seq) for seq in sequences])
        weights = lengths / np.mean(lengths)
        return weights
    
    else:
        raise ValueError(f"Unsupported weight calculation method: {method}")


def remove_redundant_sequences(
    sequences: List[str], 
    descriptions: List[str],
    similarity_threshold: float = 0.95
) -> Tuple[List[str], List[str], List[int]]:
    """
    Remove highly similar sequences to reduce redundancy.
    
    Parameters
    ----------
    sequences : List[str]
        Input sequences.
    descriptions : List[str]
        Corresponding descriptions.
    similarity_threshold : float, optional
        Sequence identity threshold for redundancy removal.
        Defaults to 0.95.
        
    Returns
    -------
    Tuple[List[str], List[str], List[int]]
        Non-redundant sequences, descriptions, and original indices.
    """
    if not sequences:
        return [], [], []
    
    kept_sequences = [sequences[0]]
    kept_descriptions = [descriptions[0]]
    kept_indices = [0]
    
    for i in range(1, len(sequences)):
        seq = sequences[i]
        desc = descriptions[i]
        
        # Check similarity with all kept sequences
        is_redundant = False
        for kept_seq in kept_sequences:
            similarity = _calculate_sequence_identity(seq, kept_seq)
            if similarity >= similarity_threshold:
                is_redundant = True
                break
        
        if not is_redundant:
            kept_sequences.append(seq)
            kept_descriptions.append(desc)
            kept_indices.append(i)
    
    logging.info(
        f"Removed {len(sequences) - len(kept_sequences)} redundant sequences "
        f"(threshold: {similarity_threshold})"
    )
    
    return kept_sequences, kept_descriptions, kept_indices


def _calculate_sequence_identity(seq1: str, seq2: str) -> float:
    """Calculate pairwise sequence identity."""
    if len(seq1) == 0 and len(seq2) == 0:
        return 1.0
    
    min_len = min(len(seq1), len(seq2))
    if min_len == 0:
        return 0.0
    
    matches = sum(1 for i in range(min_len) if seq1[i] == seq2[i])
    return matches / min_len 