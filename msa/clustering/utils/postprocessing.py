"""
Postprocessing utilities for clustering results analysis.

This module provides functions for analyzing and evaluating clustering results,
including quality metrics and result visualization preparation.
"""

import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from ..interfaces import ClusteringResult


def calculate_clustering_metrics(
    features: np.ndarray,
    cluster_labels: List[int],
    metric_names: Optional[List[str]] = None
) -> Dict[str, float]:
    """
    Calculate clustering quality metrics.
    
    Parameters
    ----------
    features : np.ndarray
        Feature matrix used for clustering.
    cluster_labels : List[int]
        Cluster assignments for each sample.
    metric_names : List[str], optional
        Metrics to calculate. If None, calculates all available metrics.
        Available: ['silhouette', 'calinski_harabasz', 'davies_bouldin']
        
    Returns
    -------
    Dict[str, float]
        Dictionary mapping metric names to their values.
    """
    if metric_names is None:
        metric_names = ['silhouette', 'calinski_harabasz', 'davies_bouldin']
    
    metrics = {}
    
    # Filter out noise points (label -1) for metrics calculation
    valid_mask = np.array(cluster_labels) >= 0
    if not np.any(valid_mask):
        logging.warning("No valid clusters found for metrics calculation")
        return {name: float('nan') for name in metric_names}
    
    valid_features = features[valid_mask]
    valid_labels = np.array(cluster_labels)[valid_mask]
    
    # Check if we have at least 2 clusters
    unique_labels = np.unique(valid_labels)
    if len(unique_labels) < 2:
        logging.warning("Less than 2 clusters found for metrics calculation")
        return {name: float('nan') for name in metric_names}
    
    try:
        if 'silhouette' in metric_names:
            metrics['silhouette'] = silhouette_score(valid_features, valid_labels)
        
        if 'calinski_harabasz' in metric_names:
            metrics['calinski_harabasz'] = calinski_harabasz_score(valid_features, valid_labels)
        
        if 'davies_bouldin' in metric_names:
            metrics['davies_bouldin'] = davies_bouldin_score(valid_features, valid_labels)
            
    except Exception as e:
        logging.error(f"Error calculating clustering metrics: {e}")
        for name in metric_names:
            if name not in metrics:
                metrics[name] = float('nan')
    
    return metrics


def analyze_cluster_composition(
    result: ClusteringResult,
    include_noise: bool = False
) -> Dict[str, Any]:
    """
    Analyze the composition of clusters.
    
    Parameters
    ----------
    result : ClusteringResult
        Clustering result to analyze.
    include_noise : bool, optional
        Whether to include noise points in analysis.
        Defaults to False.
        
    Returns
    -------
    Dict[str, Any]
        Dictionary containing cluster composition analysis.
    """
    labels = np.array(result.cluster_labels)
    
    # Get unique cluster labels
    if include_noise:
        unique_labels = np.unique(labels)
    else:
        unique_labels = np.unique(labels[labels >= 0])
    
    composition = {
        'n_clusters': len(unique_labels[unique_labels >= 0]),
        'n_noise': np.sum(labels == -1),
        'total_sequences': len(labels),
        'cluster_sizes': {},
        'cluster_size_stats': {},
        'largest_cluster': None,
        'smallest_cluster': None,
    }
    
    # Analyze each cluster
    cluster_sizes = []
    for label in unique_labels:
        if not include_noise and label < 0:
            continue
            
        cluster_mask = labels == label
        cluster_size = np.sum(cluster_mask)
        cluster_sizes.append(cluster_size)
        
        cluster_name = f"cluster_{label}" if label >= 0 else "noise"
        composition['cluster_sizes'][cluster_name] = {
            'size': int(cluster_size),
            'percentage': float(cluster_size / len(labels) * 100),
            'sequences': [i for i, mask in enumerate(cluster_mask) if mask]
        }
    
    # Calculate cluster size statistics
    if cluster_sizes:
        composition['cluster_size_stats'] = {
            'mean': float(np.mean(cluster_sizes)),
            'std': float(np.std(cluster_sizes)),
            'min': int(np.min(cluster_sizes)),
            'max': int(np.max(cluster_sizes)),
            'median': float(np.median(cluster_sizes))
        }
        
        # Find largest and smallest clusters
        max_idx = np.argmax(cluster_sizes)
        min_idx = np.argmin(cluster_sizes)
        composition['largest_cluster'] = int(unique_labels[max_idx])
        composition['smallest_cluster'] = int(unique_labels[min_idx])
    
    return composition


def extract_cluster_representatives(
    result: ClusteringResult,
    method: str = 'centroid',
    n_representatives: int = 1
) -> Dict[int, List[int]]:
    """
    Extract representative sequences from each cluster.
    
    Parameters
    ----------
    result : ClusteringResult
        Clustering result containing sequences and cluster assignments.
    method : str, optional
        Method for selecting representatives ('centroid', 'random', 'longest').
        Defaults to 'centroid'.
    n_representatives : int, optional
        Number of representatives per cluster.
        Defaults to 1.
        
    Returns
    -------
    Dict[int, List[int]]
        Dictionary mapping cluster IDs to lists of representative sequence indices.
    """
    labels = np.array(result.cluster_labels)
    sequences = result.sequences
    
    representatives = {}
    
    # Get unique cluster labels (excluding noise)
    unique_labels = np.unique(labels[labels >= 0])
    
    for label in unique_labels:
        cluster_mask = labels == label
        cluster_indices = np.where(cluster_mask)[0]
        cluster_sequences = [sequences[i] for i in cluster_indices]
        
        if method == 'random':
            # Random selection
            selected_indices = np.random.choice(
                cluster_indices, 
                size=min(n_representatives, len(cluster_indices)),
                replace=False
            )
            
        elif method == 'longest':
            # Select longest sequences
            lengths = [len(sequences[i]) for i in cluster_indices]
            sorted_indices = np.argsort(lengths)[::-1]  # Descending order
            selected_indices = cluster_indices[sorted_indices[:n_representatives]]
            
        elif method == 'centroid':
            # Select sequences closest to cluster center
            if result.cluster_centers is not None and len(result.cluster_centers) > label:
                # Use provided cluster centers
                center = result.cluster_centers[label]
                
                # Calculate distances to center (requires re-encoding sequences)
                # This is a simplified version - in practice, you'd want to use the same
                # encoding as was used for clustering
                selected_indices = cluster_indices[:n_representatives]  # Placeholder
            else:
                # Fallback to first n sequences
                selected_indices = cluster_indices[:n_representatives]
                
        else:
            raise ValueError(f"Unknown representative selection method: {method}")
        
        representatives[int(label)] = selected_indices.tolist()
    
    return representatives


def compare_clustering_results(
    results: Dict[str, ClusteringResult],
    comparison_metrics: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Compare multiple clustering results.
    
    Parameters
    ----------
    results : Dict[str, ClusteringResult]
        Dictionary mapping algorithm names to clustering results.
    comparison_metrics : List[str], optional
        Metrics to use for comparison.
        Defaults to ['n_clusters', 'n_noise', 'silhouette_score'].
        
    Returns
    -------
    Dict[str, Any]
        Comparison results including metrics table and rankings.
    """
    if comparison_metrics is None:
        comparison_metrics = ['n_clusters', 'n_noise', 'silhouette_score']
    
    comparison = {
        'algorithms': list(results.keys()),
        'metrics_table': {},
        'rankings': {},
        'best_algorithm': {}
    }
    
    # Extract metrics for each algorithm
    for algorithm, result in results.items():
        metrics = {}
        
        # Basic metrics from result
        metrics['n_clusters'] = result.n_clusters
        metrics['n_noise'] = result.n_noise
        metrics['total_sequences'] = len(result.sequences)
        
        # Extract from metadata if available
        if 'silhouette_score' in result.metadata:
            metrics['silhouette_score'] = result.metadata['silhouette_score']
        
        comparison['metrics_table'][algorithm] = metrics
    
    # Calculate rankings for each metric
    for metric in comparison_metrics:
        metric_values = {}
        for algorithm in results.keys():
            if metric in comparison['metrics_table'][algorithm]:
                value = comparison['metrics_table'][algorithm][metric]
                if not np.isnan(value):
                    metric_values[algorithm] = value
        
        if metric_values:
            # Determine ranking order (higher is better for most metrics except noise)
            reverse = metric not in ['n_noise', 'davies_bouldin']
            sorted_algorithms = sorted(
                metric_values.items(), 
                key=lambda x: x[1], 
                reverse=reverse
            )
            
            comparison['rankings'][metric] = [alg for alg, _ in sorted_algorithms]
            comparison['best_algorithm'][metric] = sorted_algorithms[0][0]
    
    return comparison


def generate_cluster_summary_report(
    result: ClusteringResult,
    features: Optional[np.ndarray] = None
) -> Dict[str, Any]:
    """
    Generate a comprehensive summary report for clustering results.
    
    Parameters
    ----------
    result : ClusteringResult
        Clustering result to summarize.
    features : np.ndarray, optional
        Feature matrix used for clustering (for quality metrics).
        
    Returns
    -------
    Dict[str, Any]
        Comprehensive clustering summary report.
    """
    report = {
        'algorithm': result.algorithm_name,
        'parameters': result.algorithm_params,
        'overview': {
            'total_sequences': len(result.sequences),
            'n_clusters': result.n_clusters,
            'n_noise': result.n_noise,
            'noise_percentage': result.n_noise / len(result.sequences) * 100
        },
        'cluster_composition': analyze_cluster_composition(result),
        'metadata': result.metadata
    }
    
    # Add quality metrics if features are provided
    if features is not None:
        try:
            quality_metrics = calculate_clustering_metrics(features, result.cluster_labels)
            report['quality_metrics'] = quality_metrics
        except Exception as e:
            logging.warning(f"Could not calculate quality metrics: {e}")
            report['quality_metrics'] = {}
    
    # Add representative sequences
    try:
        representatives = extract_cluster_representatives(result, method='centroid', n_representatives=3)
        report['representatives'] = representatives
    except Exception as e:
        logging.warning(f"Could not extract representatives: {e}")
        report['representatives'] = {}
    
    # Add sequence length analysis per cluster
    cluster_length_analysis = {}
    labels = np.array(result.cluster_labels)
    unique_labels = np.unique(labels[labels >= 0])
    
    for label in unique_labels:
        cluster_mask = labels == label
        cluster_sequences = [result.sequences[i] for i, mask in enumerate(cluster_mask) if mask]
        lengths = [len(seq) for seq in cluster_sequences]
        
        if lengths:
            cluster_length_analysis[f'cluster_{label}'] = {
                'mean_length': float(np.mean(lengths)),
                'std_length': float(np.std(lengths)),
                'min_length': int(np.min(lengths)),
                'max_length': int(np.max(lengths))
            }
    
    report['sequence_length_analysis'] = cluster_length_analysis
    
    return report


def filter_clusters_by_quality(
    result: ClusteringResult,
    min_cluster_size: int = 5,
    max_noise_percentage: float = 0.5
) -> ClusteringResult:
    """
    Filter clustering results based on quality criteria.
    
    Parameters
    ----------
    result : ClusteringResult
        Original clustering result.
    min_cluster_size : int, optional
        Minimum cluster size to keep.
        Defaults to 5.
    max_noise_percentage : float, optional
        Maximum allowed noise percentage.
        Defaults to 0.5.
        
    Returns
    -------
    ClusteringResult
        Filtered clustering result.
    """
    labels = np.array(result.cluster_labels)
    
    # Check noise percentage
    noise_percentage = np.sum(labels == -1) / len(labels)
    if noise_percentage > max_noise_percentage:
        logging.warning(
            f"High noise percentage ({noise_percentage:.2%}) exceeds threshold "
            f"({max_noise_percentage:.2%})"
        )
    
    # Filter small clusters
    unique_labels = np.unique(labels[labels >= 0])
    labels_to_remove = []
    
    for label in unique_labels:
        cluster_size = np.sum(labels == label)
        if cluster_size < min_cluster_size:
            labels_to_remove.append(label)
    
    # Reassign small clusters to noise
    filtered_labels = labels.copy()
    for label in labels_to_remove:
        filtered_labels[filtered_labels == label] = -1
    
    # Relabel remaining clusters to be consecutive
    remaining_labels = np.unique(filtered_labels[filtered_labels >= 0])
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(remaining_labels)}
    
    final_labels = filtered_labels.copy()
    for old_label, new_label in label_mapping.items():
        final_labels[filtered_labels == old_label] = new_label
    
    # Update metadata
    new_metadata = result.metadata.copy()
    new_metadata['filtering_applied'] = {
        'min_cluster_size': min_cluster_size,
        'max_noise_percentage': max_noise_percentage,
        'clusters_removed': len(labels_to_remove),
        'original_n_clusters': result.n_clusters,
        'filtered_n_clusters': len(remaining_labels)
    }
    
    logging.info(
        f"Filtered clustering: removed {len(labels_to_remove)} small clusters, "
        f"kept {len(remaining_labels)} clusters"
    )
    
    return ClusteringResult(
        cluster_labels=final_labels.tolist(),
        cluster_centers=result.cluster_centers,
        cluster_probabilities=result.cluster_probabilities,
        metadata=new_metadata,
        algorithm_params=result.algorithm_params,
        algorithm_name=result.algorithm_name,
        sequences=result.sequences,
        descriptions=result.descriptions
    ) 