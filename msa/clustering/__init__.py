"""
MSA clustering module providing various clustering algorithms and interfaces.

This module implements a pluggable clustering architecture following the Factory + Strategy pattern,
allowing easy addition of new clustering algorithms while maintaining a consistent interface.
"""

from .interfaces import ClusteringAlgorithm, ClusteringResult
from .manager import ClusteringManager
from .factory import ClusteringFactory

__all__ = [
    'ClusteringAlgorithm',
    'ClusteringResult', 
    'ClusteringManager',
    'ClusteringFactory'
] 