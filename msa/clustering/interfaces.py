"""
Abstract interfaces for MSA clustering algorithms.

This module defines the base interfaces that all clustering algorithms must implement,
ensuring consistency and interoperability across different clustering methods.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import numpy as np


@dataclass
class ClusteringResult:
    """
    Standard clustering result format.
    
    This dataclass provides a consistent structure for returning clustering results
    from any clustering algorithm, making it easy to compare and analyze results.
    """
    cluster_labels: List[int]
    """Cluster assignments for each sequence (-1 for noise/outliers)"""
    
    cluster_centers: Optional[np.ndarray]
    """Cluster centers/centroids if available (algorithm-dependent)"""
    
    cluster_probabilities: Optional[List[float]]
    """Cluster membership probabilities for each sequence (algorithm-dependent)"""
    
    metadata: Dict[str, Any]
    """Additional algorithm-specific metadata (e.g., n_clusters, silhouette_score)"""
    
    algorithm_params: Dict[str, Any]
    """Parameters used for this clustering run"""
    
    algorithm_name: str
    """Name of the clustering algorithm used"""
    
    sequences: List[str]
    """Original sequences that were clustered"""
    
    descriptions: List[str]  
    """Descriptions corresponding to the sequences"""

    @property
    def n_clusters(self) -> int:
        """Number of clusters found (excluding noise)"""
        unique_labels = set(self.cluster_labels)
        return len([label for label in unique_labels if label >= 0])
    
    @property
    def n_noise(self) -> int:
        """Number of noise/outlier points"""
        return sum(1 for label in self.cluster_labels if label < 0)

    def get_cluster_sequences(self, cluster_id: int) -> List[str]:
        """Get sequences belonging to a specific cluster"""
        return [seq for i, seq in enumerate(self.sequences) 
                if self.cluster_labels[i] == cluster_id]
    
    def get_cluster_descriptions(self, cluster_id: int) -> List[str]:
        """Get descriptions belonging to a specific cluster"""
        return [desc for i, desc in enumerate(self.descriptions) 
                if self.cluster_labels[i] == cluster_id]


class ClusteringAlgorithm(ABC):
    """
    Base interface for all clustering algorithms.
    
    This abstract base class defines the interface that all clustering algorithms
    must implement to be compatible with the MSA clustering framework.
    """
    
    @abstractmethod
    def cluster(self, 
        sequences: List[str], 
        descriptions: List[str],
        **kwargs
    ) -> ClusteringResult:
        """
        Perform clustering on sequences.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to cluster.
        descriptions : List[str]
            Corresponding descriptions for sequences.
        **kwargs
            Algorithm-specific parameters.
            
        Returns
        -------
        ClusteringResult
            Clustering results in standard format.
        """
        pass
    
    @abstractmethod
    def get_default_params(self) -> Dict[str, Any]:
        """
        Get default parameters for this algorithm.
        
        Returns
        -------
        Dict[str, Any]
            Default parameter dictionary.
        """
        pass
    
    @property
    @abstractmethod
    def algorithm_name(self) -> str:
        """
        Return algorithm name.
        
        Returns
        -------
        str
            Name of the clustering algorithm.
        """
        pass
    
    @abstractmethod
    def get_param_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions of algorithm parameters.
        
        Returns
        -------
        Dict[str, str]
            Parameter names mapped to their descriptions.
        """
        pass
    
    def validate_params(self, **kwargs) -> Dict[str, Any]:
        """
        Validate and merge parameters with defaults.
        
        Parameters
        ----------
        **kwargs
            Parameters to validate.
            
        Returns
        -------
        Dict[str, Any]
            Validated parameter dictionary.
            
        Raises
        ------
        ValueError
            If invalid parameters are provided.
        """
        defaults = self.get_default_params()
        params = defaults.copy()
        
        # Update with provided parameters
        for key, value in kwargs.items():
            if key not in defaults:
                raise ValueError(f"Unknown parameter '{key}' for {self.algorithm_name}")
            params[key] = value
            
        return params 