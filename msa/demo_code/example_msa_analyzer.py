#!/usr/bin/env python3
"""
Test script for MSAAnalyzer functionality.
This replaces the original analyze_msa_clustering.py functionality with class-based approach.
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# Ensure the grandparent directory (src/) is in the Python path
current_dir = Path(__file__).resolve().parent
grandparent_dir = current_dir.parent.parent
sys.path.insert(0, str(grandparent_dir))

# Import MSA and MSAAnalyzer
from msa.msa_pipeline import MSA
from msa.msa_analyzer import MSAAnalyzer


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def create_test_log_files(temp_dir: str) -> str:
    """
    Create test log files for testing log parsing functionality.
    
    Parameters
    ----------
    temp_dir : str
        Temporary directory to create test files in.
        
    Returns
    -------
    str
        Path to the directory containing test log files.
    """
    log_dir = os.path.join(temp_dir, "MSA_JKHmmer")
    os.makedirs(log_dir, exist_ok=True)
    
    # Create test log content
    test_log_content = """
Starting DBSCAN parameter optimization...
eps    min_samples    n_clusters    n_not_clustered
3.0    3              5             12
3.5    3              7             8
4.0    3              9             4
4.5    3              6             10
5.0    3              4             15
"""
    
    # Create multiple test log files
    for i in range(1, 4):
        log_file = os.path.join(log_dir, f"Asec_clvg_TMD_{i}.log")
        with open(log_file, 'w') as f:
            f.write(test_log_content)
    
    return log_dir


def create_test_a3m_files(temp_dir: str) -> str:
    """
    Create test A3M files for testing cluster size analysis.
    
    Parameters
    ----------
    temp_dir : str
        Temporary directory to create test files in.
        
    Returns
    -------
    str
        Path to the directory containing test A3M files.
    """
    # Create chain directories
    for chain_id in [101, 102, 103]:
        chain_dir = os.path.join(temp_dir, "MSA_JKHmmer", f"chain{chain_id}")
        os.makedirs(chain_dir, exist_ok=True)
        
        # Create test A3M files with different cluster sizes
        for cluster_id in range(3):
            a3m_file = os.path.join(chain_dir, f"cluster_{cluster_id:03d}.a3m")
            
            # Create A3M content with varying numbers of sequences
            num_sequences = 5 + cluster_id * 3  # 5, 8, 11 sequences
            a3m_content = ""
            
            for seq_id in range(num_sequences):
                a3m_content += f">sequence_{seq_id}\n"
                a3m_content += "ACDEFGHIKLMNPQRSTVWY\n"
            
            with open(a3m_file, 'w') as f:
                f.write(a3m_content)
    
    return temp_dir


def test_analyzer_initialization():
    """Test MSAAnalyzer initialization with and without MSA instance."""
    logging.info("Testing MSAAnalyzer initialization")
    
    # Test initialization without MSA
    analyzer1 = MSAAnalyzer()
    assert analyzer1.msa is None
    logging.info("✓ MSAAnalyzer initialized without MSA instance")
    
    # Test initialization with MSA
    test_sequences = ["ACDEFG", "ACDEFG", "ACDE--"]
    test_descriptions = ["query", "seq1", "seq2"]
    msa = MSA(
        query_sequence="ACDEFG",
        chain_poly_type='protein',
        sequences=test_sequences,
        descriptions=test_descriptions,
        deduplicated=False
    )
    
    analyzer2 = MSAAnalyzer(msa)
    assert analyzer2.msa is not None
    assert analyzer2.msa.depth == 3
    logging.info("✓ MSAAnalyzer initialized with MSA instance")


def test_msa_get_analyzer():
    """Test MSA.get_analyzer() method."""
    logging.info("Testing MSA.get_analyzer() method")
    
    test_sequences = ["ACDEFG", "ACDEFG", "ACDE--"]
    test_descriptions = ["query", "seq1", "seq2"]
    msa = MSA(
        query_sequence="ACDEFG",
        chain_poly_type='protein',
        sequences=test_sequences,
        descriptions=test_descriptions,
        deduplicated=False
    )
    
    # Get analyzer from MSA
    analyzer = msa.get_analyzer()
    assert isinstance(analyzer, MSAAnalyzer)
    assert analyzer.msa is msa
    logging.info("✓ MSA.get_analyzer() works correctly")


def test_log_parsing():
    """Test log file parsing functionality."""
    logging.info("Testing log file parsing")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test log files
        log_dir = create_test_log_files(temp_dir)
        
        # Create analyzer and test log parsing
        analyzer = MSAAnalyzer()
        analyzer.setup_logger(temp_dir)
        
        # Test parsing individual log file
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        test_log_file = os.path.join(log_dir, log_files[0])
        
        df = analyzer.parse_cluster_log(test_log_file)
        assert not df.empty
        assert 'epsilon' in df.columns
        assert 'n_clusters' in df.columns
        logging.info("✓ Individual log file parsing works")
        
        # Test analyzing multiple log files
        combined_df = analyzer.analyze_msa_clustering(
            log_dir, 
            log_pattern="Asec_clvg_TMD_*.log",
            min_samples=3
        )
        assert not combined_df.empty
        assert len(combined_df) > len(df)  # Should have more rows from multiple files
        logging.info("✓ Multiple log file analysis works")


def test_a3m_analysis():
    """Test A3M file cluster size analysis."""
    logging.info("Testing A3M cluster size analysis")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test A3M files
        create_test_a3m_files(temp_dir)
        
        # Create analyzer and test A3M analysis
        analyzer = MSAAnalyzer()
        analyzer.setup_logger(temp_dir)
        
        log_dir = os.path.join(temp_dir, "MSA_JKHmmer")
        df_clusters = analyzer.analyze_a3m_cluster_sizes(
            log_dir,
            chain_pattern="chain*"
        )
        
        assert not df_clusters.empty
        assert 'chain_id' in df_clusters.columns
        assert 'msa_size' in df_clusters.columns
        assert df_clusters['chain_id'].nunique() == 3  # Should have 3 chains
        logging.info("✓ A3M cluster size analysis works")


def test_visualization():
    """Test visualization functionality."""
    logging.info("Testing visualization functionality")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test data
        log_dir = create_test_log_files(temp_dir)
        create_test_a3m_files(temp_dir)
        
        output_dir = os.path.join(temp_dir, "output")
        os.makedirs(output_dir, exist_ok=True)
        
        # Create analyzer
        analyzer = MSAAnalyzer()
        analyzer.setup_logger(temp_dir)
        
        # Test epsilon vs clusters plot
        log_df = analyzer.analyze_msa_clustering(
            log_dir,
            log_pattern="Asec_clvg_TMD_*.log",
            min_samples=3
        )
        
        if not log_df.empty:
            analyzer.plot_epsilon_vs_clusters(log_df, output_dir)
            expected_file = os.path.join(output_dir, "DBSCAN_epsilon_vs_clusters.png")
            assert os.path.exists(expected_file)
            logging.info("✓ Epsilon vs clusters plot created")
        
        # Test cluster size distribution plot
        a3m_log_dir = os.path.join(temp_dir, "MSA_JKHmmer")
        cluster_df = analyzer.analyze_a3m_cluster_sizes(
            a3m_log_dir,
            chain_pattern="chain*"
        )
        
        if not cluster_df.empty:
            analyzer.plot_cluster_size_distribution(cluster_df, output_dir)
            expected_file = os.path.join(output_dir, "cluster_size_distribution.png")
            assert os.path.exists(expected_file)
            logging.info("✓ Cluster size distribution plot created")


def test_comprehensive_analysis():
    """Test the comprehensive analysis report generation."""
    logging.info("Testing comprehensive analysis report")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test data
        create_test_log_files(temp_dir)
        create_test_a3m_files(temp_dir)
        
        output_dir = os.path.join(temp_dir, "analysis_output")
        
        # Create analyzer
        analyzer = MSAAnalyzer()
        
        # Run comprehensive analysis
        results = analyzer.generate_analysis_report(
            target_dir=temp_dir,
            output_dir=output_dir,
            log_pattern="Asec_clvg_TMD_*.log",
            min_samples=3,
            chain_pattern="chain*"
        )
        
        # Check results
        assert 'cluster_logs' in results or 'cluster_sizes' in results
        assert os.path.exists(output_dir)
        logging.info("✓ Comprehensive analysis report generated")


def test_command_line_interface():
    """Test command line interface functionality."""
    logging.info("Testing command line interface")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test data
        create_test_log_files(temp_dir)
        create_test_a3m_files(temp_dir)
        
        # Test the main function (simulating command line usage)
        try:
            # Save original sys.argv
            original_argv = sys.argv.copy()
            
            # Set up arguments for testing
            sys.argv = [
                'test_msa_analyzer.py',
                '--target_dir', temp_dir,
                '--output_dir', os.path.join(temp_dir, 'cli_output'),
                '--log_pattern', 'Asec_clvg_TMD_*.log',
                '--min_samples', '3',
                '--chain_pattern', 'chain*',
                '--log_level', 'INFO'
            ]
            
            # Import and run main function
            from msa.msa_analyzer import main
            main()
            
            # Check if output directory was created
            output_dir = os.path.join(temp_dir, 'cli_output')
            assert os.path.exists(output_dir)
            logging.info("✓ Command line interface works")
            
        finally:
            # Restore original sys.argv
            sys.argv = original_argv


def main():
    """Main test function."""
    setup_logging()
    
    logging.info("Starting MSAAnalyzer comprehensive tests...")
    
    try:
        test_analyzer_initialization()
        test_msa_get_analyzer()
        test_log_parsing()
        test_a3m_analysis()
        test_visualization()
        test_comprehensive_analysis()
        test_command_line_interface()
        
        logging.info("🎉 All MSAAnalyzer tests passed successfully!")
        
    except Exception as e:
        logging.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 