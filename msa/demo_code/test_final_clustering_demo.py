#!/usr/bin/env python3
"""
Final comprehensive demonstration of the MSA clustering framework.

This script showcases the complete functionality of the new clustering system
with real MSA data from the KaiB dataset.
"""

import os, sys
import time
import logging
from pathlib import Path

from msa_pipeline import MSA
from clustering.utils.postprocessing import (
    analyze_cluster_composition, 
    generate_cluster_summary_report,
    extract_cluster_representatives
)


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def demonstrate_clustering_framework():
    """Comprehensive demonstration of the clustering framework."""
    print("🚀  MSA Clustering Framework - Comprehensive Demo")
    print("="*60)
    
    # Load real MSA file
    msa_file = "/home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m"
    
    print(f"📁 Loading MSA from KaiB dataset...")
    
    msa = MSA.from_file(
        file_path=msa_file,
        chain_poly_type='protein',
        #max_depth=200,  # Reasonable size for demo
        deduplicated=True
    )
    print(f"✅ MSA loaded: {msa.depth} sequences, avg length: {sum(len(s) for s in msa.sequences)/len(msa.sequences):.1f}")
    
    # Get clustering manager
    clustering_mgr = msa.get_clustering_manager()
    print(f"🔧 Clustering manager initialized")
    print(f"   Available algorithms: {clustering_mgr.available_algorithms()}")
    
    # Test all algorithms with appropriate parameters
    algorithms_config = {
        'HDBSCAN': {
            'algorithm': 'hdbscan',
            'params': {'min_cluster_size': 8, 'min_samples': 4},
            'description': 'Hierarchical density-based clustering, good for varying cluster densities'
        },
        'Hierarchical': {
            'algorithm': 'hierarchical', 
            'params': {'n_clusters': 5, 'linkage': 'ward'},
            'description': 'Agglomerative clustering, creates well-balanced clusters'
        },
        'DBSCAN': {
            'algorithm': 'dbscan',
            'params': {'eps': 15.0, 'min_samples': 4, 'optimize_eps': False},
            'description': 'Density-based clustering, finds arbitrary shaped clusters'
        }
    }
    
    results = {}
    timing_results = {}
    
    print(f"\n🧮 Testing all clustering algorithms...")
    print("-" * 60)
    
    for name, config in algorithms_config.items():
        print(f"\n{name} Clustering:")
        print(f"  📋 {config['description']}")
        print(f"  ⚙️  Parameters: {config['params']}")
        
        try:
            start_time = time.time()
            result = clustering_mgr.cluster(config['algorithm'], **config['params'])
            elapsed_time = time.time() - start_time
            
            results[name] = result
            timing_results[name] = elapsed_time
            
            print(f"  ✅ Completed in {elapsed_time:.2f} seconds")
            print(f"     🎯 {result.n_clusters} clusters found")
            print(f"     🔇 {result.n_noise} noise points ({result.n_noise/len(result.sequences)*100:.1f}%)")
            
            if result.n_clusters > 0:
                cluster_sizes = [result.cluster_labels.count(i) for i in range(result.n_clusters)]
                cluster_sizes.sort(reverse=True)
                print(f"     📊 Cluster sizes: {cluster_sizes[:5]}{'...' if len(cluster_sizes) > 5 else ''}")
            
        except Exception as e:
            print(f"  ❌ Failed: {e}")
    
    # Comparative analysis
    if len(results) > 1:
        print(f"\n📊 Comparative Analysis:")
        print("-" * 60)
        print(f"{'Algorithm':<15} {'Time (s)':<10} {'Clusters':<10} {'Noise':<8} {'Noise %':<8} {'Largest':<8}")
        print("-" * 60)
        
        for name, result in results.items():
            time_taken = timing_results[name]
            noise_pct = result.n_noise / len(result.sequences) * 100
            largest_cluster = max(result.cluster_labels.count(i) for i in range(result.n_clusters)) if result.n_clusters > 0 else 0
            
            print(f"{name:<15} {time_taken:<10.2f} {result.n_clusters:<10} {result.n_noise:<8} {noise_pct:<8.1f} {largest_cluster:<8}")
    
    # Advanced analysis using utilities
    print(f"\n🔬 Advanced Cluster Analysis:")
    print("-" * 60)
    
    if results:
        # Pick the best result (most balanced clustering)
        best_algorithm = None
        best_score = -1
        
        for name, result in results.items():
            if result.n_clusters > 1:
                # Simple scoring: prefer more clusters with less noise
                score = result.n_clusters * (1 - result.n_noise / len(result.sequences))
                if score > best_score:
                    best_score = score
                    best_algorithm = name
        
        if best_algorithm:
            result = results[best_algorithm]
            print(f"🏆 Best clustering: {best_algorithm}")
            
            # Detailed analysis
            from clustering.utils.postprocessing import (
                analyze_cluster_composition, 
                generate_cluster_summary_report,
                extract_cluster_representatives
            )
            
            # Cluster composition
            composition = analyze_cluster_composition(result)
            print(f"   📈 Composition: {composition['n_clusters']} clusters, {composition['total_sequences']} sequences")
            print(f"   📏 Size stats: mean={composition['cluster_size_stats']['mean']:.1f}, std={composition['cluster_size_stats']['std']:.1f}")
            
            # Extract representatives
            representatives = extract_cluster_representatives(result, method='random', n_representatives=2)
            print(f"   🎯 Representatives extracted for {len(representatives)} clusters")
            
            # Show sample cluster
            if result.n_clusters > 0:
                cluster_id = 0
                cluster_sequences = result.get_cluster_sequences(cluster_id)
                cluster_descriptions = result.get_cluster_descriptions(cluster_id)
                print(f"   📝 Sample cluster {cluster_id}: {len(cluster_sequences)} sequences")
                print(f"      Example sequences: {cluster_sequences[:2]}")
    
    # Preprocessing utilities demo
    print(f"\n🛠️  Preprocessing Utilities Demo:")
    print("-" * 60)
    
    from clustering.utils.preprocessing import (
        filter_sequences_by_gaps,
        remove_redundant_sequences,
        encode_sequences
    )
    
    # Test with a subset
    test_sequences = msa.sequences[:30]
    test_descriptions = msa.descriptions[:30]
    
    print(f"   📊 Original: {len(test_sequences)} sequences")
    
    # Gap filtering
    filtered_seqs, filtered_descs, indices = filter_sequences_by_gaps(
        test_sequences, test_descriptions, gap_cutoff=0.2
    )
    print(f"   🔍 After gap filtering (20%): {len(filtered_seqs)} sequences")
    
    # Redundancy removal
    unique_seqs, unique_descs, unique_indices = remove_redundant_sequences(
        filtered_seqs, filtered_descs, similarity_threshold=0.9
    )
    print(f"   🔄 After redundancy removal (90%): {len(unique_seqs)} sequences")
    
    # Encoding
    if unique_seqs:
        encoded = encode_sequences(unique_seqs[:5], encoding_method='onehot')
        print(f"   🧮 One-hot encoding: {encoded.shape}")
    
    # Usage examples
    print(f"\n📚 Usage Examples:")
    print("-" * 60)
    print("# Basic usage:")
    print("from msa_pipeline import MSA")
    print("msa = MSA.from_file('your_file.a3m', 'protein')")
    print("clustering_mgr = msa.get_clustering_manager()")
    print("")
    print("# HDBSCAN clustering:")
    print("result = clustering_mgr.cluster('hdbscan', min_cluster_size=15)")
    print("")
    print("# Compare algorithms:")
    print("results = clustering_mgr.compare_algorithms(['dbscan', 'hdbscan'])")
    print("")
    print("# Algorithm info:")
    print("info = clustering_mgr.get_algorithm_info('hdbscan')")
    print("")
    print("# Cluster analysis:")
    print("from clustering.utils.postprocessing import analyze_cluster_composition")
    print("composition = analyze_cluster_composition(result)")
    
    print(f"\n🎉 Demo completed successfully!")
    print("✨ The new MSA clustering framework is ready for production use!")
    
    return results


def demonstrate_msa_line_usage():
    """Demonstrate how to use clustering directly from MSA pipeline code."""
    print(f"\n📖 Direct Integration Example:")
    print("-" * 60)
    
    # This shows how the clustering would be used in the actual MSA pipeline
    # referencing the line in msa_pipeline.py where it's called
    msa_file = "/home/<USER>/PROject/APP-complex/Initial_Input/Clustering/KaiB/MSA_JKHmmer/KaiB_101.a3m"
    

    
    # Load MSA as it would be in the pipeline
    msa = MSA.from_file(msa_file, 'protein', max_depth=100)
    
    # Example: line 998 in msa_pipeline.py
    clustering_mgr = msa.get_clustering_manager()
    result = clustering_mgr.cluster('hdbscan', min_cluster_size=15)
    
    print(f"✅ Direct integration successful!")
    print(f"   Line 998 equivalent: clustering_mgr.cluster('hdbscan', min_cluster_size=15)")
    print(f"   Result: {result.n_clusters} clusters, {result.n_noise} noise points")
    
    # Show how to access cluster information
    print(f"\n🔍 Accessing cluster results:")
    if result.n_clusters > 0:
        print(f"   Cluster 0 sequences: {len(result.get_cluster_sequences(0))}")
        print(f"   Cluster 0 descriptions: {len(result.get_cluster_descriptions(0))}")
        print(f"   Algorithm used: {result.algorithm_name}")
        print(f"   Parameters: {result.algorithm_params}")


if __name__ == "__main__":
    # Run comprehensive demo
    results = demonstrate_clustering_framework()
    
    # Show direct usage
    demonstrate_msa_line_usage() 
