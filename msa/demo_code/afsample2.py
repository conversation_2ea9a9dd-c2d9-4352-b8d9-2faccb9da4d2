import os
import sys
import numpy as np
import logging
from typing import List, Dict, Union, Any, Tuple, Optional

# Define a class to hold command line arguments
class FLAGS:
    method = 'afsample2'
    msa_perturbation_mode = 'random'
    msa_rand_fraction = None
    msa_perturbation_profile = None


def read_rand_profile(profile_path=None):
    """
    Read a randomization profile from a file.

    The profile file should contain position-probability pairs, where each line
    has a position (1-indexed) and a probability value separated by whitespace.

    Parameters
    ----------
    profile_path (str, optional):
        Path to the profile file. If None, returns an empty dictionary.

    Returns
    -------
    Dict[int, float]:
        Dictionary mapping positions (1-indexed) to randomization probabilities.
    """
    msa_frac = {}

    if profile_path is None or not os.path.exists(profile_path):
        logging.warning(f"Profile file not found or not specified: {profile_path}")
        return msa_frac

    try:
        with open(profile_path, 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        pos = int(parts[0])
                        prob = float(parts[1])
                        msa_frac[pos] = prob

        logging.info(f"Read randomization profile with {len(msa_frac)} positions")
    except Exception as e:
        logging.error(f"Error reading profile file {profile_path}: {e}")

    return msa_frac


def get_columns_to_randomize(msa, profile=None):
    """
    Determine which columns of an MSA to randomize based on specified method and parameters.

    Parameters
    ----------
    msa (np.ndarray):
        Multiple sequence alignment array.
    profile (str, optional):
        Path to profile file when method is 'speachaf'.

    Returns
    -------
    List[int]:
        List of column indices to randomize (0-indexed).
    """
    nres = msa.shape[1]
    if FLAGS.method=='afsample2':
        if FLAGS.msa_perturbation_mode=='random':
            if FLAGS.msa_rand_fraction:
                columns_to_randomize = np.random.choice(range(0, nres), size=int(nres*FLAGS.msa_rand_fraction), replace=False) # Without replacement
            else:
                logging.info(f'Error! --msa_rand_fraction required for "{FLAGS.msa_perturbation_mode}" mode. Exiting...')
                sys.exit()

        elif FLAGS.msa_perturbation_mode=='profile':
            logging.info(f'Perturbing MSA with custom profile')
            columns_to_randomize=[]
            if FLAGS.msa_perturbation_profile!=None:
                msa_frac = read_rand_profile(FLAGS.msa_perturbation_profile)
                for pos in msa_frac:
                    r = np.random.random()
                    if msa_frac[pos]>r:
                        columns_to_randomize.append(pos-1)
            else:
                logging.info(f'Error! --msa_perturbation_profile required for "profile" mode. Exiting...')
                sys.exit()

    if FLAGS.method=='speachaf':
        logging.info(f'Perturbing MSA with "speachaf profile"')
        columns_to_randomize=[]
        if profile!=None:
            msa_frac = read_rand_profile(profile)
            for pos in msa_frac:
                r = np.random.random()
                if msa_frac[pos]>r:
                    columns_to_randomize.append(pos-1)
        else:
            logging.info(f'Error! --msa_perturbation_profile required for speachaf. Exiting...')
            sys.exit()
    return columns_to_randomize


def randomize_msa_columns(msa, columns_to_randomize, alphabet=None):
    """
    Randomize specified columns in an MSA.

    Parameters
    ----------
    msa (np.ndarray):
        Multiple sequence alignment array.
    columns_to_randomize (List[int]):
        List of column indices to randomize (0-indexed).
    alphabet (List[str], optional):
        List of characters to use for randomization.
        If None, uses the unique characters found in the MSA.

    Returns
    -------
    np.ndarray:
        MSA with randomized columns.
    """
    if not columns_to_randomize:
        return msa

    # Create a copy of the MSA to avoid modifying the original
    randomized_msa = msa.copy()

    # If no alphabet is provided, use the unique characters in the MSA
    if alphabet is None:
        alphabet = np.unique(msa).tolist()
        # Remove gap characters if present
        if '-' in alphabet:
            alphabet.remove('-')
        if '.' in alphabet:
            alphabet.remove('.')

    # For each column to randomize
    for col in columns_to_randomize:
        # Get the number of sequences
        n_seqs = msa.shape[0]

        # Generate random indices into the alphabet
        random_indices = np.random.randint(0, len(alphabet), size=n_seqs)

        # Assign random characters to the column
        for i in range(n_seqs):
            randomized_msa[i, col] = alphabet[random_indices[i]]

    return randomized_msa


def read_msa_file(msa_file: str) -> np.ndarray:
    """
    Read an MSA file and convert it to a numpy array.

    Parameters
    ----------
    msa_file (str):
        Path to the MSA file.

    Returns
    -------
    np.ndarray:
        MSA as a numpy array.
    """
    sequences = []
    headers = []

    with open(msa_file, 'r') as f:
        current_seq = ""
        for line in f:
            line = line.strip()
            if not line:
                continue
            if line.startswith('>'):
                if current_seq:
                    sequences.append(list(current_seq))
                    current_seq = ""
                headers.append(line)
            else:
                current_seq += line
        if current_seq:
            sequences.append(list(current_seq))

    if not sequences:
        logging.error(f"No sequences found in {msa_file}")
        sys.exit(1)

    # Convert to numpy array
    return np.array(sequences), headers


def write_msa_file(msa: np.ndarray, output_file: str, headers: List[str] = None):
    """
    Write an MSA array to a file.

    Parameters
    ----------
    msa (np.ndarray):
        MSA as a numpy array.
    output_file (str):
        Path to the output file.
    headers (List[str], optional):
        List of sequence headers. If None, generates default headers.
    """
    if headers is None:
        headers = [f">sequence_{i+1}" for i in range(msa.shape[0])]

    with open(output_file, 'w') as f:
        for i, seq in enumerate(msa):
            f.write(f"{headers[i]}\n")
            f.write(''.join(seq) + '\n')