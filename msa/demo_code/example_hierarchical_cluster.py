import os
import sys
import numpy as np
import pandas as pd
import logging
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add grandparent directory to path to import modules (src/ directory)
current_dir = os.path.dirname(os.path.abspath(__file__))
grandparent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, grandparent_dir)

from msa.msa_pipeline import MSA
from msa.hierarchical_cluster import HierarchicalClusterMSA

from utils.os import io_utils

def test_hierarchical_clustering():
    """
    Tests the hierarchical clustering functionality.
    """
    # Load A3M files
    a3m_dir = '/home/<USER>/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/MSA_JKHmmer/chain101'
    a3m1_list = io_utils.read_input(f'{a3m_dir}/../Asec_clvg_101.a3m')
    
    # Convert list to string for a3m format
    a3m1_str = ''.join(a3m1_list)

    # Get query sequence
    query_sequence = a3m1_list[1].strip()  # Remove newline character
    chain_poly_type = 'protein'

    # Create MSA object
    print("\n1. Creating MSA object from A3M file")
    msa = MSA.from_a3m(query_sequence, chain_poly_type, a3m1_str)
    print(f"MSA depth: {msa.depth}")
    
    # Create output directory for test
    output_dir = 'test_hierarchical_cluster_output'
    os.makedirs(output_dir, exist_ok=True)
    
    # Convert MSA to HierarchicalClusterMSA
    print("\n2. Converting MSA to HierarchicalClusterMSA using to_hierarchical_cluster_msa()")
    hierarchical_msa = msa.to_hierarchical_cluster_msa()
    print(f"HierarchicalClusterMSA type: {type(hierarchical_msa)}")
    print(f"HierarchicalClusterMSA depth: {hierarchical_msa.depth}")
    
    # Run cluster method on HierarchicalClusterMSA
    print("\n3. Running cluster method on HierarchicalClusterMSA")
    cluster_results = hierarchical_msa.cluster(
        output_dir=output_dir,
        gap_cutoff=0.25,
        min_cluster_size=5,
        n_clusters=3,
        save_fasta_files=True,
        keyword="test_hierarchical_cluster"
    )
    
    # Print clustering results
    print("\n4. Clustering Results:")
    print(f"Number of clusters: {len(cluster_results.clusters)}")
    print("\nCluster Metadata:")
    print(cluster_results.cluster_metadata)
    
    # Print information about each cluster
    print("\n5. Cluster Information:")
    for i, cluster in enumerate(cluster_results.clusters):
        print(f"\nCluster {i}:")
        print(f"  Size: {cluster.depth}")
        print(f"  First few sequences:")
        for j, (desc, seq) in enumerate(zip(cluster.descriptions[:3], cluster.sequences[:3])):
            print(f"    {desc}: {seq}")
        if cluster.depth > 3:
            print(f"    ... and {cluster.depth - 3} more sequences")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_hierarchical_clustering() 