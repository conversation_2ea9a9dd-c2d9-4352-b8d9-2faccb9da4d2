#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze and visualize MSA data, including sequence conservation and similarity.
This is a CLI wrapper around the MSAVisualizer class.
Now uses the improved separation of concerns with MSA.from_file().
"""

import os, sys
import logging
import argparse

from typing import List, Dict, Any, Tuple
from msa.msa_pipeline import MSA


def setup_logging(debug: bool = False) -> None:
    """
    Set up logging configuration.

    Parameters
    ----------
    debug : bool
        Whether to set logging level to DEBUG.
    """
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def analyze_msa(
    msa_file: str,
    output_dir: str,
    prefix: str = "",
    similarity_method: str = 'identity',
    cluster_sequences: bool = True,
    chain_poly_type: str = 'protein'
) -> None:
    """
    Analyze MSA data and create visualizations using the improved MSA workflow.

    Parameters
    ----------
    msa_file : str
        Path to the MSA file.
    output_dir : str
        Directory to save output files.
    prefix : str, optional
        Prefix for output filenames.
    similarity_method : str, optional
        Method to calculate sequence similarity ('identity' or 'blosum62').
    cluster_sequences : bool, optional
        Whether to cluster sequences in similarity visualization.
    chain_poly_type : str, optional
        Type of polymer chain ('protein', 'rna', 'dna').
        Defaults to 'protein'.
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    logging.info(f'Reading MSA file: {msa_file}')
    
    try:
        # Create MSA from file using the new MSA.from_file() method
        msa = MSA.from_file(msa_file, chain_poly_type)
        logging.info(f'Successfully read {msa.depth} sequences')
        
        # Get visualizer from MSA instance
        visualizer = msa.get_visualizer()
        
        # Generate all visualizations
        visualizer.visualize_msa(
            output_dir=output_dir,
            prefix=prefix,
            similarity_method=similarity_method,
            cluster_sequences=cluster_sequences
        )

        logging.info(f"Analysis complete. Visualizations saved in {output_dir}")
        
    except FileNotFoundError as e:
        logging.error(f"MSA file not found: {e}")
        raise
    except ValueError as e:
        logging.error(f"Error parsing MSA file: {e}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error during analysis: {e}")
        raise

    

def main():
    """Main function to run MSA analysis."""
    parser = argparse.ArgumentParser(description="Analyze and visualize MSA data")

    parser.add_argument(
        "--msa_file", required=True,
        help="Path to the MSA file (supported formats: fasta, a3m, stockholm)"
    )
    parser.add_argument(
        "--output_dir", required=True,
        help="Directory to save output files"
    )
    parser.add_argument(
        "--prefix", default="",
        help="Prefix for output filenames"
    )
    parser.add_argument(
        "--similarity_method",
        choices=['identity', 'blosum62'],
        default='identity',
        help="Method to calculate sequence similarity"
    )
    parser.add_argument(
        "--no_clustering",
        action='store_true',
        help="Disable sequence clustering in similarity visualization"
    )
    parser.add_argument(
        "--chain_poly_type",
        choices=['protein', 'rna', 'dna'],
        default='protein',
        help="Type of polymer chain"
    )
    parser.add_argument(
        "--debug",
        action='store_true',
        help="Enable debug logging"
    )

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.debug)

    # Run analysis
    analyze_msa(
        msa_file=args.msa_file,
        output_dir=args.output_dir,
        prefix=args.prefix,
        similarity_method=args.similarity_method,
        cluster_sequences=not args.no_clustering,
        chain_poly_type=args.chain_poly_type
    )

if __name__ == "__main__":
    main()