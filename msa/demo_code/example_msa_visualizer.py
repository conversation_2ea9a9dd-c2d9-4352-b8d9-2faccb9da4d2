#!/usr/bin/env python3
"""
Test script for MSAVisualizer functionality.
"""

import os
import sys
import logging
from pathlib import Path

# Ensure the grandparent directory (src/) is in the Python path
current_dir = Path(__file__).resolve().parent
grandparent_dir = current_dir.parent.parent
sys.path.insert(0, str(grandparent_dir))

# Import MSA and MSAVisualizer
from msa.msa_pipeline import MSA
from msa.msa_visualizer import MSAVisualizer


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def test_direct_visualization():
    """Test creating a visualizer directly from an MSA instance."""
    logging.info("Testing direct visualization from MSA instance")
    
    # Create a simple test MSA
    query_sequence = "ACDEFGHIKLMNPQRSTVWY"
    sequences = [
        "ACDEFGHIKLMNPQRSTVWY",  # Query sequence
        "ACDEFGHIKLMNPQRSTVW-",  # One gap
        "ACDEFGHIKLMNPQRST---",  # Three gaps
        "ACDEFGHIKLMNPQRSTWWY",  # One substitution
    ]
    descriptions = ["query", "seq1", "seq2", "seq3"]
    
    # Create MSA instance
    msa = MSA(
        query_sequence=query_sequence,
        chain_poly_type='protein',
        sequences=sequences,
        descriptions=descriptions,
        deduplicated=False
    )
    
    # Get visualizer from MSA
    visualizer = msa.get_visualizer()
    
    # Create output directory
    output_dir = "test_visualizer_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Test all visualization methods
    try:
        # Test conservation visualization
        conservation_output = os.path.join(output_dir, "test_conservation.pdf")
        visualizer.visualize_conservation(conservation_output)
        logging.info(f"Conservation visualization created: {conservation_output}")
        
        # Test similarity visualization
        similarity_output = os.path.join(output_dir, "test_similarity.pdf")
        visualizer.visualize_similarity(similarity_output)
        logging.info(f"Similarity visualization created: {similarity_output}")
        
        # Test combined visualization
        visualizer.visualize_msa(output_dir, prefix="test_combined_")
        logging.info(f"Combined visualizations created in: {output_dir}")
        
        logging.info("Direct visualization test completed successfully!")
        
    except Exception as e:
        logging.error(f"Error during visualization: {e}")
        raise


def test_file_based_visualization():
    """Test creating a visualizer from a file."""
    logging.info("Testing file-based visualization")
    
    # Create a test FASTA file
    test_fasta_content = """>query
ACDEFGHIKLMNPQRSTVWY
>seq1
ACDEFGHIKLMNPQRSTVW-
>seq2
ACDEFGHIKLMNPQRST---
>seq3
ACDEFGHIKLMNPQRSTWWY
"""
    
    # Save test file
    test_file = "test_input.fasta"
    with open(test_file, 'w') as f:
        f.write(test_fasta_content)
    
    try:
        # Create visualizer from file
        # visualizer, sequences, labels = MSAVisualizer.from_file(test_file, 'protein') # Original line to be commented or removed
        # The from_file method in MSAVisualizer was removed as it's now part of MSA class.
        # To test file-based visualization, we should first create an MSA object from the file.
        msa_from_file = MSA.from_file(file_path=test_file, chain_poly_type='protein')
        visualizer = msa_from_file.get_visualizer()
        sequences = msa_from_file.sequences
        labels = msa_from_file.descriptions

        logging.info(f"Loaded {len(sequences)} sequences from file")
        
        # Create output directory
        output_dir = "test_file_visualizer_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # Test visualization
        visualizer.visualize_msa(output_dir, prefix="file_test_")
        logging.info(f"File-based visualizations created in: {output_dir}")
        
        logging.info("File-based visualization test completed successfully!")
        
    except Exception as e:
        logging.error(f"Error during file-based visualization: {e}")
        raise
    finally:
        # Clean up test file
        if os.path.exists(test_file):
            os.remove(test_file)


def main():
    """Main test function."""
    setup_logging()
    
    logging.info("Starting MSAVisualizer tests...")
    
    try:
        test_direct_visualization()
        test_file_based_visualization()
        
        logging.info("All MSAVisualizer tests completed successfully!")
        
    except Exception as e:
        logging.error(f"Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 