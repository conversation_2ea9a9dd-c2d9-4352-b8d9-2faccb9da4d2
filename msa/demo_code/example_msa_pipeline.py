import os, sys
import numpy as np

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from msa.msa_pipeline import MSA, sequences_are_feature_equivalent
from utils.os import io_utils


print("=== MSA class 테스트 ===")
print("\n1. A3M 파일 로드 및 MSA 객체 생성")
a3m = '/home/<USER>/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/MSA_JKHmmer/chain101'
a3m1_list = io_utils.read_input(f'{a3m}/Asec_clvg_101_000.a3m')
a3m2_list = io_utils.read_input(f'{a3m}/Asec_clvg_101_010.a3m')

# Convert list to string for a3m format
a3m1_str = ''.join(a3m1_list)
a3m2_str = ''.join(a3m2_list)

query_sequence = a3m1_list[1].strip()  # Remove newline character
chain_poly_type = 'protein'

msa1 = MSA.from_a3m(query_sequence, chain_poly_type, a3m1_str)
msa2 = MSA.from_a3m(query_sequence, chain_poly_type, a3m2_str)

# Print MSA information
print(f"MSA1 depth: {msa1.depth}")
print(f"MSA2 depth: {msa2.depth}")


print("\n2. MSA 병합 테스트")
# Test merging MSAs
merged_msa = MSA.from_multiple_msas([msa1, msa2])
print(f"Merged MSA depth: {merged_msa.depth}")


print("\n3. 빈 MSA 생성 테스트")
# Test creating an empty MSA
empty_msa = MSA.from_empty(query_sequence, chain_poly_type)
print(f"Empty MSA depth: {empty_msa.depth}")


print("\n4. 시퀀스 인코딩 테스트")
# Test sequence encoding
max_len = 100  # Use a smaller length for testing
encoded_sequences = msa1.encode_sequences(max_len=max_len, encoding_method='onehot')
print(f"Encoded shape (onehot): {encoded_sequences.shape}")

# Test MSA Transformer encoding
try:
    print("\n4-1. MSA Transformer 인코딩 테스트")
    # Use a small subset for testing to be fast
    small_msa = MSA.from_empty(query_sequence, chain_poly_type)
    small_msa.sequences = msa1.sequences[:3]  # Take just a few sequences
    small_msa.descriptions = msa1.descriptions[:3]
    
    # Try to encode with MSA Transformer
    encoded_transformer = small_msa.encode_sequences(max_len=max_len, encoding_method='MSAtransformer')
    print(f"Encoded shape (MSA Transformer): {encoded_transformer.shape}")
    print(f"Example values: {encoded_transformer[0, :5]} (first 5 values of first sequence)")
except Exception as e:
    print(f"MSA Transformer encoding failed: {e}")
    print("Make sure you have PyTorch, ESM, and required dependencies installed.")
    import traceback
    traceback.print_exc()


print("\n5. 특성 추출 테스트")
# Test feature extraction
try:
    features = msa1.featurize()
    print("Features extracted successfully")
    for key, value in features.items():
        print(f"  {key}: {value.shape}")
except Exception as e:
    print(f"Error in feature extraction: {e}")


print("\n6. 시퀀스 비교 테스트")
# Test sequence comparison
seq1 = query_sequence
seq2 = msa1.sequences[0]
print(f"Query sequence: {seq1[:20]}...")
print(f"First MSA sequence: {seq2[:20]}...")
are_equivalent = sequences_are_feature_equivalent(seq1, seq2, chain_poly_type)
print(f"Sequences are equivalent: {are_equivalent}")


print("\n7. A3M 형식 변환 테스트")
# Test conversion to A3M format
a3m_output = msa1.to_a3m()
print(f"A3M output (first 100 chars): {a3m_output[:100]}...")

