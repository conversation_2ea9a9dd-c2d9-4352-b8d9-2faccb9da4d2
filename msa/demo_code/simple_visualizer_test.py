#!/usr/bin/env python3
"""
Simple test script for MSAVisualizer functionality.
Tests the visualization functions directly without requiring the full MSA class.
"""

import os
import sys
import logging
import importlib.util
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Ensure the grandparent directory (src/) is in the Python path
current_dir = Path(__file__).resolve().parent
grandparent_dir = current_dir.parent.parent
sys.path.insert(0, str(grandparent_dir))

# Directly import visualize_msa functions
from utils.visualization.visualize_msa import plot_conservation_heatmap, plot_sequence_similarity

def test_direct_visualization():
    """Test visualization functions directly."""
    logging.info("Testing direct visualization functions")
    
    # Sample data for testing
    sequences = [
        "ACDEFGHIKLMNPQRSTVWY",
        "ACDEFGHIKLMNPQRSTVW-",
        "ACDEFGHIKLMNPQRST---",
        "ACDEFGHIKLMNPQRSTWWY",
        "ACDEFGHIK-MNPQRSTVWY"
    ]
    
    labels = ["query", "seq1", "seq2", "seq3", "seq4"]
    position_labels = list(range(1, 21))  # 1-20 for 20 amino acids
    
    # Create output directory
    output_dir = "simple_test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Test conservation heatmap
        conservation_path = os.path.join(output_dir, "simple_conservation_test.pdf")
        plot_conservation_heatmap(
            msa_data=sequences,
            output_path=conservation_path,
            reference_seq=sequences[0],
            position_labels=position_labels,
            title="Simple Conservation Test"
        )
        logging.info(f"Conservation heatmap created: {conservation_path}")
        
        # Test sequence similarity
        similarity_path = os.path.join(output_dir, "simple_similarity_test.pdf")
        plot_sequence_similarity(
            msa_data=sequences,
            output_path=similarity_path,
            method='identity',
            cluster_sequences=True,
            sequence_labels=labels,
            title="Simple Similarity Test"
        )
        logging.info(f"Sequence similarity visualization created: {similarity_path}")
        
        logging.info("Direct visualization functions test completed successfully!")
        
    except Exception as e:
        logging.error(f"Error during visualization: {e}")
        import traceback
        traceback.print_exc()
        raise


def main():
    """Main test function."""
    logging.info("Starting simple visualization tests...")
    
    try:
        test_direct_visualization()
        logging.info("All simple visualization tests completed successfully!")
        
    except Exception as e:
        logging.error(f"Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 