#!/usr/bin/env python3

import os
import sys
import argparse
import logging
from pathlib import Path

# Add parent directory to path to import msa modules
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from msa.dbscan_cluster import DBSCANClusterMSA
from msa.hierarchical_cluster import HierarchicalClusterMSA


def setup_logging(verbose: bool = False) -> None:
    """
    Setup logging configuration.
    
    Parameters
    ----------
    verbose : bool, optional
        If True, set logging level to DEBUG. Otherwise, set to INFO.
        Defaults to False.
    """
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def test_clustering_method(
    algorithm: str,
    input_file: str,
    output_dir: str,
    encoding_method: str = 'onehot',
    verbose: bool = False,
) -> None:
    """
    Test a specific clustering method with given parameters.
    
    Parameters
    ----------
    algorithm : str
        Clustering algorithm to use ('dbscan' or 'hierarchical')
    input_file : str
        Path to input A3M file
    output_dir : str
        Directory to save clustering results
    encoding_method : str, optional
        Encoding method to use ('onehot' or 'MSAtransformer').
        Defaults to 'onehot'.
    verbose : bool, optional
        Enable verbose logging.
        Defaults to False.
    """
    logging.info(f"Starting {algorithm} clustering test with {encoding_method} encoding")
    
    try:
        # Create output directory for this test
        test_output_dir = os.path.join(output_dir, f"{algorithm}_{encoding_method}")
        os.makedirs(test_output_dir, exist_ok=True)
        
        # Load MSA
        if algorithm == 'dbscan':
            msa = DBSCANClusterMSA.from_file(input_file, chain_poly_type='protein')
            logging.info(f"Loaded MSA with {msa.depth} sequences for DBSCAN clustering")
            
            # Perform clustering
            result = msa.cluster(
                output_dir=test_output_dir,
                encoding_method=encoding_method,
                save_a3m_files=True,
                keyword=f"dbscan_{encoding_method}",
                remove_lowercase=False  # Preserve lowercase letters
            )
            
        elif algorithm == 'hierarchical':
            msa = HierarchicalClusterMSA.from_file(input_file, chain_poly_type='protein')
            logging.info(f"Loaded MSA with {msa.depth} sequences for hierarchical clustering")
            
            # Perform clustering
            result = msa.cluster(
                output_dir=test_output_dir,
                encoding_method=encoding_method,
                save_a3m_files=True,
                keyword=f"hierarchical_{encoding_method}",
                remove_lowercase=False  # Preserve lowercase letters
            )
            
        else:
            raise ValueError(f"Unknown algorithm: {algorithm}")
        
        # Log results
        logging.info(f"Clustering completed successfully!")
        logging.info(f"Algorithm: {result.algorithm_type}")
        logging.info(f"Number of clusters: {len(result.clusters)}")
        logging.info(f"Parameters: {result.clustering_params}")
        
        if not result.cluster_metadata.empty:
            logging.info("Cluster metadata:")
            for _, row in result.cluster_metadata.iterrows():
                logging.info(f"  Cluster {row['cluster_rank']}: {row['size']} sequences")
        
        # Save summary
        summary_file = os.path.join(test_output_dir, "clustering_summary.txt")
        with open(summary_file, 'w') as f:
            f.write(f"Clustering Summary\n")
            f.write(f"================\n")
            f.write(f"Algorithm: {result.algorithm_type}\n")
            f.write(f"Encoding: {encoding_method}\n")
            f.write(f"Number of clusters: {len(result.clusters)}\n")
            f.write(f"Parameters: {result.clustering_params}\n")
            f.write(f"\nCluster Details:\n")
            for _, row in result.cluster_metadata.iterrows():
                f.write(f"Cluster {row['cluster_rank']}: {row['size']} sequences\n")
        
        logging.info(f"Summary saved to: {summary_file}")
        
    except Exception as e:
        logging.error(f"Error during {algorithm} clustering: {str(e)}")
        if verbose:
            import traceback
            logging.error(traceback.format_exc())
        raise


def main():
    """
    Main function to test unified clustering interface.
    """
    parser = argparse.ArgumentParser(
        description="Test unified clustering interface for MSA clustering"
    )
    parser.add_argument(
        "--input_file", "-i",
        type=str,
        required=True,
        help="Path to input A3M file"
    )
    parser.add_argument(
        "--output_dir", "-o",
        type=str,
        default="test_unified_clustering_output",
        help="Output directory for clustering results (default: test_unified_clustering_output)"
    )
    parser.add_argument(
        "--algorithm", "-a",
        type=str,
        choices=['dbscan', 'hierarchical', 'both'],
        default='both',
        help="Clustering algorithm to test (default: both)"
    )
    parser.add_argument(
        "--encoding_method", "-e",
        type=str,
        choices=['onehot', 'MSAtransformer', 'both'],
        default='both',
        help="Encoding method to test (default: both)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Check input file exists
    if not os.path.exists(args.input_file):
        logging.error(f"Input file not found: {args.input_file}")
        sys.exit(1)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    logging.info(f"Output directory: {args.output_dir}")
    
    # Determine algorithms to test
    algorithms = ['dbscan', 'hierarchical'] if args.algorithm == 'both' else [args.algorithm]
    
    # Determine encoding methods to test
    encoding_methods = ['onehot', 'MSAtransformer'] if args.encoding_method == 'both' else [args.encoding_method]
    
    # Run tests
    success_count = 0
    total_tests = len(algorithms) * len(encoding_methods)
    
    for algorithm in algorithms:
        for encoding_method in encoding_methods:
            try:
                logging.info(f"\n{'='*50}")
                logging.info(f"Testing {algorithm} with {encoding_method} encoding")
                logging.info(f"{'='*50}")
                
                test_clustering_method(
                    algorithm=algorithm,
                    input_file=args.input_file,
                    output_dir=args.output_dir,
                    encoding_method=encoding_method,
                    verbose=args.verbose
                )
                
                success_count += 1
                logging.info(f"✅ {algorithm} + {encoding_method} test PASSED")
                
            except Exception as e:
                logging.error(f"❌ {algorithm} + {encoding_method} test FAILED: {str(e)}")
    
    # Final summary
    logging.info(f"\n{'='*50}")
    logging.info(f"Test Summary: {success_count}/{total_tests} tests passed")
    logging.info(f"{'='*50}")
    
    if success_count == total_tests:
        logging.info("🎉 All tests passed successfully!")
        sys.exit(0)
    else:
        logging.error(f"⚠️  {total_tests - success_count} tests failed")
        sys.exit(1)


if __name__ == "__main__":
    main() 