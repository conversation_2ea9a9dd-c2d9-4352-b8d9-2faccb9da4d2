import os, sys
import numpy as np
import logging
import argparse  # Add argparse import

# Add grandparent directory to Python path (src/ directory)
current_dir = os.path.dirname(os.path.abspath(__file__))
grandparent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, grandparent_dir)

from msa.msa_pipeline import MSA
from msa.dbscan_cluster import DBSCANClusterMSA, ClusteringResult
from utils.os import io_utils

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_afcluster():
    """
    Test the AFCluster method of the MSA class.
    """
    print("=== Testing AFCluster method (Legacy) ===")

    # Load A3M files
    a3m_dir = '/home/<USER>/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/MSA_JKHmmer/chain101'
    a3m1_list = io_utils.read_input(f'{a3m_dir}/../Asec_clvg_101.a3m')

    # Convert list to string for a3m format
    a3m1_str = ''.join(a3m1_list)

    # Get query sequence
    query_sequence = a3m1_list[1].strip()  # Remove newline character
    chain_poly_type = 'protein'

    # Create MSA object
    print("\n1. Creating MSA object from A3M file")
    msa = MSA.from_a3m(query_sequence, chain_poly_type, a3m1_str)
    print(f"MSA depth: {msa.depth}")

    # Create output directory for test
    output_dir = 'test_afcluster_output'
    os.makedirs(output_dir, exist_ok=True)

    # Run AFCluster (legacy method)
    print("\n2. Running legacy AFCluster method")
    cluster_results = msa.AFCluster(
        output_dir=output_dir,
        gap_cutoff=0.25,
        min_eps=3.0,
        max_eps=20.0,
        eps_step=0.5,
        min_samples=3,
        save_a3m_files=True,
        keyword="test_legacy_cluster"
    )

    # Print results
    print("\n3. Legacy cluster results:")
    print(f"Number of clusters: {len(cluster_results['clusters'])}")
    print(f"Best parameters: {cluster_results['best_params']}")

    # Print cluster metadata
    print("\n4. Legacy cluster metadata:")
    metadata_df = cluster_results['cluster_metadata']
    if not metadata_df.empty:
        for idx, row in metadata_df.iterrows():
            print(f"Cluster {idx+1}:")
            print(f"  Size: {row['size']}")
            print(f"  Avg similarity within cluster: {row['avg_similarity_within_cluster']:.4f}")
            print(f"  Avg similarity to query: {row['avg_similarity_to_query']:.4f}")
    else:
        print("No clusters found.")

    print("\nLegacy AFCluster test completed successfully!")


def test_dbscan_cluster_msa(encoding_method='onehot', remove_lowercase=False):
    """
    Test the new DBSCANClusterMSA class and to_dbscan_cluster_msa() method.
    
    Parameters
    ----------
    encoding_method (str, optional):
        Encoding method to use for sequence encoding.
        Options: 'onehot', 'MSAtransformer'
        Defaults to 'onehot'.
    remove_lowercase (bool, optional):
        Whether to remove lowercase letters when using MSAtransformer encoding.
        Default is False (preserve lowercase letters).
    """
    lowercase_option = "removing" if remove_lowercase else "preserving"
    print(f"\n=== Testing DBSCANClusterMSA class with encoding_method='{encoding_method}', {lowercase_option} lowercase ===")

    # Load A3M files
    a3m_dir = '/home/<USER>/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/MSA_JKHmmer/chain101'
    a3m1_list = io_utils.read_input(f'{a3m_dir}/../Asec_clvg_101.a3m')

    # Convert list to string for a3m format
    a3m1_str = ''.join(a3m1_list)

    # Get query sequence
    query_sequence = a3m1_list[1].strip()  # Remove newline character
    chain_poly_type = 'protein'

    # Create MSA object
    print("\n1. Creating MSA object from A3M file")
    msa = MSA.from_a3m(query_sequence, chain_poly_type, a3m1_str)
    print(f"MSA depth: {msa.depth}")

    # Create output directory for test
    lowercase_str = "remove_lowercase" if remove_lowercase else "keep_lowercase"
    output_dir = f'test_dbscan_cluster_msa_{encoding_method}_{lowercase_str}_output'
    os.makedirs(output_dir, exist_ok=True)

    # Convert MSA to DBSCANClusterMSA
    print("\n2. Converting MSA to DBSCANClusterMSA using to_dbscan_cluster_msa()")
    dbscan_cluster_msa = msa.to_dbscan_cluster_msa()
    print(f"DBSCANClusterMSA type: {type(dbscan_cluster_msa)}")
    print(f"DBSCANClusterMSA depth: {dbscan_cluster_msa.depth}")

    # Run cluster method on DBSCANClusterMSA with specified encoding method
    print(f"\n3. Running cluster method on DBSCANClusterMSA with encoding_method='{encoding_method}', remove_lowercase={remove_lowercase}")
    cluster_results = dbscan_cluster_msa.cluster(
        output_dir=output_dir,
        gap_cutoff=0.25,
        min_eps=3,
        max_eps=20,
        eps_step=0.5,
        min_samples=3,
        encoding_method=encoding_method,  # Pass the encoding method parameter
        save_a3m_files=True,
        keyword=f"test_{encoding_method}_{lowercase_str}_cluster",
        remove_lowercase=remove_lowercase
    )

    # Print results
    print("\n4. New cluster results:")
    print(f"Result type: {type(cluster_results)}")
    print(f"Number of clusters: {len(cluster_results.clusters)}")
    print(f"Best parameters: {cluster_results.best_params}")

    # Print cluster metadata
    print("\n5. New cluster metadata:")
    metadata_df = cluster_results.cluster_metadata
    if not metadata_df.empty:
        for idx, row in metadata_df.iterrows():
            print(f"Cluster {idx+1}:")
            print(f"  Size: {row['size']}")
            print(f"  Avg similarity within cluster: {row['avg_similarity_within_cluster']:.4f}")
            print(f"  Avg similarity to query: {row['avg_similarity_to_query']:.4f}")
    else:
        print("No clusters found.")

    # Check if A3M files were created
    print("\n6. Checking A3M files:")
    a3m_files = [f for f in os.listdir(output_dir) if f.endswith('.a3m')]
    print(f"Number of A3M files created: {len(a3m_files)}")
    for a3m_file in a3m_files:
        print(f"  {a3m_file}")

    print(f"\nDBSCANClusterMSA test with encoding_method='{encoding_method}', remove_lowercase={remove_lowercase} completed successfully!")


def test_lowercase_preservation(encoding_method='onehot', remove_lowercase=False):
    """
    Test that lowercase letters are preserved in the clustered MSA.
    
    Parameters
    ----------
    encoding_method (str, optional):
        Encoding method to use for sequence encoding.
        Options: 'onehot', 'MSAtransformer'
        Defaults to 'onehot'.
    remove_lowercase (bool, optional):
        Whether to remove lowercase letters when using MSAtransformer encoding.
        Default is False (preserve lowercase letters).
    """
    lowercase_option = "removing" if remove_lowercase else "preserving"
    print(f"\n=== Testing Lowercase Preservation in DBSCANClusterMSA with encoding_method='{encoding_method}', {lowercase_option} lowercase ===")

    # Create a simple MSA with lowercase letters
    # Using valid amino acid letters that ESM can process
    query_sequence = "ACDEFGHIKL"
    sequences = [
        "ACDEFGHIKL",  # Query sequence
        "ACDEFgHIKL",  # Has lowercase 'g'
        "ACdEFGHIKL",  # Has lowercase 'd'
        "ACDEFGHiKL",  # Has lowercase 'i'
        "acDEFGHIKL",  # Has lowercase 'ac'
    ]
    descriptions = [
        "Query",
        "Seq1_with_lowercase_g",
        "Seq2_with_lowercase_d",
        "Seq3_with_lowercase_i",
        "Seq4_with_lowercase_ac",
    ]

    # Create MSA object
    print("\n1. Creating MSA object with lowercase letters")
    msa = MSA(
        query_sequence=query_sequence,
        chain_poly_type='protein',
        sequences=sequences,
        descriptions=descriptions,
        deduplicated=False
    )
    print(f"MSA depth: {msa.depth}")
    print("Original sequences:")
    for i, seq in enumerate(msa.sequences):
        print(f"  {i+1}: {seq}")

    # Create output directory for test
    lowercase_str = "remove_lowercase" if remove_lowercase else "keep_lowercase"
    output_dir = f'test_lowercase_preservation_{encoding_method}_{lowercase_str}'
    os.makedirs(output_dir, exist_ok=True)

    # Convert MSA to DBSCANClusterMSA
    print("\n2. Converting MSA to DBSCANClusterMSA")
    dbscan_cluster_msa = msa.to_dbscan_cluster_msa()

    # Run cluster method on DBSCANClusterMSA
    print(f"\n3. Running cluster method on DBSCANClusterMSA with encoding_method='{encoding_method}', remove_lowercase={remove_lowercase}")
    cluster_results = dbscan_cluster_msa.cluster(
        output_dir=output_dir,
        gap_cutoff=0.25,
        min_eps=1.0,  # Lower eps for this small test
        max_eps=5.0,
        eps_step=0.5,
        min_samples=2,  # Lower min_samples for this small test
        encoding_method=encoding_method,  # Pass the encoding method parameter
        save_a3m_files=True,
        keyword=f"lowercase_test_{encoding_method}_{lowercase_str}",
        remove_lowercase=remove_lowercase
    )

    # Check if clusters were created
    print("\n4. Checking clusters:")
    if cluster_results.clusters:
        print(f"Number of clusters: {len(cluster_results.clusters)}")

        # Check if lowercase letters are preserved in each cluster
        for i, cluster in enumerate(cluster_results.clusters):
            print(f"\nCluster {i+1} sequences:")
            for j, seq in enumerate(cluster.sequences):
                print(f"  {j+1}: {seq}")

            # Save A3M file for manual inspection
            output_filename = os.path.join(output_dir, f"manual_cluster_{i+1}.a3m")
            with open(output_filename, 'w') as f:
                f.write(cluster.to_a3m())
            print(f"Saved cluster to {output_filename}")
    else:
        print("No clusters were created.")

    # Check A3M files
    print("\n5. Checking A3M files:")
    a3m_files = [f for f in os.listdir(output_dir) if f.endswith('.a3m')]
    print(f"Number of A3M files created: {len(a3m_files)}")
    for a3m_file in a3m_files:
        print(f"  {a3m_file}")

        # Read the A3M file and check for lowercase letters
        a3m_content = io_utils.read_input(os.path.join(output_dir, a3m_file))
        has_lowercase = False
        for line in a3m_content:
            if not line.startswith('>') and any(c.islower() for c in line):
                has_lowercase = True
                break
        print(f"    Has lowercase letters: {has_lowercase}")

    print(f"\nLowercase preservation test with encoding_method='{encoding_method}', remove_lowercase={remove_lowercase} completed!")


if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test DBSCANClusterMSA and AFCluster')
    parser.add_argument('--skip_afcluster', action='store_true', help='Skip AFCluster test')
    parser.add_argument('--test_lowercase', action='store_true', help='Run lowercase preservation test')
    parser.add_argument('--encoding_method', type=str, default='onehot', choices=['onehot', 'MSAtransformer'],
                        help='Encoding method to use (onehot or MSAtransformer)')
    parser.add_argument('--run_all', action='store_true', help='Run tests with both encoding methods')
    parser.add_argument('--remove_lowercase', action='store_true', help='Remove lowercase letters when using MSAtransformer encoding')
    args = parser.parse_args()
    
    test_dbscan_cluster_msa(encoding_method='onehot', remove_lowercase=False)
    test_dbscan_cluster_msa(encoding_method='MSAtransformer', remove_lowercase=args.remove_lowercase)    
    
    # Run tests based on arguments
    if not args.skip_afcluster:
        test_afcluster()
    
    if args.test_lowercase:
        print("\n==================================================")
        print("RUNNING LOWERCASE PRESERVATION TEST")
        print("==================================================")
        test_lowercase_preservation(encoding_method=args.encoding_method, remove_lowercase=args.remove_lowercase)
    
    if args.run_all:
        print("\n==================================================")
        print("RUNNING TESTS WITH ONEHOT ENCODING")
        print("==================================================")
        test_dbscan_cluster_msa(encoding_method='onehot', remove_lowercase=args.remove_lowercase)
        
        print("\n==================================================")
        print("RUNNING TESTS WITH MSATRANSFORMER ENCODING")
        print("==================================================")
        test_dbscan_cluster_msa(encoding_method='MSAtransformer', remove_lowercase=args.remove_lowercase)
    else:
        # Run tests with specified encoding method
        test_dbscan_cluster_msa(encoding_method=args.encoding_method, remove_lowercase=args.remove_lowercase) 