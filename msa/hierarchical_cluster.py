import os, sys
import logging
import numpy as np
import pandas as pd

from typing import List, Tuple, Dict, Any, Optional, Sequence, Literal, Union
from scipy.cluster.hierarchy import ward, fcluster
from scipy.spatial.distance import squareform
from sklearn.metrics import pairwise_distances
from dataclasses import dataclass

from msa_pipeline import MSA
try:
    if torch.cuda.is_available():
        from cuml import ward
    print("cuML's ward imported successfully")
except ImportError:
    print("cuML is not installed. Using scikit-learn's ward instead.")
    cuml_DBSCAN = None


@dataclass
class ClusteringResult:
    """
    Data class to store clustering results from any clustering algorithm.

    Attributes
    ----------
    clusters : List[MSA]
        List of MSA objects, one for each cluster
    cluster_metadata : pd.DataFrame
        DataFrame with cluster information (cluster_id, size, consensus_sequence, etc.)
    clustering_assignments : pd.DataFrame
        DataFrame with sequence assignments (includes 'cluster_label' column)
    clustering_params : Dict[str, Any]
        Dictionary with clustering parameters and metrics
    algorithm_type : str
        Type of clustering algorithm used ('dbscan' or 'hierarchical')
    """
    clusters: List['MSA']
    cluster_metadata: pd.DataFrame
    clustering_assignments: pd.DataFrame
    clustering_params: Dict[str, Any]
    algorithm_type: str


class HierarchicalClusterMSA(MSA):
    """
    Multiple Sequence Alignment container with hierarchical clustering capabilities.
    Inherits from MSA class and adds hierarchical clustering functionality.
    """


    def cluster(self,
        output_dir: Optional[str] = None,
        gap_cutoff: float = 0.25,
        min_cluster_size: int = 19,
        n_clusters: int = 10,
        encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
        save_a3m_files: bool = False,
        keyword: str = "cluster",
        remove_lowercase: bool = False,
        remove_special_chars: bool = True,
    ) -> ClusteringResult:
        """
        Clusters sequences in the MSA using Ward's hierarchical clustering algorithm.
        Based on the implementation in the provided script with unified encoding interface.

        Parameters
        ----------
        output_dir (Optional[str], optional):
            Directory to save output files. If None, files are not saved.
            Defaults to None.
        gap_cutoff (float, optional):
            Maximum fraction of gaps allowed in a sequence.
            Sequences with more gaps will be removed.
            Defaults to 0.25.
        min_cluster_size (int, optional):
            Minimum size of clusters.
            Defaults to 19.
        n_clusters (int, optional):
            Number of top clusters to return.
            Defaults to 10.
        encoding_method (Literal['onehot', 'MSAtransformer'], optional):
            Method to use for encoding sequences.
            Defaults to 'onehot'.
        save_a3m_files (bool, optional):
            If True, saves A3M files for each cluster.
            Defaults to False.
        keyword (str, optional):
            Prefix for output files.
            Defaults to "cluster".
        remove_lowercase (bool, optional):
            Whether to remove lowercase letters when using MSAtransformer encoding.
            Default is False (preserve lowercase letters).
        remove_special_chars (bool, optional):
            Whether to remove special characters like '.' and '*' when using MSAtransformer encoding.
            Default is True.

        Returns
        -------
        ClusteringResult:
            A ClusteringResult object containing:
            - 'clusters': List of HierarchicalClusterMSA objects, one for each cluster
            - 'cluster_metadata': DataFrame with cluster information
            - 'clustering_assignments': DataFrame with sequence assignments
            - 'clustering_params': Dictionary with clustering parameters
            - 'algorithm_type': 'hierarchical'
        """
        # Create output directory if specified
        if output_dir and save_a3m_files:
            os.makedirs(output_dir, exist_ok=True)
            logging.info(f"Created output directory: {output_dir}")

        # Setup logging
        logging.info(f"Starting hierarchical clustering for MSA with {self.depth} sequences")

        # 1. Filter sequences based on gap content
        original_sequences, filtered_descriptions, filtered_indices = self._filter_sequences(gap_cutoff)
        logging.debug(f"Number of sequences after gap-filtering: {len(self.sequences)} -> {len(original_sequences)}")

        # Create sequences without lowercase for clustering, but keep original sequences
        filtered_sequences = [
            ''.join([ char for char in seq if not char.islower() ])
            for seq in original_sequences
        ]
        if not filtered_sequences:
            logging.warning(f"No sequences remain after gap filtering gap-cutoff={gap_cutoff}")
            logging.warning(f"It is an unexpected situtation with gap-cutoff={gap_cutoff}. Please change gap-cutoff value.")
            sys.exit(1)

        # 2. Create DataFrame for clustering
        df = pd.DataFrame({
            'SequenceName': filtered_descriptions,
            'sequence': filtered_sequences,
            'original_sequence': original_sequences,  # Store original sequences with lowercase [NOTE]
            'original_index': filtered_indices
        })

        # Separate query sequence
        # [NOTE] Do not change the order of the following two lines
        query_df = df.iloc[:1].copy()
        df = df.iloc[1:].copy()
        if df.empty:
            logging.warning("No sequences to cluster after removing query")
            # Return the original MSA since we can't cluster
            return ClusteringResult(
                clusters=[self],
                cluster_metadata=pd.DataFrame(),
                clustering_assignments=pd.DataFrame(),
                clustering_params={'min_cluster_size': min_cluster_size, 'n_clusters': 0},
                algorithm_type='hierarchical'
            )

        # 3. Encode sequences for clustering
        seq_lens = set(len(seq) for seq in filtered_sequences)
        assert len(seq_lens) == 1, "All sequences must be of the same length"
        max_seq_len = max(seq_lens)
        
        # Use encode_sequences method based on encoding_method        
        if encoding_method == 'onehot': # use sequence with only uppercase letters
            sequences_to_encode = df.sequence.tolist()
        elif encoding_method == 'MSAtransformer': # preserve original sequences with lowercase
            sequences_to_encode = df.original_sequence.tolist()
        else:
            raise NotImplementedError(f"The input encoding method {encoding_method} is not implemented")
            
        # Use the unified encode_sequences method 
        if encoding_method == 'MSAtransformer':
            # For MSA Transformer, use the _encode_MSAtransformer method directly
            encoded_seqs = self._encode_MSAtransformer(
                sequences=sequences_to_encode,
                max_seq_len=max_seq_len,
                remove_lowercase=remove_lowercase,
                remove_special_chars=remove_special_chars
            )
        else:
            # For other encoding methods, use the standard encode_sequences method
            encoded_seqs = self.encode_sequences(
                sequences=sequences_to_encode,
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
        
        logging.info(f"Encoded sequences shape: {encoded_seqs.shape}")
        
        # 4. Calculate distance matrix
        logging.info("Calculating distance matrix")
        distance_matrix = self.calculate_pairwise_distance(encoded_seqs)        

        # 5. Perform hierarchical clustering
        logging.info(f"Performing hierarchical clustering with min_cluster_size={min_cluster_size}")
        dist_condensed = squareform(distance_matrix)
        Z = ward(dist_condensed)

        # 6. Find optimal number of clusters
        max_clusters, cluster_labels = self._search_minsize_clustering(Z, min_cluster_size, len(df))
        if max_clusters is None:
            logging.warning("Could not find suitable clustering")
            # Create a single cluster with all sequences if no good clustering is found
            df['cluster_label'] = 0
            empty_assignments_df = df.copy()
            return ClusteringResult(
                clusters=[self],
                cluster_metadata=pd.DataFrame(),
                clustering_assignments=empty_assignments_df,
                clustering_params={'min_cluster_size': min_cluster_size, 'n_clusters': 0},
                algorithm_type='hierarchical'
            )
        logging.info(f"Found optimal clustering with {max_clusters} clusters")

        # 7. Add clustering results to DataFrame
        df['cluster_label'] = cluster_labels

        # 8. Calculate cluster sizes and sort
        cluster_sizes = df['cluster_label'].value_counts().to_dict()
        df['cluster_size'] = df['cluster_label'].map(cluster_sizes)
        df = df.sort_values(by=['cluster_size'], ascending=[False])

        # 9. Generate cluster metadata
        clusters = [x for x in df.cluster_label.unique()]
        sorted_clusters = sorted(clusters, key=lambda x: cluster_sizes.get(x, 0), reverse=True)

        logging.info(f"Found {len(clusters)} clusters")

        # 10. Generate cluster metadata and MSA objects for each cluster
        cluster_metadata = []
        cluster_msas = []

        for idx, clust in enumerate(sorted_clusters):
            # Get sequences in this cluster
            tmp = df.loc[df.cluster_label == clust]

            # Generate consensus sequence
            consensus_seq = self._generate_consensus_sequence(tmp.sequence.tolist())

            # Calculate distances using normalized edit distance
            avg_dist_to_consensus = np.mean([
                self._calculate_sequence_similarity(x, consensus_seq) for x in tmp.sequence.tolist()
            ])

            avg_dist_to_query = np.mean([
                self._calculate_sequence_similarity(x, query_df.sequence.iloc[0]) for x in tmp.sequence.tolist()
            ])

            # Create combined DataFrame with query at the top
            combined_df = pd.concat([query_df, tmp], axis=0)

            # Add to metadata
            cluster_metadata.append({
                'cluster_id': clust,
                'cluster_rank': idx,
                'consensus_sequence': consensus_seq,
                'avg_similarity_within_cluster': avg_dist_to_consensus,
                'avg_similarity_to_query': avg_dist_to_query,
                'size': len(tmp)
            })

            # Use original sequences with lowercase letters preserved
            cluster_sequences = combined_df.original_sequence.tolist()
            cluster_descriptions = combined_df.SequenceName.tolist()

            # Ensure chain_poly_type is one of the allowed literal values
            poly_type: Literal['protein', 'rna', 'dna'] = 'protein'
            if self.chain_poly_type in ('protein', 'rna', 'dna'):
                poly_type = self.chain_poly_type  # type: ignore

            # Create HierarchicalClusterMSA object for this cluster
            cluster_msa = HierarchicalClusterMSA(
                query_sequence=self.query_sequence,
                chain_poly_type=poly_type,
                sequences=cluster_sequences,
                descriptions=cluster_descriptions,
                deduplicated=False  # Already deduplicated during clustering
            )

            cluster_msas.append(cluster_msa)

            # Save A3M file if requested
            if save_a3m_files and output_dir:
                output_filename = os.path.join(output_dir, f"{keyword}_{idx:03d}.a3m")
                with open(output_filename, 'w') as f:
                    f.write(cluster_msa.to_a3m())
                logging.info(f"Wrote cluster {idx} (original label {clust}, size {len(tmp)}) to {output_filename}")

        # 11. Create DataFrames for return
        cluster_metadata_df = pd.DataFrame(cluster_metadata)

        # 12. Return results
        result = ClusteringResult(
            clusters=cluster_msas,
            cluster_metadata=cluster_metadata_df,
            clustering_assignments=df,
            clustering_params={
                'min_cluster_size': min_cluster_size,
                'n_clusters': max_clusters,
                'algorithm_type': 'hierarchical'
            },
            algorithm_type='hierarchical'
        )

        return result


    def calculate_pairwise_distance(self, 
        encoded_seqs: np.ndarray,
        metric: str = 'euclidean',
        n_jobs: int = -1,
    ) -> np.ndarray:
        """
        Calculate pairwise distances between sequence embeddings.
        
        Parameters
        ----------
        encoded_seqs : np.ndarray
            Directory to save output distance matrix.
        metric : str, optional
            Distance metric to use for pairwise_distances.
            Defaults to 'euclidean'.
        n_jobs : int, optional
            Number of jobs to run in parallel. -1 means using all processors.
            Defaults to -1.
            
        Returns
        -------
        np.ndarray
            Distance matrix between sequence embeddings.
        """
        logging.info("Starting pairwise distance calculation")
        
        # 4. Normalize embeddings (encoded_seqs is already flattened by _encode_onehot)
        encoded_seqs_scaled = (encoded_seqs - encoded_seqs.mean()) / encoded_seqs.std()
        
        # 5. Calculate pairwise distances
        logging.info(f"Calculating pairwise distances using {metric} metric")
        distance_matrix = pairwise_distances(encoded_seqs_scaled, metric=metric, n_jobs=n_jobs)
        
        return distance_matrix


    def _search_minsize_clustering(self, Z, min_size=19, n_samples=None):
        """
        Find the maximum number of clusters where all clusters have at least min_size elements.

        Parameters
        ----------
        Z : ndarray
            The hierarchical clustering encoded as a linkage matrix.
        min_size : int, optional
            The minimum cluster size.
            Defaults to 19.
        n_samples : int, optional
            The number of samples in the dataset.

        Returns
        -------
        Tuple[Optional[int], Optional[List[int]]]:
            A tuple containing:
            - The maximum number of clusters where all clusters have at least min_size elements
            - The cluster labels for each sample
        """
        if n_samples is None:
            n_samples = Z.shape[0] + 1

        maxxclust, clusters = None, None
        many_clusters = int(n_samples / 10)  # start from very high number of clusters

        for maxx in range(many_clusters, 2, -1):
            nodes = list(fcluster(Z, maxx, criterion="maxclust"))
            unique_nodes = list(set(nodes))
            sample_counts = 0

            for n in unique_nodes:
                # stop when all clusters size is above minimum
                if nodes.count(n) > min_size:
                    maxxclust = maxx
                    clusters = nodes
                    sample_counts += 1

            if sample_counts == len(unique_nodes):
                break

        return maxxclust, clusters

