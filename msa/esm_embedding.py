import os, sys
import numpy as np
import string
import logging

from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional, Union

from Bio import SeqIO
import esm
import torch
torch.set_num_threads(10)

esm_model_name_to_dim = {
    "esm_msa1b_t12_100M_UR50S": 768,
    "esm2_t33_650M_UR50D": 1280,
}

class ESMEmbedder:
    """
    Wrapper class for ESM MSA Transformer models.
    Provides functionality to embed MSA sequences using pretrained ESM models.
    """
    
    def __init__(
        self, 
        model_name: str = "esm_msa1b_t12_100M_UR50S", 
        device: Optional[str] = None,
        repr_layer: int = 12
    ):
        """
        Initialize the ESM embedder with specified model.
        
        Parameters
        ----------
        model_name : str
            Name of the ESM model to use. Default is "esm_msa1b_t12_100M_UR50S".
        device : Optional[str]
            Device to use for inference. If None, will use CUDA if available, else CPU.
        repr_layer : int
            Representation layer to extract embeddings from. Default is 12.
        """
        # Set device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device        
        
        self.repr_layer = repr_layer
        
        # Load model and alphabet
        logging.info(f"Loading ESM model {model_name} on {self.device} device")
        try:
            self.model, self.alphabet = self._load_model(model_name)
            self.batch_converter = self.alphabet.get_batch_converter()
            logging.info(f"Model loaded successfully")
            
            # Move model to device and set to eval mode
            self.model = self.model.to(self.device)
            self.model.eval()
            
            # Get embedding dimension
            self.embedding_dim = esm_model_name_to_dim.get(model_name, None)
            if self.embedding_dim is None:
                raise ValueError(f"Unsupported model name: {model_name}")
        except Exception as e:
            logging.error(f"Failed to load ESM model: {e}")
            raise

    @staticmethod
    def _load_model(model_name: str) -> Tuple[torch.nn.Module, Any]:
        """
        Load ESM model and alphabet.
        
        Parameters
        ----------
        model_name : str
            Name of the ESM model to use.
            
        Returns
        -------
        Tuple[torch.nn.Module, Any]
            Tuple containing the model and alphabet.
        """
        if model_name == "esm_msa1b_t12_100M_UR50S":
            return esm.pretrained.esm_msa1b_t12_100M_UR50S()
        elif model_name == "esm2_t33_650M_UR50D":
            return esm.pretrained.esm2_t33_650M_UR50D()
        else:
            raise ValueError(f"Unsupported model name: {model_name}")

    @staticmethod
    def preprocess_msa(
        sequences: List[str], 
        descriptions: List[str], 
        max_seq_len: int,
        remove_lowercase: bool = False,
        remove_special_chars: bool = True,
        pad_to_equal_length: bool = True
    ) -> List[Tuple[str, str]]:
        """
        Preprocess MSA sequences for ESM embedding.
        
        Parameters
        ----------
        sequences : List[str]
            List of MSA sequences.
        descriptions : List[str]
            List of sequence descriptions.
        remove_lowercase : bool, optional
            Whether to remove lowercase letters (insertions in MSA format).
            Default is False (preserve lowercase letters).
        remove_special_chars : bool, optional
            Whether to remove special characters like '.' and '*'.
            Default is True.
        pad_to_equal_length : bool, optional
            Whether to pad sequences to equal length.
            Default is True.
            
        Returns
        -------
        List[Tuple[str, str]]
            List of (description, sequence) tuples in ESM input format.
        """
        # Check if sequences and descriptions have the same length
        assert len(sequences) == len(descriptions), "Sequences and descriptions must have the same length"
        
        # Create translation table based on parameters
        deletekeys = {}
                
        if remove_lowercase:
            logging.info(' adding lowercase letters to deletion table for ESM input')
            deletekeys.update(dict.fromkeys(string.ascii_lowercase))
            
        if remove_special_chars:
            logging.info(' adding special characters to deletion table for ESM input')
            deletekeys["."] = None
            deletekeys["*"] = None
            
        # Create translation table
        translation = str.maketrans(deletekeys)
        
        # Apply translation to each sequence
        processed_sequences = [seq.translate(translation) for seq in sequences]
        
        # Replace invalid amino acid characters with 'X' to '-' gap
        processed_sequences = [
            seq.replace('X', '-') for seq in processed_sequences
        ]        
        processed_sequences = [
            ''.join(char.upper() for char in seq)
            for seq in processed_sequences
        ]        
        
        # Pad sequences to equal length if needed
        if pad_to_equal_length:
            processed_sequences = [seq.ljust(max_seq_len, '-') for seq in processed_sequences]
            
        # Create final MSA data
        processed_msa = list(zip(descriptions, processed_sequences))
        
        return processed_msa

    def embed_msa(
        self,
        sequences: List[str],
        descriptions: List[str],
        max_seqs: Optional[int] = 16000,
        max_seq_len: Optional[int] = 1022,
        remove_lowercase: bool = False,
        remove_special_chars: bool = True
    ) -> np.ndarray:
        """
        Embed MSA sequences using ESM MSA Transformer.
        
        Parameters
        ----------
        sequences : List[str]
            List of MSA sequences.
        descriptions : List[str]
            List of sequence descriptions.
        max_seq_len : Optional[int]
            Maximum sequence length. If None, no truncation is done. Default is 1022.
        max_seqs : Optional[int]
            Maximum number of sequences to embed. If None, no limit is applied. Default is 16000.
        remove_lowercase : bool, optional
            Whether to remove lowercase letters (insertions in MSA format).
            Default is False (preserve lowercase letters).
        remove_special_chars : bool, optional
            Whether to remove special characters like '.' and '*'.
            Default is True.
            
        Returns
        -------
        np.ndarray
            MSA embedding array of shape (n_sequences, seq_length, embedding_dim).
        """
        with torch.no_grad():
            try:
                # Apply limits if specified
                if max_seqs is not None and len(sequences) > max_seqs:
                    logging.warning(f"Truncating the # of sequences from {len(sequences)} sequences to {max_seqs} sequences")
                    sequences = sequences[: max_seqs]
                    descriptions = descriptions[: max_seqs]

                # Preprocess MSA with specified options
                msa_data = self.preprocess_msa(
                    sequences, 
                    descriptions, 
                    max_seq_len=max_seq_len,
                    remove_lowercase=remove_lowercase,
                    remove_special_chars=remove_special_chars
                )

                # Convert to tokens
                batch_labels, batch_strs, batch_tokens = self.batch_converter(msa_data)

                # Move to device
                batch_tokens = batch_tokens.to(self.device) # [batch, n_seqs, padding_seq_len]

                # Get embeddings
                results = self.model(
                    batch_tokens, 
                    repr_layers=[self.repr_layer], 
                    return_contacts=False
                )

                # Extract embeddings from specified layer
                embeddings = results["representations"][self.repr_layer] # [batch, n_seqs, padding_seq_len, dim]

                # Convert to numpy and return
                return embeddings.cpu().numpy()

            except Exception as e:
                logging.error(f"Error during MSA embedding: {e}")
                raise

    def save_embeddings(self, embeddings: np.ndarray, output_path: str) -> None:
        """
        Save embeddings to file.
        
        Parameters
        ----------
        embeddings : np.ndarray
            Embeddings to save.
        output_path : str
            Path to save embeddings to.
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save embeddings
        np.save(output_path, embeddings)
        logging.info(f"Saved embeddings to {output_path}") 