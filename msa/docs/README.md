# MSA Package README

## Overview

The `msa` package provides a comprehensive suite of tools for handling, analyzing, clustering, and visualizing Multiple Sequence Alignment (MSA) data. Inspired by AlphaFold 3's MSA processing methodologies, this package aims to offer an efficient and flexible way to manage and utilize MSA data in bioinformatics research, particularly within protein structure prediction pipelines.

## Package Structure

The diagram below illustrates the main components of the `msa` package and their interrelationships:

```mermaid
graph TD
    subgraph MSA_Core [MSA Core Logic - msa_pipeline.py]
        direction LR
        MSA["<strong>MSA</strong><br/>- query_sequence<br/>- chain_poly_type<br/>- sequences<br/>- descriptions"]
        style MSA fill:#lightyellow,stroke:#333,stroke-width:2px

        subgraph MSA_Constructors [Class Methods - Constructors]
            direction TB
            MSA_from_a3m["from_a3m()"]
            MSA_from_multiple_a3ms["from_multiple_a3ms()"]
            MSA_from_multiple_msas["from_multiple_msas()"]
            MSA_from_empty["from_empty()"]
            MSA_from_file["from_file()"]
        end
        MSA -- Contains --> MSA_Constructors
        
        subgraph MSA_Main_Methods [Instance Methods]
            direction TB
            MSA_to_a3m["to_a3m()"]
            MSA_featurize["featurize()"]
            MSA_encode_sequences["encode_sequences(encoding_method)"]
            MSA_to_dbscan["to_dbscan_cluster_msa()"]
            MSA_to_hierarchical["to_hierarchical_cluster_msa()"]
            MSA_get_visualizer["get_visualizer()"]
            MSA_get_analyzer["get_analyzer()"]
        end
        MSA -- Contains --> MSA_Main_Methods

        subgraph MSA_Internal_Encoding [Internal Encoding Helpers]
             direction TB
             MSA_encode_onehot["_encode_onehot()"]
             MSA_encode_MSAtransformer["_encode_MSAtransformer()"]
        end
        MSA_encode_sequences --> MSA_encode_onehot
        MSA_encode_sequences --> MSA_encode_MSAtransformer
    end

    subgraph Clustering_Modules [Clustering Modules]
        direction TB
        DBSCANClusterMSA["<strong>DBSCANClusterMSA</strong> (dbscan_cluster.py)<br/>Inherits: MSA"]
        style DBSCANClusterMSA fill:#lightblue,stroke:#333,stroke-width:2px
        DBSCANClusterMSA_cluster["cluster(encoding_method)"]
        DBSCANClusterMSA -- Has method --> DBSCANClusterMSA_cluster

        HierarchicalClusterMSA["<strong>HierarchicalClusterMSA</strong> (hierarchical_cluster.py)<br/>Inherits: MSA"]
        style HierarchicalClusterMSA fill:#lightgreen,stroke:#333,stroke-width:2px
        HierarchicalClusterMSA_cluster["cluster()"]
        HierarchicalClusterMSA -- Has method --> HierarchicalClusterMSA_cluster
    end

    subgraph Embedding_Module [Embedding Module]
        direction TB
        ESMEmbedder["<strong>ESMEmbedder</strong> (esm_embedding.py)"]
        style ESMEmbedder fill:#lightpink,stroke:#333,stroke-width:2px
        ESMEmbedder_embed_msa["embed_msa()"]
        ESMEmbedder -- Has method --> ESMEmbedder_embed_msa
    end

    subgraph Visualization_Module [Visualization Module]
        direction TB
        MSAVisualizer["<strong>MSAVisualizer</strong> (msa_visualizer.py)"]
        style MSAVisualizer fill:#lavender,stroke:#333,stroke-width:2px
        MSAVisualizer_visualize_msa["visualize_msa()"]
        MSAVisualizer_visualize_conservation["visualize_conservation()"]
        MSAVisualizer_visualize_similarity["visualize_similarity()"]
        MSAVisualizer -- Has methods --> MSAVisualizer_visualize_msa
        MSAVisualizer -- Has methods --> MSAVisualizer_visualize_conservation
        MSAVisualizer -- Has methods --> MSAVisualizer_visualize_similarity
    end

    subgraph Analysis_Module [Analysis Module]
        direction TB
        MSAAnalyzer["<strong>MSAAnalyzer</strong> (msa_analyzer.py)"]
        style MSAAnalyzer fill:#lightcoral,stroke:#333,stroke-width:2px
        MSAAnalyzer_parse_cluster_log["parse_cluster_log()"]
        MSAAnalyzer_analyze_msa_clustering["analyze_msa_clustering()"]
        MSAAnalyzer_analyze_a3m_cluster_sizes["analyze_a3m_cluster_sizes()"]
        MSAAnalyzer_plot_epsilon_vs_clusters["plot_epsilon_vs_clusters()"]
        MSAAnalyzer_plot_cluster_size_distribution["plot_cluster_size_distribution()"]
        MSAAnalyzer_generate_analysis_report["generate_analysis_report()"]
        MSAAnalyzer -- Has methods --> MSAAnalyzer_parse_cluster_log
        MSAAnalyzer -- Has methods --> MSAAnalyzer_analyze_msa_clustering
        MSAAnalyzer -- Has methods --> MSAAnalyzer_analyze_a3m_cluster_sizes
        MSAAnalyzer -- Has methods --> MSAAnalyzer_plot_epsilon_vs_clusters
        MSAAnalyzer -- Has methods --> MSAAnalyzer_plot_cluster_size_distribution
        MSAAnalyzer -- Has methods --> MSAAnalyzer_generate_analysis_report
    end
    
    subgraph Support_Files [Supporting Files & Directories]
        direction TB
        InitFile["__init__.py<br/>(Exposes MSA, MSAVisualizer, MSAAnalyzer)"]
        PipelineMD["msa_pipeline.md<br/>(Detailed MSA pipeline diagram)"]
        VisualizerMD["msa_visualizer.md<br/>(Detailed visualizer diagram)"]
        ReferenceCodeDir["reference_code/<br/>(AF3 Msa, AFSample2 scripts)"]
        ExampleCodeDir["example_*.py<br/>(Example scripts demonstrating usage)"]
    end

    %% Relationships
    MSA_to_dbscan -.-> DBSCANClusterMSA;
    MSA_to_hierarchical -.-> HierarchicalClusterMSA;
    MSA_get_visualizer -.-> MSAVisualizer;
    MSA_get_analyzer -.-> MSAAnalyzer;
    MSAVisualizer -- Uses --> MSA
    MSAAnalyzer -- Uses --> MSA

    MSA_encode_MSAtransformer -- Calls/Uses --> ESMEmbedder

    DBSCANClusterMSA_cluster -- Calls/Uses --> MSA_encode_onehot
    DBSCANClusterMSA_cluster -- Calls/Uses --> MSA_encode_MSAtransformer

    HierarchicalClusterMSA_cluster -- Calls/Uses --> MSA_encode_onehot
```

-   **Core Modules:**
    -   `msa_pipeline.py`: Defines the central `MSA` class for data loading, preprocessing, encoding, and feature extraction.
    -   `dbscan_cluster.py`: Defines `DBSCANClusterMSA` for DBSCAN-based clustering of MSAs.
    -   `hierarchical_cluster.py`: Defines `HierarchicalClusterMSA` for hierarchical clustering of MSAs.
    -   `esm_embedding.py`: Defines `ESMEmbedder` for generating sequence embeddings using pre-trained ESM models.
    -   `msa_visualizer.py`: Defines `MSAVisualizer` for creating various visualizations from `MSA` objects.
    -   `msa_analyzer.py`: Defines `MSAAnalyzer` for analyzing clustering results, parsing logs, and generating statistical reports.
-   **Supporting Files & Directories:**
    -   `__init__.py`: Makes key classes (`MSA`, `MSAVisualizer`, `MSAAnalyzer`) easily accessible at the package level.
    -   `msa_pipeline.md`: Contains a detailed Mermaid diagram of the MSA processing pipeline and class interactions.
    -   `msa_visualizer.md`: Contains a detailed Mermaid diagram and documentation for the `MSAVisualizer` class and its workflows.
    -   `reference_code/`: Includes original AlphaFold 3 MSA-related code (`af3_msa.py`) and an MSA sampling script (`afsample2.py`) for reference.
    -   `example_code/`: Contains example Python scripts (`example_dbscan_cluster.py`, `example_hierarchical_cluster.py`, `example_msa_visualizer.py`, `simple_visualizer_test.py`, `example_msa_analyzer.py`) demonstrating how to use the various modules and their functionalities.

## Key Classes and Functionality

### `MSA` (in `msa_pipeline.py`)

The `MSA` class is the cornerstone of this package, serving as a container and processor for multiple sequence alignments.

-   **Purpose:** To load, manage, process, and featurize MSA data.
-   **Key Features:**
    -   **Flexible Loading:**
        -   From A3M strings or files: `MSA.from_a3m()`
        -   From multiple A3M strings: `MSA.from_multiple_a3ms()`
        -   From multiple `MSA` objects: `MSA.from_multiple_msas()`
        -   From FASTA, A3M, or Stockholm files: `MSA.from_file()`
        -   As an empty MSA with only a query: `MSA.from_empty()`
    -   **Data Processing:** Deduplication of sequences, conversion to A3M format (`to_a3m()`).
    -   **Sequence Encoding:**
        -   `encode_sequences()`: Unified method for encoding.
        -   `_encode_onehot()`: Internal helper for one-hot encoding.
        -   `_encode_MSAtransformer()`: Internal helper using `ESMEmbedder` for ESM-based embeddings.
    -   **Feature Extraction:** `featurize()` method to convert MSA data into a numerical format suitable for machine learning models.
    -   **Conversion to Specialized MSA Types:**
        -   `to_dbscan_cluster_msa()`: Returns a `DBSCANClusterMSA` instance.
        -   `to_hierarchical_cluster_msa()`: Returns a `HierarchicalClusterMSA` instance.
    -   **Analysis and Visualization Access:**
        -   `get_visualizer()`: Returns an `MSAVisualizer` instance for this MSA.
        -   `get_analyzer()`: Returns an `MSAAnalyzer` instance for this MSA.

### `MSAAnalyzer` (in `msa_analyzer.py`)

This class provides comprehensive analysis of MSA clustering results with integrated visualization capabilities.

-   **Purpose:** To analyze clustering logs, compute statistics, and generate reports with visualization.
-   **Key Features:**
    -   **Log Analysis:**
        -   `parse_cluster_log()`: Parse individual clustering log files.
        -   `analyze_msa_clustering()`: Analyze multiple log files for parameter optimization results.
    -   **Cluster Size Analysis:**
        -   `analyze_a3m_cluster_sizes()`: Analyze cluster sizes from A3M files.
    -   **Integrated Visualization:**
        -   `plot_epsilon_vs_clusters()`: Visualize DBSCAN parameter optimization.
        -   `plot_cluster_size_distribution()`: Create cluster size distribution plots.
    -   **Comprehensive Reporting:**
        -   `generate_analysis_report()`: Generate complete analysis with all statistics and plots.
    -   **Flexible Usage:** Can be used with or without an MSA instance.
-   **Example Usage:**
    ```python
    from msa import MSA, MSAAnalyzer
    
    # Method 1: Use with MSA instance
    msa = MSA.from_file("input.a3m", "protein")
    analyzer = msa.get_analyzer()
    
    # Method 2: Use independently
    analyzer = MSAAnalyzer()
    results = analyzer.generate_analysis_report(
        target_dir="clustering_results",
        output_dir="analysis_output"
    )
    ```

### `DBSCANClusterMSA` (in `dbscan_cluster.py`)

This class extends `MSA` to implement DBSCAN (Density-Based Spatial Clustering of Applications with Noise).

-   **Purpose:** To cluster MSA sequences based on their similarity using the DBSCAN algorithm.
-   **Key Features:**
    -   Inherits all functionalities from the `MSA` class.
    -   `cluster()`:
        -   Performs sequence filtering (e.g., based on gap content).
        -   Encodes sequences using either one-hot or MSA Transformer embeddings (via `self.encode_sequences()`).
        -   Optimizes DBSCAN parameters (`eps`, `min_samples`).
        -   Executes clustering using cuML (if GPU available) or scikit-learn.
        -   Returns a `ClusteringResult` object containing clustered `DBSCANClusterMSA` objects, metadata, assignments, and best parameters.
        -   Optionally saves clustered MSAs as A3M files.

### `HierarchicalClusterMSA` (in `hierarchical_cluster.py`)

This class extends `MSA` to implement hierarchical clustering using Ward's method.

-   **Purpose:** To group MSA sequences based on a hierarchical clustering approach.
-   **Key Features:**
    -   Inherits all functionalities from the `MSA` class.
    -   `cluster()`:
        -   Performs sequence filtering.
        -   Encodes sequences (typically one-hot, via `self._encode_onehot()`).
        -   Calculates a pairwise distance matrix (`calculate_pairwise_distance()`).
        -   Performs Ward's hierarchical clustering.
        -   Determines an optimal number of clusters based on `min_cluster_size` (`_search_minsize_clustering()`).
        -   Returns a `ClusteringResult` object (similar structure to DBSCAN's).
        -   Optionally saves clustered MSAs (currently as A3M files, though termed `save_fasta_files`).

### `ESMEmbedder` (in `esm_embedding.py`)

Provides an interface to pre-trained ESM (Evolutionary Scale Modeling) models for generating sequence embeddings.

-   **Purpose:** To convert protein sequences into dense vector representations (embeddings).
-   **Key Features:**
    -   Loads specified ESM models (e.g., `esm_msa1b_t12_100M_UR50S`).
    -   `preprocess_msa()`: Prepares sequences for the ESM model (handles special characters, lowercase, padding).
    -   `embed_msa()`: Computes embeddings for a batch of sequences.
    -   Utilized internally by `MSA._encode_MSAtransformer()`.

### `MSAVisualizer` (in `msa_visualizer.py`)

Dedicated to creating visualizations from `MSA` objects.

-   **Purpose:** To generate visual representations of MSA data, such as conservation heatmaps and similarity matrices.
-   **Key Features:**
    -   Accessed via `msa_instance.get_visualizer()`.
    -   `visualize_conservation()`: Generates a PDF heatmap of sequence conservation.
    -   `visualize_similarity()`: Generates a PDF sequence similarity matrix (optionally clustered).
    -   `visualize_msa()`: A convenience method to generate all standard visualizations.
    -   Uses plotting functions from `utils.visualization.visualize_msa`.

## Examples

Example scripts demonstrating the usage of each module are available in the `example_code/` directory:
-   `example_msa_pipeline.py`: (Note: this file was not explicitly requested to be moved/renamed, assuming it exists or will be created as an example for msa_pipeline.py if needed)
-   `example_dbscan_cluster.py`: Demonstrates DBSCAN clustering
-   `example_hierarchical_cluster.py`: Demonstrates hierarchical clustering
-   `example_msa_visualizer.py`: Demonstrates visualization functionality
-   `simple_visualizer_test.py`: A simpler visualization example
-   `example_msa_analyzer.py`: Demonstrates analysis functionality

These examples showcase how to use the core functionalities of the package and can serve as starting points for your own scripts.

## Workflow and Usage

A typical workflow using this package might involve:

1.  **Load or Create MSA:**
    -   Use `MSA.from_file()` (for FASTA, A3M, Stockholm), `MSA.from_a3m()`, etc., to load existing MSA data.
    -   Alternatively, generate MSA data using external tools and then load it.

2.  **Optional: Cluster MSA:**
    -   Convert the `MSA` object to a specialized clustering type:
        -   `dbscan_msa = msa.to_dbscan_cluster_msa()`
        -   `hierarchical_msa = msa.to_hierarchical_cluster_msa()`
    -   Run the `cluster()` method on the specialized object:
        -   `dbscan_results = dbscan_msa.cluster(...)`
        -   `hierarchical_results = hierarchical_msa.cluster(...)`
    -   The results will contain `MSA` objects for each cluster.

3.  **Analyze Clustering Results:**
    -   Get an analyzer: `analyzer = msa.get_analyzer()` or create independently `analyzer = MSAAnalyzer()`
    -   Generate comprehensive reports: `analyzer.generate_analysis_report(...)`
    -   Parse specific log files: `analyzer.parse_cluster_log(...)`
    -   Analyze cluster sizes: `analyzer.analyze_a3m_cluster_sizes(...)`

4.  **Featurize or Encode:**
    -   Call `msa.featurize()` on the original `MSA` object or any clustered `MSA` object to get numerical features.
    -   Call `msa.encode_sequences()` for specific encoding methods like one-hot or MSA Transformer embeddings.

5.  **Optional: Visualize MSA:**
    -   Get a visualizer: `visualizer = msa.get_visualizer()` (can be called on original or clustered `MSA` objects).
    -   Generate plots: `visualizer.visualize_msa(...)` or individual visualization methods.
    -   For clustering analysis: Use `MSAAnalyzer` visualization methods for parameter optimization and cluster statistics.

6.  **Downstream Analysis:** Use the extracted features, encoded sequences, or clustered MSAs for further tasks, such as input to protein structure prediction models.

For more detailed diagrams and explanations of specific components, refer to:
-   `msa_pipeline.md`: For the core `MSA` class and its interactions.
-   `msa_visualizer.md`: For the `MSAVisualizer` class and its workflows.

## Reference Code

The `msa/reference_code/` directory contains:
-   `af3_msa.py`: A version of the `Msa` class from DeepMind's AlphaFold 3, which served as an inspiration for `msa_pipeline.MSA`.
-   `afsample2.py`: A script related to MSA sampling/perturbation, potentially for future implementation (note: `MSA.AFsample2()` is currently not implemented).
These are provided for comparison and to understand the design origins.

## Testing

Unit tests for each module are available in the `test_scripts/` directory:
-   `test_msa_pipeline.py`: Tests core MSA functionality
-   `test_dbscan_cluster.py`: Tests DBSCAN clustering
-   `test_hierarchical_cluster.py`: Tests hierarchical clustering
-   `test_msa_visualizer.py`: Tests visualization functionality
-   `test_msa_analyzer.py`: Tests analysis functionality and replaces the original `analyze_msa_clustering.py` script

The tests verify the core functionalities of the package and ensure compatibility across different usage patterns.
It's recommended to run these tests to ensure the package is working correctly in your environment.



