# MSAVisualizer 클래스 문서

## 개요

`MSAVisualizer`는 Multiple Sequence Alignment (MSA) 데이터를 시각화하기 위한 전용 클래스입니다. 단일 책임 원칙을 따라 MSA 데이터 처리와 시각화 기능을 분리하여 설계되었습니다.

## 클래스 구조

```mermaid
classDiagram
    class MSAVisualizer {
        -msa: MSA
        +__init__(msa_instance)
        +visualize_conservation(output_path, title)
        +visualize_similarity(output_path, method, cluster_sequences, title)
        +visualize_msa(output_dir, prefix, similarity_method, cluster_sequences)
        +from_file(file_path, chain_poly_type)$ MSAVisualizer, List[str], List[str]
        -_read_msa_file(msa_file)$ List[str], List[str], str
    }
    
    class MSA {
        +query_sequence: str
        +sequences: List[str]
        +descriptions: List[str]
        +chain_poly_type: str
        +get_visualizer() MSAVisualizer
    }
    
    class VisualizationUtils {
        +plot_conservation_heatmap()
        +plot_sequence_similarity()
    }
    
    MSAVisualizer --> MSA : contains
    MSAVisualizer --> VisualizationUtils : uses
```

## 워크플로우

### 1. 기본 워크플로우 (MSA 객체 사용)

```mermaid
flowchart TD
    A[MSA 객체] --> B[msa.get_visualizer()]
    B --> C[MSAVisualizer 인스턴스]
    C --> D{시각화 유형 선택}
    D -->|보존성| E[visualize_conservation()]
    D -->|유사도| F[visualize_similarity()]
    D -->|통합| G[visualize_msa()]
    E --> H[보존성 히트맵 PDF]
    F --> I[유사도 매트릭스 PDF]
    G --> J[모든 시각화 파일들]
```

### 2. 파일 기반 워크플로우

```mermaid
flowchart TD
    A[MSA 파일<br/>(.fasta, .a3m, .sto)] --> B[MSAVisualizer.from_file()]
    B --> C[파일 포맷 감지]
    C --> D{파일 타입}
    D -->|.a3m| E[A3M 파서]
    D -->|.sto| F[Stockholm 파서]
    D -->|기타| G[FASTA 파서]
    E --> H[시퀀스 추출]
    F --> H
    G --> H
    H --> I[MSA 객체 생성]
    I --> J[MSAVisualizer 인스턴스]
    J --> K[시각화 실행]
```

### 3. CLI 실행 워크플로우

```mermaid
flowchart TD
    A[python msa_visualizer.py] --> B[명령행 인자 파싱]
    B --> C[로깅 설정]
    C --> D[from_file() 호출]
    D --> E{파일 읽기 성공?}
    E -->|예| F[visualize_msa() 실행]
    E -->|아니오| G[에러 메시지 출력]
    F --> H[시각화 파일 생성]
    G --> I[프로그램 종료 (오류)]
    H --> J[프로그램 종료 (성공)]
```

## 메소드 상세 설명

### 1. `__init__(msa_instance: MSA)`
MSA 객체를 받아서 시각화 준비를 완료합니다.

```mermaid
sequenceDiagram
    participant User
    participant MSAVisualizer
    participant MSA
    
    User->>MSA: MSA 객체 생성
    User->>MSAVisualizer: __init__(msa_instance)
    MSAVisualizer->>MSA: self.msa = msa_instance
    MSAVisualizer-->>User: 시각화 준비 완료
```

### 2. `visualize_conservation(output_path, title)`
시퀀스 보존성을 히트맵으로 시각화합니다.

```mermaid
flowchart TD
    A[visualize_conservation 호출] --> B[출력 디렉토리 생성]
    B --> C[위치 라벨 생성<br/>(1-based indexing)]
    C --> D[기본 제목 설정]
    D --> E[plot_conservation_heatmap 호출]
    E --> F[보존성 히트맵 생성]
    F --> G[PDF 파일 저장]
    G --> H[로그 메시지 출력]
```

**생성되는 시각화:**
- 아미노산별 위치별 빈도 히트맵
- 위치별 보존 점수 그래프
- 참조 시퀀스 하이라이트

### 3. `visualize_similarity(output_path, method, cluster_sequences, title)`
시퀀스 간 유사도를 매트릭스로 시각화합니다.

```mermaid
flowchart TD
    A[visualize_similarity 호출] --> B[출력 디렉토리 생성]
    B --> C[유사도 계산 방법 설정<br/>(identity/blosum62)]
    C --> D[클러스터링 옵션 확인]
    D --> E[plot_sequence_similarity 호출]
    E --> F[유사도 매트릭스 생성]
    F --> G{클러스터링 활성화?}
    G -->|예| H[계층적 클러스터링 적용]
    G -->|아니오| I[원본 순서 유지]
    H --> J[PDF 파일 저장]
    I --> J
```

### 4. `visualize_msa(output_dir, prefix, similarity_method, cluster_sequences)`
모든 시각화를 한 번에 생성합니다.

```mermaid
flowchart TD
    A[visualize_msa 호출] --> B[출력 디렉토리 생성]
    B --> C[보존성 파일명 생성<br/>(prefix + conservation_heatmap.pdf)]
    C --> D[visualize_conservation 호출]
    D --> E[유사도 파일명 생성<br/>(prefix + sequence_similarity.pdf)]
    E --> F[visualize_similarity 호출]
    F --> G[완료 로그 메시지]
```

### 5. `from_file(file_path, chain_poly_type)` (클래스 메소드)
MSA 파일에서 직접 시각화 객체를 생성합니다.

```mermaid
flowchart TD
    A[from_file 클래스 메소드 호출] --> B[_read_msa_file 호출]
    B --> C[파일 확장자 확인]
    C --> D{파일 타입}
    D -->|.a3m| E[A3M 형식 파싱<br/>- 소문자 제거<br/>- 시퀀스 길이 정규화]
    D -->|.sto| F[Stockholm 형식<br/>BioPython 사용]
    D -->|기타| G[FASTA 형식<br/>BioPython 사용]
    E --> H[시퀀스 및 라벨 추출]
    F --> H
    G --> H
    H --> I[MSA 객체 생성]
    I --> J[MSAVisualizer 인스턴스 생성]
    J --> K[결과 반환<br/>(visualizer, sequences, labels)]
```

## 사용 예시

### Python 코드에서 사용

```python
from msa.msa_pipeline import MSA
from msa.msa_visualizer import MSAVisualizer

# 방법 1: MSA 객체를 통한 시각화
msa = MSA(query_sequence, 'protein', sequences, descriptions)
visualizer = msa.get_visualizer()
visualizer.visualize_msa(output_dir="results", prefix="my_")

# 방법 2: 파일에서 직접 시각화
visualizer, seqs, labels = MSAVisualizer.from_file("input.a3m")
visualizer.visualize_conservation("conservation.pdf")
```

### 명령행에서 사용

```bash
python msa/msa_visualizer.py \
    --msa_file input.a3m \
    --output_dir results \
    --prefix analysis_ \
    --similarity_method identity \
    --chain_poly_type protein
```

## 데이터 흐름

```mermaid
graph LR
    A[MSA 파일] --> B[파일 파서]
    B --> C[시퀀스 데이터]
    C --> D[MSA 객체]
    D --> E[MSAVisualizer]
    E --> F[시각화 함수들]
    F --> G[matplotlib/scipy]
    G --> H[PDF 시각화 파일]
    
    I[CLI 인자] --> J[argparse]
    J --> K[설정 값들]
    K --> E
```

## 의존성

```mermaid
graph TD
    A[MSAVisualizer] --> B[msa.msa_pipeline.MSA]
    A --> C[utils.visualization.visualize_msa]
    A --> D[Bio.AlignIO]
    A --> E[Bio.Align.MultipleSeqAlignment]
    
    C --> F[matplotlib.pyplot]
    C --> G[scipy.cluster.hierarchy]
    C --> H[scipy.spatial.distance]
    C --> I[numpy]
```

## 확장 가능성

이 클래스는 다음과 같은 방향으로 확장이 가능합니다:

1. **새로운 시각화 방법 추가**
   - 계통수 시각화
   - 3D 시각화
   - 대화형 시각화 (plotly)

2. **파일 포맷 지원 확대**
   - CLUSTAL 형식
   - PHYLIP 형식
   - 사용자 정의 형식

3. **성능 최적화**
   - 대용량 MSA 처리
   - 병렬 처리
   - 메모리 효율성 개선

## 설계 원칙

- **단일 책임 원칙**: 시각화 기능만 담당
- **개방-폐쇄 원칙**: 새로운 시각화 방법 추가에 열려있음
- **의존성 역전 원칙**: 추상화된 인터페이스에 의존
- **인터페이스 분리 원칙**: 필요한 기능만 노출 