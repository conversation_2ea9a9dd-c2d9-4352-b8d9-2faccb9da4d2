``` mermaid
graph TD
    subgraph MSA_Core [MSA Core Logic - msa_pipeline.py]
        direction LR
        MSA["<strong>MSA</strong><br/>- query_sequence<br/>- chain_poly_type<br/>- sequences<br/>- descriptions"]
        style MSA fill:#lightyellow,stroke:#333,stroke-width:2px

        subgraph MSA_Constructors [Class Methods - Constructors]
            direction TB
            MSA_from_a3m["from_a3m()"]
            MSA_from_multiple_a3ms["from_multiple_a3ms()"]
            MSA_from_multiple_msas["from_multiple_msas()"]
            MSA_from_empty["from_empty()"]
        end
        MSA -- Contains --> MSA_Constructors
        
        subgraph MSA_Main_Methods [Instance Methods]
            direction TB
            MSA_to_a3m["to_a3m()"]
            MSA_featurize["featurize()"]
            MSA_encode_sequences["encode_sequences(encoding_method)"]
            MSA_to_dbscan["to_dbscan_cluster_msa()"]
            MSA_to_hierarchical["to_hierarchical_cluster_msa()"]
        end
        MSA -- Contains --> MSA_Main_Methods

        subgraph MSA_Internal_Encoding [Internal Encoding Helpers]
             direction TB
             MSA_encode_onehot["_encode_onehot()"]
             MSA_encode_MSAtransformer["_encode_MSAtransformer()"]
        end
        MSA_encode_sequences --> MSA_encode_onehot
        MSA_encode_sequences --> MSA_encode_MSAtransformer
    end

    subgraph Clustering_Modules [Clustering Modules]
        direction TB
        DBSCANClusterMSA["<strong>DBSCANClusterMSA</strong> (dbscan_cluster.py)"]
        style DBSCANClusterMSA fill:#lightblue,stroke:#333,stroke-width:2px
        DBSCANClusterMSA_cluster["cluster(encoding_method)"]
        DBSCANClusterMSA -- Has method --> DBSCANClusterMSA_cluster

        HierarchicalClusterMSA["<strong>HierarchicalClusterMSA</strong> (hierarchical_cluster.py)"]
        style HierarchicalClusterMSA fill:#lightgreen,stroke:#333,stroke-width:2px
        HierarchicalClusterMSA_cluster["cluster()"]
        HierarchicalClusterMSA -- Has method --> HierarchicalClusterMSA_cluster
    end

    subgraph Embedding_Module [Embedding Module - esm_embedding.py]
        direction TB
        ESMEmbedder["<strong>ESMEmbedder</strong>"]
        style ESMEmbedder fill:#lightpink,stroke:#333,stroke-width:2px
        ESMEmbedder_embed_msa["embed_msa()"]
        ESMEmbedder -- Has method --> ESMEmbedder_embed_msa
    end
    
    subgraph Reference_Code [Reference Code - For Context]
        direction TB
        AF3_MSA["Original AlphaFold 3 Msa Class (reference_code/af3_msa.py)"]
        style AF3_MSA fill:#whitesmoke,stroke:#666,stroke-width:1px,stroke-dasharray: 5 5
        AFSample2["AFSample2 Script (reference_code/afsample2.py)"]
        style AFSample2 fill:#whitesmoke,stroke:#666,stroke-width:1px,stroke-dasharray: 5 5
    end

    subgraph Test_Scripts [Test Scripts]
        direction TB
        TestMSAPipeline["test_msa_pipeline.py"]
        style TestMSAPipeline fill:#f0f0f0,stroke:#333,stroke-width:1px
        TestDBSCAN["test_dbscan_cluster.py"]
        style TestDBSCAN fill:#f0f0f0,stroke:#333,stroke-width:1px
        TestHierarchical["test_hierarchical_cluster.py"]
        style TestHierarchical fill:#f0f0f0,stroke:#333,stroke-width:1px
    end

    %% Relationships
    DBSCANClusterMSA -- Inherits from --> MSA
    HierarchicalClusterMSA -- Inherits from --> MSA

    MSA_to_dbscan -.-> DBSCANClusterMSA;
    MSA_to_hierarchical -.-> HierarchicalClusterMSA;

    MSA_encode_MSAtransformer -- Calls/Uses --> ESMEmbedder

    DBSCANClusterMSA_cluster -- Calls/Uses --> MSA_encode_onehot
    DBSCANClusterMSA_cluster -- Calls/Uses --> MSA_encode_MSAtransformer

    HierarchicalClusterMSA_cluster -- Calls/Uses --> MSA_encode_onehot

    MSA -- Inspired by/Based on --> AF3_MSA

    TestMSAPipeline -- Tests --> MSA
    TestMSAPipeline -- Tests --> ESMEmbedder
    TestDBSCAN -- Tests --> DBSCANClusterMSA
    TestHierarchical -- Tests --> HierarchicalClusterMSA
```