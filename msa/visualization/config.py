"""
Visualization configuration and styling settings.

This module centralizes all visualization configuration including
plot styles, color schemes, and layout settings.
"""

import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, Tuple, Optional
from dataclasses import dataclass, field
from enum import Enum

try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False


class ColorScheme(Enum):
    """Available color schemes for visualizations."""
    VIRIDIS = "viridis"
    PLASMA = "plasma"
    INFERNO = "inferno"
    CIVIDIS = "cividis"
    TAB10 = "tab10"
    SET3 = "Set3"


@dataclass 
class PlotStyle:
    """Configuration for plot styling."""
    figsize: Tuple[int, int] = (12, 8)
    dpi: int = 300
    style: str = "whitegrid"
    color_palette: str = "viridis"
    font_size: int = 12
    title_size: int = 14
    label_size: int = 12
    tick_size: int = 10


@dataclass
class VisualizationConfig:
    """
    Comprehensive visualization configuration.
    
    This class manages all visualization settings including
    plot styles, output formats, and quality parameters.
    """
    
    # Plot styling
    style: PlotStyle = field(default_factory=PlotStyle)
    
    # Output settings
    output_format: str = "pdf"
    save_kwargs: Dict[str, Any] = field(default=None)
    
    # Quality settings
    bbox_inches: str = "tight"
    transparent: bool = False
    
    # Animation settings (for future use)
    animation_fps: int = 10
    
    def __post_init__(self):
        """Initialize default save kwargs if not provided."""
        if self.save_kwargs is None:
            self.save_kwargs = {
                'dpi': self.style.dpi,
                'bbox_inches': self.bbox_inches,
                'transparent': self.transparent
            }
    
    def apply_style(self) -> None:
        """Apply the configured matplotlib/seaborn style."""
        # Set matplotlib parameters
        plt.rcParams.update({
            'font.size': self.style.font_size,
            'axes.titlesize': self.style.title_size,
            'axes.labelsize': self.style.label_size,
            'xtick.labelsize': self.style.tick_size,
            'ytick.labelsize': self.style.tick_size,
            'figure.figsize': self.style.figsize,
            'figure.dpi': self.style.dpi
        })
        
        # Apply seaborn style if available
        if SEABORN_AVAILABLE:
            sns.set_style(self.style.style)
            sns.set_palette(self.style.color_palette)
    
    def get_color_palette(self, n_colors: int) -> list:
        """
        Get color palette with specified number of colors.
        
        Parameters
        ----------
        n_colors : int
            Number of colors needed
            
        Returns
        -------
        list
            List of color values
        """
        if SEABORN_AVAILABLE:
            return sns.color_palette(self.style.color_palette, n_colors)
        else:
            # Fallback to matplotlib colormap
            cmap = plt.cm.get_cmap(self.style.color_palette)
            return [cmap(i / n_colors) for i in range(n_colors)]
    
    def save_figure(self, fig: plt.Figure, output_path: str) -> None:
        """
        Save figure with configured settings.
        
        Parameters
        ----------
        fig : plt.Figure
            Figure to save
        output_path : str
            Output file path
        """
        fig.savefig(output_path, **self.save_kwargs)
        plt.close(fig)


# Pre-defined configurations
class PresetConfigs:
    """Pre-defined visualization configurations for common use cases."""
    
    @staticmethod
    def publication() -> VisualizationConfig:
        """High-quality configuration for publications."""
        style = PlotStyle(
            figsize=(10, 8),
            dpi=600,
            style="white",
            color_palette="Set2",
            font_size=14,
            title_size=16,
            label_size=14,
            tick_size=12
        )
        return VisualizationConfig(
            style=style,
            output_format="pdf",
            transparent=True
        )
    
    @staticmethod
    def presentation() -> VisualizationConfig:
        """Configuration optimized for presentations."""
        style = PlotStyle(
            figsize=(12, 8),
            dpi=300,
            style="whitegrid",
            color_palette="Set1",
            font_size=16,
            title_size=20,
            label_size=16,
            tick_size=14
        )
        return VisualizationConfig(
            style=style,
            output_format="png"
        )
    
    @staticmethod
    def web() -> VisualizationConfig:
        """Configuration for web display."""
        style = PlotStyle(
            figsize=(10, 6),
            dpi=150,
            style="whitegrid",
            color_palette="viridis",
            font_size=12,
            title_size=14,
            label_size=12,
            tick_size=10
        )
        return VisualizationConfig(
            style=style,
            output_format="png",
            transparent=False
        )
    
    @staticmethod
    def quick() -> VisualizationConfig:
        """Fast, low-quality configuration for quick analysis."""
        style = PlotStyle(
            figsize=(8, 6),
            dpi=100,
            font_size=10,
            title_size=12,
            label_size=10,
            tick_size=8
        )
        return VisualizationConfig(
            style=style,
            output_format="png"
        )


# Default configuration
DEFAULT_CONFIG = VisualizationConfig()


def get_amino_acid_colors() -> Dict[str, str]:
    """Get standard amino acid color scheme."""
    return {
        'A': '#C8C8C8',  # Alanine - light gray
        'R': '#145AFF',  # Arginine - blue  
        'N': '#00DCDC',  # Asparagine - cyan
        'D': '#E60A0A',  # Aspartic acid - red
        'C': '#E6E600',  # Cysteine - yellow
        'Q': '#00DCDC',  # Glutamine - cyan
        'E': '#E60A0A',  # Glutamic acid - red
        'G': '#EBEBEB',  # Glycine - white
        'H': '#8282D2',  # Histidine - pale blue
        'I': '#0F820F',  # Isoleucine - green
        'L': '#0F820F',  # Leucine - green
        'K': '#145AFF',  # Lysine - blue
        'M': '#E6E600',  # Methionine - yellow
        'F': '#3232AA',  # Phenylalanine - dark blue
        'P': '#DC9682',  # Proline - salmon
        'S': '#FA9600',  # Serine - orange
        'T': '#FA9600',  # Threonine - orange
        'W': '#B45AB4',  # Tryptophan - purple
        'Y': '#3232AA',  # Tyrosine - dark blue
        'V': '#0F820F',  # Valine - green
        '-': '#FFFFFF',  # Gap - white
        '.': '#FFFFFF'   # Gap - white
    }


def get_conservation_colormap() -> str:
    """Get recommended colormap for conservation visualization."""
    return 'RdYlBu_r'  # Red-Yellow-Blue reversed


def setup_matplotlib_defaults() -> None:
    """Setup recommended matplotlib defaults for MSA visualization."""
    plt.rcParams.update({
        'figure.autolayout': True,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.1,
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
        'axes.spines.top': False,
        'axes.spines.right': False
    }) 