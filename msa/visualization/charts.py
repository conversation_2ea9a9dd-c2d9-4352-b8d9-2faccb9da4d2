"""
MSA statistical charts and distribution plots.

This module provides chart-based visualizations for MSA statistics
including distributions, comparisons, and quality metrics.
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Optional, Dict, Any, Tuple
from collections import Counter

from ..core.msa_core import MSACore
from ..analysis.statistics import MSAStatistics, MSAStats
from ..analysis.metrics import MSAMetrics, MSAQualityMetrics
from .config import VisualizationConfig, DEFAULT_CONFIG

try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False


class MSACharts:
    """
    Statistical charts and distribution plots for MSA analysis.
    
    This class provides chart-based visualizations including
    histograms, box plots, scatter plots, and statistical comparisons.
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        """
        Initialize the MSA charts generator.
        
        Parameters
        ----------
        config : Optional[VisualizationConfig]
            Visualization configuration. Uses default if None.
        """
        self.config = config or DEFAULT_CONFIG
        self.stats_analyzer = MSAStatistics()
        self.metrics_analyzer = MSAMetrics()
    
    def plot_length_distribution(self,
                                msa: MSACore,
                                output_path: str,
                                title: Optional[str] = None,
                                bins: int = 30) -> None:
        """
        Plot sequence length distribution.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        bins : int
            Number of histogram bins
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for length distribution")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Get sequence lengths
        lengths = [seq.length for seq in msa.sequences]
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.config.style.figsize)
        
        # Histogram
        ax1.hist(lengths, bins=bins, alpha=0.7, edgecolor='black', color='skyblue')
        ax1.set_xlabel('Sequence Length')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Length Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Box plot
        if SEABORN_AVAILABLE:
            sns.boxplot(y=lengths, ax=ax2)
        else:
            ax2.boxplot(lengths)
            ax2.set_ylabel('Sequence Length')
        
        ax2.set_title('Length Statistics')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics text
        stats_text = f'Mean: {np.mean(lengths):.1f}\n'
        stats_text += f'Median: {np.median(lengths):.1f}\n'
        stats_text += f'Std: {np.std(lengths):.1f}\n'
        stats_text += f'Range: {np.min(lengths)}-{np.max(lengths)}'
        
        ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # Set main title
        if title is None:
            title = f"Sequence Length Analysis ({len(msa)} sequences)"
        fig.suptitle(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Length distribution chart saved to: {output_path}")
    
    def plot_gap_content_distribution(self,
                                     msa: MSACore,
                                     output_path: str,
                                     title: Optional[str] = None) -> None:
        """
        Plot gap content distribution across sequences.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for gap content distribution")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate gap contents
        gap_contents = []
        for seq in msa.sequences:
            gap_count = seq.sequence.count('-') + seq.sequence.count('.')
            gap_content = gap_count / len(seq.sequence) if len(seq.sequence) > 0 else 0
            gap_contents.append(gap_content * 100)  # Convert to percentage
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.config.style.figsize)
        
        # Histogram
        ax1.hist(gap_contents, bins=20, alpha=0.7, edgecolor='black', color='lightcoral')
        ax1.set_xlabel('Gap Content (%)')
        ax1.set_ylabel('Number of Sequences')
        ax1.set_title('Gap Content Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Violin plot or box plot
        if SEABORN_AVAILABLE:
            sns.violinplot(y=gap_contents, ax=ax2)
        else:
            ax2.boxplot(gap_contents)
            ax2.set_ylabel('Gap Content (%)')
        
        ax2.set_title('Gap Content Statistics')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics
        stats_text = f'Mean: {np.mean(gap_contents):.1f}%\n'
        stats_text += f'Median: {np.median(gap_contents):.1f}%\n'
        stats_text += f'Max: {np.max(gap_contents):.1f}%'
        
        ax1.text(0.98, 0.98, stats_text, transform=ax1.transAxes,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # Set main title
        if title is None:
            title = f"Gap Content Analysis ({len(msa)} sequences)"
        fig.suptitle(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Gap content distribution chart saved to: {output_path}")
    
    def plot_amino_acid_composition(self,
                                   msa: MSACore,
                                   output_path: str,
                                   title: Optional[str] = None,
                                   top_n: int = 10) -> None:
        """
        Plot amino acid composition across the MSA.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        top_n : int
            Number of top amino acids to show
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for amino acid composition")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate amino acid frequencies
        aa_counts = Counter()
        total_residues = 0
        
        for seq in msa.sequences:
            for aa in seq.sequence.upper():
                if aa not in '-.':
                    aa_counts[aa] += 1
                    total_residues += 1
        
        if total_residues == 0:
            logging.warning("No amino acids found in MSA")
            return
        
        # Get most common amino acids
        most_common = aa_counts.most_common(top_n)
        amino_acids = [aa for aa, _ in most_common]
        frequencies = [count / total_residues * 100 for _, count in most_common]
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.config.style.figsize)
        
        # Bar chart
        colors = self.config.get_color_palette(len(amino_acids))
        bars = ax1.bar(amino_acids, frequencies, color=colors, alpha=0.7, edgecolor='black')
        ax1.set_xlabel('Amino Acid')
        ax1.set_ylabel('Frequency (%)')
        ax1.set_title(f'Top {top_n} Amino Acid Frequencies')
        ax1.grid(True, alpha=0.3)
        
        # Add frequency labels on bars
        for bar, freq in zip(bars, frequencies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{freq:.1f}%', ha='center', va='bottom')
        
        # Pie chart
        ax2.pie(frequencies, labels=amino_acids, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Amino Acid Composition')
        
        # Set main title
        if title is None:
            title = f"Amino Acid Composition Analysis ({total_residues:,} residues)"
        fig.suptitle(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Amino acid composition chart saved to: {output_path}")
    
    def plot_quality_metrics(self,
                            msa: MSACore,
                            output_path: str,
                            title: Optional[str] = None) -> None:
        """
        Plot comprehensive quality metrics.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for quality metrics")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate quality metrics
        quality_metrics = self.metrics_analyzer.calculate_comprehensive_metrics(msa)
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        
        # Position coverage
        ax1.plot(quality_metrics.position_coverage, color='blue', linewidth=1.0)
        ax1.fill_between(range(len(quality_metrics.position_coverage)), 
                        quality_metrics.position_coverage, alpha=0.3)
        ax1.set_xlabel('Position')
        ax1.set_ylabel('Coverage')
        ax1.set_title('Position Coverage')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1.05)
        
        # Conservation scores
        conservation_values = [score.conservation for score in quality_metrics.conservation_scores]
        ax2.plot(conservation_values, color='green', linewidth=1.0)
        ax2.fill_between(range(len(conservation_values)), conservation_values, alpha=0.3)
        ax2.set_xlabel('Position')
        ax2.set_ylabel('Conservation')
        ax2.set_title('Position Conservation')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1.05)
        
        # Identity distribution
        if quality_metrics.identity_distribution:
            ax3.hist(quality_metrics.identity_distribution, bins=20, alpha=0.7, 
                    edgecolor='black', color='orange')
            ax3.set_xlabel('Pairwise Identity')
            ax3.set_ylabel('Frequency')
            ax3.set_title('Identity Distribution')
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, 'No identity data', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Identity Distribution')
        
        # Quality score summary
        metrics_data = {
            'Coverage': quality_metrics.mean_coverage,
            'Conservation': quality_metrics.mean_conservation,
            'Identity': quality_metrics.mean_pairwise_identity,
            'Overall': quality_metrics.overall_quality_score
        }
        
        metrics_names = list(metrics_data.keys())
        metrics_values = list(metrics_data.values())
        colors = ['blue', 'green', 'orange', 'red']
        
        bars = ax4.bar(metrics_names, metrics_values, color=colors, alpha=0.7, edgecolor='black')
        ax4.set_ylabel('Score')
        ax4.set_title('Quality Metrics Summary')
        ax4.set_ylim(0, 1.0)
        ax4.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, metrics_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')
        
        # Set main title
        if title is None:
            title = f"MSA Quality Analysis (Score: {quality_metrics.overall_quality_score:.3f})"
        fig.suptitle(title, fontsize=16)
        
        plt.tight_layout()
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Quality metrics chart saved to: {output_path}")
    
    def plot_comparative_analysis(self,
                                msas: List[MSACore],
                                names: List[str],
                                output_path: str,
                                title: Optional[str] = None) -> None:
        """
        Plot comparative analysis between multiple MSAs.
        
        Parameters
        ----------
        msas : List[MSACore]
            List of MSAs to compare
        names : List[str]
            Names for each MSA
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        """
        if not msas or len(msas) != len(names):
            logging.warning("Invalid MSAs or names provided for comparative analysis")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate statistics for each MSA
        stats_data = []
        for msa in msas:
            stats = self.stats_analyzer.calculate_comprehensive_stats(msa)
            quality = self.metrics_analyzer.calculate_overall_quality_score(msa)
            stats_data.append({
                'n_sequences': stats.n_sequences,
                'mean_length': stats.mean_length,
                'mean_gap_content': stats.mean_gap_content * 100,  # Convert to percentage
                'shannon_diversity': stats.shannon_diversity,
                'quality_score': quality
            })
        
        # Create figure
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        # Metrics to plot
        metrics = [
            ('n_sequences', 'Number of Sequences', 'Sequences'),
            ('mean_length', 'Mean Length', 'Length'),
            ('mean_gap_content', 'Mean Gap Content', 'Gap Content (%)'),
            ('shannon_diversity', 'Shannon Diversity', 'Diversity'),
            ('quality_score', 'Quality Score', 'Quality Score')
        ]
        
        colors = self.config.get_color_palette(len(names))
        
        for i, (metric_key, metric_title, ylabel) in enumerate(metrics):
            ax = axes[i]
            values = [data[metric_key] for data in stats_data]
            
            bars = ax.bar(names, values, color=colors, alpha=0.7, edgecolor='black')
            ax.set_ylabel(ylabel)
            ax.set_title(metric_title)
            ax.grid(True, alpha=0.3)
            
            # Rotate x-axis labels if needed
            if len(max(names, key=len)) > 8:
                ax.tick_params(axis='x', rotation=45)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                       f'{value:.2f}', ha='center', va='bottom')
        
        # Remove empty subplot
        if len(metrics) < len(axes):
            fig.delaxes(axes[-1])
        
        # Set main title
        if title is None:
            title = f"Comparative MSA Analysis ({len(msas)} MSAs)"
        fig.suptitle(title, fontsize=16)
        
        plt.tight_layout()
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Comparative analysis chart saved to: {output_path}")
    
    def plot_position_wise_statistics(self,
                                     msa: MSACore,
                                     output_path: str,
                                     title: Optional[str] = None,
                                     window_size: int = 10) -> None:
        """
        Plot position-wise statistics with sliding window analysis.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        window_size : int
            Sliding window size for smoothing
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for position-wise statistics")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate position-wise metrics
        quality_metrics = self.metrics_analyzer.calculate_comprehensive_metrics(msa)
        
        positions = list(range(msa.max_length))
        coverage = quality_metrics.position_coverage
        conservation = [score.conservation for score in quality_metrics.conservation_scores]
        gap_fraction = [score.gap_fraction for score in quality_metrics.conservation_scores]
        
        # Apply sliding window smoothing
        def smooth_data(data, window):
            if len(data) < window:
                return data
            smoothed = []
            for i in range(len(data)):
                start = max(0, i - window // 2)
                end = min(len(data), i + window // 2 + 1)
                smoothed.append(np.mean(data[start:end]))
            return smoothed
        
        coverage_smooth = smooth_data(coverage, window_size)
        conservation_smooth = smooth_data(conservation, window_size)
        gap_fraction_smooth = smooth_data(gap_fraction, window_size)
        
        # Create figure
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10), sharex=True)
        
        # Coverage plot
        ax1.plot(positions, coverage, color='lightblue', alpha=0.5, label='Raw')
        ax1.plot(positions, coverage_smooth, color='blue', linewidth=2, label=f'Smoothed (w={window_size})')
        ax1.set_ylabel('Coverage')
        ax1.set_title('Position Coverage')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1.05)
        
        # Conservation plot
        ax2.plot(positions, conservation, color='lightgreen', alpha=0.5, label='Raw')
        ax2.plot(positions, conservation_smooth, color='green', linewidth=2, label=f'Smoothed (w={window_size})')
        ax2.set_ylabel('Conservation')
        ax2.set_title('Position Conservation')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 1.05)
        
        # Gap fraction plot
        ax3.plot(positions, gap_fraction, color='lightcoral', alpha=0.5, label='Raw')
        ax3.plot(positions, gap_fraction_smooth, color='red', linewidth=2, label=f'Smoothed (w={window_size})')
        ax3.set_xlabel('Position')
        ax3.set_ylabel('Gap Fraction')
        ax3.set_title('Position Gap Content')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(0, 1.05)
        
        # Set main title
        if title is None:
            title = f"Position-wise Statistics ({len(msa)} sequences, {msa.max_length} positions)"
        fig.suptitle(title, fontsize=14)
        
        plt.tight_layout()
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Position-wise statistics chart saved to: {output_path}")
    
    def plot_all_charts(self,
                       msa: MSACore,
                       output_dir: str,
                       prefix: str = "") -> None:
        """
        Create all statistical charts for an MSA.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Length distribution
        length_path = os.path.join(output_dir, f"{prefix}length_distribution.{self.config.output_format}")
        self.plot_length_distribution(msa, length_path)
        
        # Gap content distribution
        gap_path = os.path.join(output_dir, f"{prefix}gap_content.{self.config.output_format}")
        self.plot_gap_content_distribution(msa, gap_path)
        
        # Amino acid composition
        aa_path = os.path.join(output_dir, f"{prefix}amino_acid_composition.{self.config.output_format}")
        self.plot_amino_acid_composition(msa, aa_path)
        
        # Quality metrics
        quality_path = os.path.join(output_dir, f"{prefix}quality_metrics.{self.config.output_format}")
        self.plot_quality_metrics(msa, quality_path)
        
        # Position-wise statistics
        position_path = os.path.join(output_dir, f"{prefix}position_statistics.{self.config.output_format}")
        self.plot_position_wise_statistics(msa, position_path)
        
        logging.info(f"All MSA charts saved in: {output_dir}") 