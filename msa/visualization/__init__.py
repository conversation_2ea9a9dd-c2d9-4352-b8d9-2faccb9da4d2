"""
MSA visualization module.

This module provides comprehensive visualization tools for MSA analysis,
split into focused components for better maintainability and readability.
"""

from .plots import <PERSON><PERSON><PERSON>ter
from .charts import MSACharts
from .clustering import ClusteringVisualizer
from .config import VisualizationConfig, PresetConfigs, DEFAULT_CONFIG

# Import core types for convenience
from ..core.msa_core import MS<PERSON><PERSON>
from typing import Optional, Dict, Any, List, TYPE_CHECKING

if TYPE_CHECKING:
    from ..clustering.interfaces import ClusteringResult

__all__ = [
    'MSAPlotter',
    'MSACharts', 
    'ClusteringVisualizer',
    'VisualizationConfig',
    'PresetConfigs',
    'DEFAULT_CONFIG',
    'VisualizationManager',
    # Convenience functions
    'visualize_msa_basic',
    'visualize_msa_clustering',
    'visualize_msa_comprehensive',
    'create_all_visualizations'
]


class VisualizationManager:
    """
    Unified visualization manager for all MSA visualization needs.
    
    This class provides a single interface for all visualization functionality,
    integrating basic MSA plots, statistical charts, and clustering analysis.
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        """
        Initialize the visualization manager.
        
        Parameters
        ----------
        config : Optional[VisualizationConfig]
            Visualization configuration. Uses default if None.
        """
        self.config = config or DEFAULT_CONFIG
        self.plotter = MSAPlotter(self.config)
        self.charts = MSACharts(self.config)
        self.clustering_viz = ClusteringVisualizer(self.config)
    
    def visualize_basic_msa(self, 
                           msa: MSACore, 
                           output_dir: str, 
                           prefix: str = "") -> None:
        """
        Create basic MSA visualizations.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to visualize
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        """
        self.plotter.plot_all_basic(msa, output_dir, prefix)
    
    def visualize_msa_statistics(self, 
                                msa: MSACore, 
                                output_dir: str, 
                                prefix: str = "") -> None:
        """
        Create statistical analysis charts for MSA.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        """
        import os
        
        # Length distribution
        length_path = os.path.join(output_dir, f"{prefix}length_distribution.{self.config.output_format}")
        self.charts.plot_length_distribution(msa, length_path)
        
        # Gap content distribution
        gap_path = os.path.join(output_dir, f"{prefix}gap_content_distribution.{self.config.output_format}")
        self.charts.plot_gap_content_distribution(msa, gap_path)
        
        # Amino acid composition
        aa_path = os.path.join(output_dir, f"{prefix}amino_acid_composition.{self.config.output_format}")
        self.charts.plot_amino_acid_composition(msa, aa_path)
        
        # Quality metrics
        quality_path = os.path.join(output_dir, f"{prefix}quality_metrics.{self.config.output_format}")
        self.charts.plot_quality_metrics(msa, quality_path)
        
        # Position-wise statistics
        position_path = os.path.join(output_dir, f"{prefix}position_statistics.{self.config.output_format}")
        self.charts.plot_position_wise_statistics(msa, position_path)
    
    def visualize_clustering_analysis(self, 
                                    msa: MSACore, 
                                    clustering_result: 'ClusteringResult',
                                    output_dir: str,
                                    prefix: str = "",
                                    encoding_method: str = 'onehot',
                                    create_pca: bool = True,
                                    create_tsne: bool = True,
                                    create_comparison: bool = True) -> None:
        """
        Create comprehensive clustering visualizations.
        
        Parameters
        ----------
        msa : MSACore
            MSA data
        clustering_result : ClusteringResult
            Clustering result object
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        encoding_method : str
            Encoding method for dimensional reduction
        create_pca : bool
            Whether to create PCA plots
        create_tsne : bool
            Whether to create t-SNE plots
        create_comparison : bool
            Whether to create side-by-side comparison
        """
        self.clustering_viz.visualize_comprehensive_clustering(
            msa=msa,
            clustering_result=clustering_result,
            output_dir=output_dir,
            prefix=prefix,
            encoding_method=encoding_method,
            create_pca=create_pca,
            create_tsne=create_tsne,
            create_comparison=create_comparison
        )
    
    def visualize_comparative_analysis(self, 
                                     msas: List[MSACore], 
                                     names: List[str],
                                     output_dir: str,
                                     prefix: str = "") -> None:
        """
        Create comparative analysis between multiple MSAs.
        
        Parameters
        ----------
        msas : List[MSACore]
            List of MSAs to compare
        names : List[str]
            Names for each MSA
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        """
        import os
        
        comp_path = os.path.join(output_dir, f"{prefix}comparative_analysis.{self.config.output_format}")
        self.charts.plot_comparative_analysis(msas, names, comp_path)
    
    def create_complete_analysis(self, 
                               msa: MSACore,
                               output_dir: str,
                               prefix: str = "",
                               clustering_result: Optional['ClusteringResult'] = None,
                               encoding_method: str = 'onehot',
                               include_clustering: bool = True) -> None:
        """
        Create complete MSA analysis with all visualizations.
        
        This is the main convenience method that creates a comprehensive
        analysis suite including basic plots, statistics, and clustering.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to analyze
        output_dir : str
            Output directory for all visualizations
        prefix : str
            Filename prefix for all outputs
        clustering_result : Optional[ClusteringResult]
            Clustering result (if available)
        encoding_method : str
            Encoding method for clustering analysis
        include_clustering : bool
            Whether to include clustering analysis
        """
        import os
        import logging
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        logging.info(f"Creating complete MSA analysis in: {output_dir}")
        
        # 1. Basic MSA visualizations
        logging.info("  → Creating basic MSA visualizations...")
        self.visualize_basic_msa(msa, output_dir, prefix)
        
        # 2. Statistical analysis
        logging.info("  → Creating statistical analysis...")
        self.visualize_msa_statistics(msa, output_dir, prefix)
        
        # 3. Clustering analysis (if provided)
        if include_clustering and clustering_result is not None:
            logging.info("  → Creating clustering analysis...")
            self.visualize_clustering_analysis(
                msa, clustering_result, output_dir, prefix, encoding_method
            )
        
        logging.info(f"Complete MSA analysis finished in: {output_dir}")


# ============================================================================
# Convenience Functions
# ============================================================================

def visualize_msa_basic(msa: MSACore, 
                       output_dir: str, 
                       prefix: str = "",
                       config: Optional[VisualizationConfig] = None) -> None:
    """
    Create basic MSA visualizations using default settings.
    
    Parameters
    ----------
    msa : MSACore
        MSA data to visualize
    output_dir : str
        Output directory
    prefix : str
        Filename prefix
    config : Optional[VisualizationConfig]
        Visualization configuration
    """
    manager = VisualizationManager(config)
    manager.visualize_basic_msa(msa, output_dir, prefix)


def visualize_msa_clustering(msa: MSACore,
                           clustering_result: 'ClusteringResult',
                           output_dir: str,
                           prefix: str = "",
                           encoding_method: str = 'onehot',
                           config: Optional[VisualizationConfig] = None) -> None:
    """
    Create clustering visualizations using default settings.
    
    Parameters
    ----------
    msa : MSACore
        MSA data
    clustering_result : ClusteringResult
        Clustering result object
    output_dir : str
        Output directory
    prefix : str
        Filename prefix
    encoding_method : str
        Encoding method for dimensional reduction
    config : Optional[VisualizationConfig]
        Visualization configuration
    """
    manager = VisualizationManager(config)
    manager.visualize_clustering_analysis(msa, clustering_result, output_dir, 
                                        prefix, encoding_method)


def visualize_msa_comprehensive(msa: MSACore,
                              output_dir: str,
                              clustering_result: Optional['ClusteringResult'] = None,
                              prefix: str = "",
                              encoding_method: str = 'onehot',
                              config: Optional[VisualizationConfig] = None) -> None:
    """
    Create comprehensive MSA analysis with all visualizations.
    
    Parameters
    ----------
    msa : MSACore
        MSA data to analyze
    output_dir : str
        Output directory
    clustering_result : Optional[ClusteringResult]
        Clustering result (if available)
    prefix : str
        Filename prefix
    encoding_method : str
        Encoding method for clustering analysis
    config : Optional[VisualizationConfig]
        Visualization configuration
    """
    manager = VisualizationManager(config)
    manager.create_complete_analysis(msa, output_dir, prefix, clustering_result, encoding_method)


def create_all_visualizations(msa: MSACore,
                            output_dir: str,
                            clustering_result: Optional['ClusteringResult'] = None,
                            config_preset: str = 'default',
                            prefix: str = "",
                            encoding_method: str = 'onehot') -> None:
    """
    Create all possible visualizations with preset configurations.
    
    This is the highest-level convenience function that creates everything
    with sensible defaults and preset configurations.
    
    Parameters
    ----------
    msa : MSACore
        MSA data to analyze
    output_dir : str
        Output directory
    clustering_result : Optional[ClusteringResult]
        Clustering result (if available)
    config_preset : str
        Configuration preset ('default', 'publication', 'presentation', 'web', 'quick')
    prefix : str
        Filename prefix
    encoding_method : str
        Encoding method for clustering analysis
    """
    # Get configuration based on preset
    if config_preset == 'publication':
        config = PresetConfigs.publication()
    elif config_preset == 'presentation':
        config = PresetConfigs.presentation()
    elif config_preset == 'web':
        config = PresetConfigs.web()
    elif config_preset == 'quick':
        config = PresetConfigs.quick()
    else:
        config = DEFAULT_CONFIG
    
    # Create comprehensive analysis
    visualize_msa_comprehensive(msa, output_dir, clustering_result, prefix, encoding_method, config) 