"""
Clustering visualization functionality.

This module provides specialized visualizations for clustering results
including PCA/t-SNE plots, cluster distributions, and clustering metrics.
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Optional, Dict, Any, TYPE_CHECKING, Literal
from collections import Counter
from pathlib import Path

try:
    from sklearn.decomposition import PCA
    from sklearn.manifold import TSNE
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from ..core.msa_core import MSACore
from .config import VisualizationConfig, DEFAULT_CONFIG

# Type checking imports
if TYPE_CHECKING:
    from ..clustering.interfaces import ClusteringResult


class ClusteringVisualizer:
    """
    Specialized visualizer for clustering results.
    
    This class provides advanced clustering visualizations including
    dimensional reduction plots, cluster statistics, and quality assessment.
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        """
        Initialize the clustering visualizer.
        
        Parameters
        ----------
        config : Optional[VisualizationConfig]
            Visualization configuration. Uses default if None.
        """
        self.config = config or DEFAULT_CONFIG
    
    def plot_cluster_distribution(self,
                                 clustering_result: 'ClusteringResult',
                                 output_path: str,
                                 title: Optional[str] = None) -> None:
        """
        Create cluster size distribution visualization.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        """
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate cluster sizes
        cluster_sizes = []
        for cluster_id in range(clustering_result.n_clusters):
            cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
            cluster_sizes.append(len(cluster_sequences))
        
        if not cluster_sizes:
            logging.warning("No clusters to visualize distribution for")
            return
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.config.style.figsize)
        
        # Histogram
        ax1.hist(cluster_sizes, bins=min(20, len(cluster_sizes)), 
                alpha=0.7, edgecolor='black', color='skyblue')
        ax1.set_xlabel('Cluster Size (number of sequences)')
        ax1.set_ylabel('Number of Clusters')
        ax1.set_title('Cluster Size Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Box plot
        ax2.boxplot(cluster_sizes)
        ax2.set_ylabel('Cluster Size (number of sequences)')
        ax2.set_title('Cluster Size Statistics')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics text
        stats_text = f'Total clusters: {len(cluster_sizes)}\n'
        stats_text += f'Mean size: {np.mean(cluster_sizes):.1f}\n'
        stats_text += f'Median size: {np.median(cluster_sizes):.1f}\n'
        stats_text += f'Max size: {np.max(cluster_sizes)}\n'
        stats_text += f'Min size: {np.min(cluster_sizes)}'
        
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # Set main title
        if title is None:
            title = f"Cluster Distribution ({clustering_result.algorithm_name})"
        fig.suptitle(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Cluster distribution visualization saved to: {output_path}")
    
    def plot_clustering_pca(self,
                           msa: MSACore,
                           clustering_result: 'ClusteringResult',
                           output_path: str,
                           title: Optional[str] = None,
                           encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
                           show_query: bool = True,
                           max_clusters_display: int = 10) -> None:
        """
        Create PCA-based clustering visualization.
        
        Parameters
        ----------
        msa : MSACore
            MSA data
        clustering_result : ClusteringResult
            Clustering result object
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        encoding_method : Literal['onehot', 'MSAtransformer']
            Encoding method for sequences
        show_query : bool
            Whether to highlight query sequence
        max_clusters_display : int
            Maximum clusters to display with distinct colors
        """
        if not SKLEARN_AVAILABLE:
            logging.warning("scikit-learn not available. Cannot create PCA visualization.")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Prepare sequences for encoding (exclude query if it exists)
        sequences_for_encoding = msa.sequences[1:] if len(msa.sequences) > 1 else []
        
        if len(sequences_for_encoding) < 2:
            logging.warning("Not enough sequences for PCA visualization")
            return
        
        try:
            # Encode sequences
            max_seq_len = msa.max_length
            encoded_seqs = self._encode_sequences_for_clustering(
                sequences_for_encoding, max_seq_len, encoding_method
            )
            
            # Perform PCA
            pca_model = PCA()
            pca_embedding = pca_model.fit_transform(encoded_seqs)
            
            # Get cluster labels (excluding query sequence)
            cluster_labels = clustering_result.cluster_labels[1:] if len(clustering_result.cluster_labels) > 1 else []
            
            # Create PCA visualization
            self._plot_clustering_landscape(
                embedding=pca_embedding,
                cluster_labels=cluster_labels,
                output_path=output_path,
                plot_type='PCA',
                title=title,
                show_query=show_query,
                max_clusters_display=max_clusters_display,
                pca_model=pca_model,
                msa=msa,
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
            
            logging.info(f"PCA clustering visualization saved to: {output_path}")
            
        except Exception as e:
            logging.error(f"Failed to create PCA visualization: {e}")
            raise RuntimeError(f"PCA visualization failed: {e}") from e
    
    def plot_clustering_tsne(self,
                            msa: MSACore,
                            clustering_result: 'ClusteringResult',
                            output_path: str,
                            title: Optional[str] = None,
                            encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
                            show_query: bool = True,
                            max_clusters_display: int = 10,
                            perplexity: float = 30.0,
                            random_state: int = 42) -> None:
        """
        Create t-SNE-based clustering visualization.
        
        Parameters
        ----------
        msa : MSACore
            MSA data
        clustering_result : ClusteringResult
            Clustering result object
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        encoding_method : Literal['onehot', 'MSAtransformer']
            Encoding method for sequences
        show_query : bool
            Whether to highlight query sequence
        max_clusters_display : int
            Maximum clusters to display with distinct colors
        perplexity : float
            t-SNE perplexity parameter
        random_state : int
            Random state for reproducibility
        """
        if not SKLEARN_AVAILABLE:
            logging.warning("scikit-learn not available. Cannot create t-SNE visualization.")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # For t-SNE, include all sequences including query
        all_sequences = msa.sequences
        
        if len(all_sequences) < 2:
            logging.warning("Not enough sequences for t-SNE visualization")
            return
        
        try:
            # Encode all sequences
            max_seq_len = msa.max_length
            encoded_seqs = self._encode_sequences_for_clustering(
                all_sequences, max_seq_len, encoding_method
            )
            
            # Perform t-SNE
            tsne_model = TSNE(perplexity=perplexity, random_state=random_state)
            tsne_embedding = tsne_model.fit_transform(encoded_seqs)
            
            # Split embedding: query is first, others follow
            query_embedding = tsne_embedding[0:1] if msa.query_sequence else None
            sequences_embedding = tsne_embedding[1:] if query_embedding is not None else tsne_embedding
            
            # Get cluster labels (excluding query sequence)
            cluster_labels = clustering_result.cluster_labels[1:] if len(clustering_result.cluster_labels) > 1 else clustering_result.cluster_labels
            
            # Create t-SNE visualization
            self._plot_clustering_landscape(
                embedding=sequences_embedding,
                cluster_labels=cluster_labels,
                output_path=output_path,
                plot_type='t-SNE',
                title=title,
                show_query=show_query,
                max_clusters_display=max_clusters_display,
                query_embedding=query_embedding
            )
            
            logging.info(f"t-SNE clustering visualization saved to: {output_path}")
            
        except Exception as e:
            logging.error(f"Failed to create t-SNE visualization: {e}")
            raise RuntimeError(f"t-SNE visualization failed: {e}") from e
    
    def plot_cluster_comparison(self,
                               clustering_result: 'ClusteringResult',
                               output_path: str,
                               title: Optional[str] = None) -> None:
        """
        Create cluster comparison visualization.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        """
        if clustering_result.n_clusters < 2:
            logging.warning("At least 2 clusters required for comparison visualization")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Collect cluster information
        cluster_data = []
        for cluster_id in range(clustering_result.n_clusters):
            cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
            cluster_data.append({
                'id': cluster_id,
                'size': len(cluster_sequences),
                'sequences': cluster_sequences
            })
        
        # Sort clusters by size
        cluster_data.sort(key=lambda x: x['size'], reverse=True)
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # Bar chart of cluster sizes
        cluster_ids = [f"Cluster {data['id']}" for data in cluster_data]
        cluster_sizes = [data['size'] for data in cluster_data]
        
        colors = self.config.get_color_palette(len(cluster_ids))
        bars = ax1.bar(range(len(cluster_ids)), cluster_sizes, color=colors, alpha=0.7, edgecolor='black')
        
        ax1.set_xlabel('Clusters')
        ax1.set_ylabel('Number of Sequences')
        ax1.set_title('Cluster Size Comparison')
        ax1.set_xticks(range(len(cluster_ids)))
        ax1.set_xticklabels(cluster_ids, rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, size in zip(bars, cluster_sizes):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01*max(cluster_sizes),
                    f'{size}', ha='center', va='bottom')
        
        # Cumulative percentage plot
        total_sequences = sum(cluster_sizes)
        cumulative_pct = np.cumsum(cluster_sizes) / total_sequences * 100
        
        ax2.plot(range(len(cluster_ids)), cumulative_pct, 'bo-', linewidth=2, markersize=6)
        ax2.set_xlabel('Clusters (sorted by size)')
        ax2.set_ylabel('Cumulative Percentage (%)')
        ax2.set_title('Cumulative Sequence Coverage')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 105)
        
        # Add percentage labels
        for i, pct in enumerate(cumulative_pct):
            ax2.text(i, pct + 2, f'{pct:.1f}%', ha='center', va='bottom')
        
        # Set main title
        if title is None:
            title = f'Cluster Analysis - {clustering_result.algorithm_name}'
        fig.suptitle(title, fontsize=14)
        
        plt.tight_layout()
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Cluster comparison visualization saved to: {output_path}")
    
    def plot_cluster_summary(self,
                            clustering_result: 'ClusteringResult',
                            output_path: str,
                            title: Optional[str] = None) -> None:
        """
        Create clustering summary visualization.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        """
        # Apply configuration style
        self.config.apply_style()
        
        # Prepare summary data
        total_sequences = len(clustering_result.sequences)
        clustered_sequences = total_sequences - clustering_result.n_noise
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.config.style.figsize)
        
        # Pie chart: Clustered vs Noise
        labels = ['Clustered', 'Noise']
        sizes = [clustered_sequences, clustering_result.n_noise]
        colors = ['lightblue', 'lightcoral']
        
        wedges, texts, autotexts = ax1.pie(
            sizes, labels=labels, colors=colors, autopct='%1.1f%%',
            startangle=90, explode=(0.05, 0)
        )
        ax1.set_title('Clustering Overview')
        
        # Bar chart: Summary statistics
        categories = ['Total\nSequences', 'Clusters', 'Noise\nPoints', 'Largest\nCluster']
        
        # Calculate largest cluster size
        largest_cluster_size = 0
        if clustering_result.n_clusters > 0:
            cluster_sizes = []
            for cluster_id in range(clustering_result.n_clusters):
                cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
                cluster_sizes.append(len(cluster_sequences))
            largest_cluster_size = max(cluster_sizes) if cluster_sizes else 0
        
        values = [total_sequences, clustering_result.n_clusters,
                 clustering_result.n_noise, largest_cluster_size]
        
        colors_bar = ['skyblue', 'lightgreen', 'lightcoral', 'gold']
        bars = ax2.bar(categories, values, color=colors_bar, alpha=0.7, edgecolor='black')
        ax2.set_title('Clustering Statistics')
        ax2.set_ylabel('Count')
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01*max(values),
                    f'{value}', ha='center', va='bottom')
        
        # Set main title
        if title is None:
            title = f'Clustering Summary ({clustering_result.algorithm_name})'
        fig.suptitle(title, fontsize=14)
        
        plt.tight_layout()
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Cluster summary visualization saved to: {output_path}")
    
    def plot_dimensional_reduction_comparison(self,
                                            msa: MSACore,
                                            clustering_result: 'ClusteringResult',
                                            output_path: str,
                                            title: Optional[str] = None,
                                            encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
                                            show_query: bool = True,
                                            max_clusters_display: int = 10,
                                            perplexity: float = 30.0,
                                            random_state: int = 42) -> None:
        """
        Create side-by-side PCA and t-SNE comparison visualization.
        
        This method creates a comprehensive comparison between PCA and t-SNE
        dimensional reduction techniques on the same dataset.
        
        Parameters
        ----------
        msa : MSACore
            MSA data
        clustering_result : ClusteringResult
            Clustering result object
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        encoding_method : Literal['onehot', 'MSAtransformer']
            Encoding method for sequences
        show_query : bool
            Whether to highlight query sequence
        max_clusters_display : int
            Maximum clusters to display with distinct colors
        perplexity : float
            t-SNE perplexity parameter
        random_state : int
            Random state for reproducibility
        """
        if not SKLEARN_AVAILABLE:
            logging.warning("scikit-learn not available. Cannot create dimensional reduction comparison.")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        all_sequences = msa.sequences
        if len(all_sequences) < 2:
            logging.warning("Not enough sequences for dimensional reduction comparison")
            return
        
        try:
            # Encode all sequences
            max_seq_len = msa.max_length
            encoded_seqs = self._encode_sequences_for_clustering(
                all_sequences, max_seq_len, encoding_method
            )
            
            # Perform PCA
            pca_model = PCA()
            pca_embedding = pca_model.fit_transform(encoded_seqs)
            
            # Perform t-SNE
            tsne_model = TSNE(perplexity=perplexity, random_state=random_state)
            tsne_embedding = tsne_model.fit_transform(encoded_seqs)
            
            # Create side-by-side comparison
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
            
            # Get cluster labels
            cluster_labels = clustering_result.cluster_labels
            labels_array = np.array(cluster_labels)
            
            # Plot PCA
            self._plot_single_reduction(ax1, pca_embedding, labels_array, 'PCA', 
                                      max_clusters_display, show_query, msa)
            
            # Plot t-SNE
            self._plot_single_reduction(ax2, tsne_embedding, labels_array, 't-SNE', 
                                      max_clusters_display, show_query, msa)
            
            # Set main title
            if title is None:
                title = f"Dimensional Reduction Comparison ({clustering_result.algorithm_name})"
            fig.suptitle(title, fontsize=16)
            
            plt.tight_layout()
            
            # Save figure
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            self.config.save_figure(fig, output_path)
            logging.info(f"Dimensional reduction comparison saved to: {output_path}")
            
        except Exception as e:
            logging.error(f"Failed to create dimensional reduction comparison: {e}")
            raise RuntimeError(f"Dimensional reduction comparison failed: {e}") from e
    
    def visualize_comprehensive_clustering(self,
                                         msa: MSACore,
                                         clustering_result: 'ClusteringResult',
                                         output_dir: str,
                                         prefix: str = "",
                                         encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
                                         create_pca: bool = True,
                                         create_tsne: bool = True,
                                         create_comparison: bool = True,
                                         show_query: bool = True,
                                         max_clusters_display: int = 10,
                                         tsne_perplexity: float = 30.0,
                                         random_state: int = 42) -> None:
        """
        Create comprehensive clustering analysis with all visualization types.
        
        This is the main convenience method that creates all clustering visualizations
        in one call, providing a complete clustering analysis suite.
        
        Parameters
        ----------
        msa : MSACore
            MSA data
        clustering_result : ClusteringResult
            Clustering result object
        output_dir : str
            Output directory for all visualizations
        prefix : str
            Filename prefix for all outputs
        encoding_method : Literal['onehot', 'MSAtransformer']
            Encoding method for sequences
        create_pca : bool
            Whether to create individual PCA visualization
        create_tsne : bool
            Whether to create individual t-SNE visualization
        create_comparison : bool
            Whether to create side-by-side comparison
        show_query : bool
            Whether to highlight query sequence
        max_clusters_display : int
            Maximum clusters to display with distinct colors
        tsne_perplexity : float
            t-SNE perplexity parameter
        random_state : int
            Random state for reproducibility
        """
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        logging.info(f"Creating comprehensive clustering visualization with prefix '{prefix}'")
        
        try:
            # 1. Basic clustering statistics
            dist_path = os.path.join(output_dir, f"{prefix}cluster_distribution.{self.config.output_format}")
            self.plot_cluster_distribution(clustering_result, dist_path)
            
            # 2. Cluster comparison analysis
            comp_path = os.path.join(output_dir, f"{prefix}cluster_comparison.{self.config.output_format}")
            self.plot_cluster_comparison(clustering_result, comp_path)
            
            # 3. Cluster summary overview
            summary_path = os.path.join(output_dir, f"{prefix}cluster_summary.{self.config.output_format}")
            self.plot_cluster_summary(clustering_result, summary_path)
            
            # 4. Individual dimensional reduction plots
            if create_pca and SKLEARN_AVAILABLE:
                pca_path = os.path.join(output_dir, f"{prefix}clustering_PCA.{self.config.output_format}")
                self.plot_clustering_pca(msa, clustering_result, pca_path, 
                                       encoding_method=encoding_method,
                                       show_query=show_query,
                                       max_clusters_display=max_clusters_display)
            
            if create_tsne and SKLEARN_AVAILABLE:
                tsne_path = os.path.join(output_dir, f"{prefix}clustering_tSNE.{self.config.output_format}")
                self.plot_clustering_tsne(msa, clustering_result, tsne_path,
                                        encoding_method=encoding_method,
                                        show_query=show_query,
                                        max_clusters_display=max_clusters_display,
                                        perplexity=tsne_perplexity,
                                        random_state=random_state)
            
            # 5. Side-by-side comparison
            if create_comparison and SKLEARN_AVAILABLE:
                comparison_path = os.path.join(output_dir, f"{prefix}dimensional_reduction_comparison.{self.config.output_format}")
                self.plot_dimensional_reduction_comparison(msa, clustering_result, comparison_path,
                                                         encoding_method=encoding_method,
                                                         show_query=show_query,
                                                         max_clusters_display=max_clusters_display,
                                                         perplexity=tsne_perplexity,
                                                         random_state=random_state)
            
            logging.info(f"Comprehensive clustering visualization completed in: {output_dir}")
            
        except Exception as e:
            logging.error(f"Failed to create comprehensive clustering visualization: {e}")
            raise RuntimeError(f"Comprehensive clustering visualization failed: {e}") from e
    
    def _encode_sequences_for_clustering(self, 
                                       sequences: List, 
                                       max_seq_len: int, 
                                       encoding_method: Literal['onehot', 'MSAtransformer']) -> np.ndarray:
        """
        Encode sequences for dimensional reduction analysis.
        
        Parameters
        ----------
        sequences : List
            List of sequences to encode
        max_seq_len : int
            Maximum sequence length for padding
        encoding_method : Literal['onehot', 'MSAtransformer']
            Encoding method to use
        
        Returns
        -------
        np.ndarray
            Encoded sequence matrix
        """
        if encoding_method == 'onehot':
            return self._encode_onehot(sequences, max_seq_len)
        elif encoding_method == 'MSAtransformer':
            return self._encode_msa_transformer(sequences, max_seq_len)
        else:
            logging.warning(f"Unknown encoding method: {encoding_method}. Using one-hot encoding.")
            return self._encode_onehot(sequences, max_seq_len)
    
    def _encode_onehot(self, sequences: List, max_seq_len: int) -> np.ndarray:
        """One-hot encoding implementation."""
        amino_acids = 'ACDEFGHIKLMNPQRSTVWY-'
        encoded = []
        
        for seq in sequences:
            seq_encoded = []
            for pos in range(max_seq_len):
                if pos < len(seq.sequence):
                    aa = seq.sequence[pos].upper()
                    aa_vector = [0] * len(amino_acids)
                    if aa in amino_acids:
                        aa_vector[amino_acids.index(aa)] = 1
                    seq_encoded.extend(aa_vector)
                else:
                    # Padding
                    seq_encoded.extend([0] * len(amino_acids))
            encoded.append(seq_encoded)
        
        return np.array(encoded)
    
    def _encode_msa_transformer(self, sequences: List, max_seq_len: int) -> np.ndarray:
        """MSA Transformer encoding implementation (placeholder for now)."""
        # For now, use a simplified version. In a full implementation,
        # this would use the actual MSA Transformer model
        logging.warning("MSA Transformer encoding not fully implemented. Using simplified encoding.")
        
        # Simplified hash-based encoding as placeholder
        encoded = []
        for seq in sequences:
            # Create a more sophisticated encoding than simple hash
            seq_str = seq.sequence.upper()
            seq_encoded = []
            
            # Use k-mer based encoding (triplets)
            for i in range(0, min(len(seq_str), max_seq_len), 3):
                triplet = seq_str[i:i+3].ljust(3, '-')  # Pad if necessary
                # Simple hash of triplet
                triplet_hash = hash(triplet) % 100  # Normalize to 0-99
                seq_encoded.append(triplet_hash)
            
            # Pad or truncate to consistent length
            target_len = max_seq_len // 3
            if len(seq_encoded) < target_len:
                seq_encoded.extend([0] * (target_len - len(seq_encoded)))
            else:
                seq_encoded = seq_encoded[:target_len]
            
            encoded.append(seq_encoded)
        
        return np.array(encoded)
    
    def _plot_single_reduction(self, ax, embedding: np.ndarray, labels_array: np.ndarray, 
                             plot_type: str, max_clusters_display: int, show_query: bool, msa: MSACore) -> None:
        """Helper method to plot a single dimensional reduction result."""
        x_coords = embedding[:, 0]
        y_coords = embedding[:, 1]
        
        # Plot noise points
        noise_mask = labels_array == -1
        if np.any(noise_mask):
            ax.scatter(x_coords[noise_mask], y_coords[noise_mask],
                      color='lightgray', marker='x', label='unclustered', alpha=0.7)
        
        # Plot clusters
        clustered_mask = labels_array != -1
        if np.any(clustered_mask):
            unique_labels = np.unique(labels_array[clustered_mask])
            colors = self.config.get_color_palette(min(len(unique_labels), max_clusters_display))
            
            for i, cluster_id in enumerate(unique_labels[:max_clusters_display]):
                cluster_mask = labels_array == cluster_id
                color = colors[i] if i < len(colors) else 'black'
                label = f'Cluster {cluster_id}' if i < max_clusters_display else 'Other clusters'
                ax.scatter(x_coords[cluster_mask], y_coords[cluster_mask],
                         color=color, label=label, s=50, alpha=0.7)
        
        # Add query sequence if requested
        if show_query and msa.query_sequence:
            # Query is typically the first sequence
            query_x, query_y = x_coords[0], y_coords[0]
            ax.scatter(query_x, query_y, color='red', marker='*', s=150,
                      label='Query Sequence', edgecolors='black', linewidth=1)
        
        # Set labels and title
        if plot_type == 'PCA':
            ax.set_xlabel('PC 1')
            ax.set_ylabel('PC 2')
        else:  # t-SNE
            ax.set_xlabel('t-SNE 1')
            ax.set_ylabel('t-SNE 2')
        
        ax.set_title(f'{plot_type} Clustering View')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
        ax.grid(True, alpha=0.3)
    
    def _plot_clustering_landscape(self,
                                 embedding: np.ndarray,
                                 cluster_labels: List[int],
                                 output_path: str,
                                 plot_type: str,
                                 title: Optional[str] = None,
                                 show_query: bool = True,
                                 max_clusters_display: int = 10,
                                 query_embedding: Optional[np.ndarray] = None,
                                 pca_model: Optional[PCA] = None,
                                 msa: Optional[MSACore] = None,
                                 max_seq_len: Optional[int] = None,
                                 encoding_method: Optional[str] = None) -> None:
        """
        Create clustering landscape plot.
        
        This method creates the dimensional reduction visualization
        with cluster coloring and query sequence highlighting.
        """
        # Extract coordinates
        x_coords = embedding[:, 0]
        y_coords = embedding[:, 1]
        
        # Set up axis labels
        if plot_type.upper() == 'PCA':
            x_label, y_label = 'PC 1', 'PC 2'
        else:  # t-SNE
            x_label, y_label = 't-SNE 1', 't-SNE 2'
        
        # Set default title
        if title is None:
            title = f"MSA clustering with N={len(embedding)} seq. ({plot_type})"
        
        # Calculate plot limits with padding
        x_min, x_max = np.min(x_coords), np.max(x_coords)
        y_min, y_max = np.min(y_coords), np.max(y_coords)
        x_padding = (x_max - x_min) * 0.1 if x_max != x_min else 10
        y_padding = (y_max - y_min) * 0.1 if y_max != y_min else 10
        
        # Create plots: filtered and unfiltered
        for plot_suffix, filter_large_clusters in [('', True), ('_total_cluster', False)]:
            fig, ax = plt.subplots(figsize=self.config.style.figsize)
            
            # Convert cluster_labels to numpy array
            labels_array = np.array(cluster_labels)
            
            # Plot unclustered points (noise, label = -1)
            noise_mask = labels_array == -1
            if np.any(noise_mask):
                ax.scatter(x_coords[noise_mask], y_coords[noise_mask],
                          color='lightgray', marker='x', label='unclustered', alpha=0.7)
            
            if filter_large_clusters:
                # Filtered version: show only first max_clusters_display clusters distinctly
                large_cluster_mask = labels_array > max_clusters_display - 1
                if np.any(large_cluster_mask):
                    ax.scatter(x_coords[large_cluster_mask], y_coords[large_cluster_mask],
                              color='black', label='other clusters', alpha=0.7)
                
                # Show first max_clusters_display clusters with distinct colors
                display_mask = (labels_array >= 0) & (labels_array <= max_clusters_display - 1)
                if np.any(display_mask):
                    colors = self.config.get_color_palette(max_clusters_display)
                    for cluster_id in range(max_clusters_display):
                        cluster_mask = labels_array == cluster_id
                        if np.any(cluster_mask):
                            ax.scatter(x_coords[cluster_mask], y_coords[cluster_mask],
                                     color=colors[cluster_id], label=f'Cluster {cluster_id}',
                                     s=50, alpha=0.8)
            else:
                # Unfiltered version: show all clusters
                clustered_mask = labels_array != -1
                if np.any(clustered_mask):
                    unique_labels = np.unique(labels_array[clustered_mask])
                    colors = self.config.get_color_palette(len(unique_labels))
                    
                    for i, cluster_id in enumerate(unique_labels):
                        cluster_mask = labels_array == cluster_id
                        ax.scatter(x_coords[cluster_mask], y_coords[cluster_mask],
                                 color=colors[i], label=f'Cluster {cluster_id}',
                                 s=50, alpha=0.7)
            
            # Add query sequence if requested and available
            if show_query and query_embedding is not None:
                query_x, query_y = query_embedding[0, 0], query_embedding[0, 1]
                ax.scatter(query_x, query_y, color='red', marker='*', s=150,
                          label='Query Sequence', edgecolors='black', linewidth=1)
            elif show_query and pca_model is not None and msa is not None:
                # Transform query sequence for PCA
                if msa.query_sequence and max_seq_len and encoding_method:
                    query_encoded = self._encode_sequences_for_clustering(
                        [msa.query_sequence], max_seq_len, encoding_method
                    )
                    query_pca = pca_model.transform(query_encoded)
                    query_x, query_y = query_pca[0, 0], query_pca[0, 1]
                    ax.scatter(query_x, query_y, color='red', marker='*', s=150,
                              label='Query Sequence', edgecolors='black', linewidth=1)
            
            # Set labels and title
            ax.set_xlabel(x_label)
            ax.set_ylabel(y_label)
            ax.set_title(title)
            
            # Set axis limits
            ax.set_xlim(x_min - x_padding, x_max + x_padding)
            ax.set_ylim(y_min - y_padding, y_max + y_padding)
            
            # Add legend
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
            
            plt.tight_layout()
            
            # Generate output filename
            base_path = Path(output_path)
            final_output_path = base_path.parent / f"{base_path.stem}{plot_suffix}{base_path.suffix}"
            
            # Save figure
            self.config.save_figure(fig, str(final_output_path))
            logging.info(f"Created {plot_type} landscape plot: {final_output_path}")
    
    def plot_all_clustering(self,
                           msa: MSACore,
                           clustering_result: 'ClusteringResult',
                           output_dir: str,
                           prefix: str = "",
                           create_pca: bool = True,
                           create_tsne: bool = True) -> None:
        """
        Create all clustering visualizations.
        
        Parameters
        ----------
        msa : MSACore
            MSA data
        clustering_result : ClusteringResult
            Clustering result object
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        create_pca : bool
            Whether to create PCA plots
        create_tsne : bool
            Whether to create t-SNE plots
        """
        # Use the comprehensive method for better functionality
        self.visualize_comprehensive_clustering(
            msa=msa,
            clustering_result=clustering_result,
            output_dir=output_dir,
            prefix=prefix,
            create_pca=create_pca,
            create_tsne=create_tsne,
            create_comparison=True  # Always create comparison by default
        ) 