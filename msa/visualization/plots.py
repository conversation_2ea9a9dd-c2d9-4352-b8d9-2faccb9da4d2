"""
Basic MSA plotting functionality.

This module provides fundamental plotting capabilities for MSA analysis
including conservation heatmaps and similarity matrices.
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Optional, Tuple, Dict, Any
from collections import Counter
from scipy.cluster.hierarchy import linkage, dendrogram
from scipy.spatial.distance import squareform

from ..core.msa_core import MS<PERSON>ore
from ..core.sequence import Sequence
from .config import VisualizationConfig, DEFAULT_CONFIG, get_amino_acid_colors, get_conservation_colormap


class MSAPlotter:
    """
    Basic MSA plotting functionality.
    
    This class provides core plotting methods for MSA visualization
    including conservation analysis and sequence similarity matrices.
    """
    
    def __init__(self, config: Optional[VisualizationConfig] = None):
        """
        Initialize the MSA plotter.
        
        Parameters
        ----------
        config : Optional[VisualizationConfig]
            Visualization configuration. Uses default if None.
        """
        self.config = config or DEFAULT_CONFIG
        self.amino_acids = 'ACDEFGHIKLMNPQRSTVWY-'
        self.aa_to_idx = {aa: idx for idx, aa in enumerate(self.amino_acids)}
        
        # BLOSUM62 matrix for similarity scoring
        self._setup_blosum62()
    
    def _setup_blosum62(self) -> None:
        """Setup BLOSUM62 scoring matrix."""
        # Simplified BLOSUM62 for key amino acids
        self.blosum62 = {
            'A': {'A': 4, 'R': -1, 'N': -2, 'D': -2, 'C': 0, 'Q': -1, 'E': -1, 'G': 0, 'H': -2, 'I': -1,
                  'L': -1, 'K': -1, 'M': -1, 'F': -2, 'P': -1, 'S': 1, 'T': 0, 'W': -3, 'Y': -2, 'V': 0, '-': -4},
            'R': {'A': -1, 'R': 5, 'N': 0, 'D': -2, 'C': -3, 'Q': 1, 'E': 0, 'G': -2, 'H': 0, 'I': -3,
                  'L': -2, 'K': 2, 'M': -1, 'F': -3, 'P': -2, 'S': -1, 'T': -1, 'W': -3, 'Y': -2, 'V': -3, '-': -4},
            # Add other amino acids as needed...
            '-': {aa: -4 for aa in 'ACDEFGHIKLMNPQRSTVWY-'}
        }
        # Complete matrix initialization would be here in production
    
    def plot_conservation_heatmap(self, 
                                msa: MSACore,
                                output_path: str,
                                title: Optional[str] = None,
                                reference_seq: Optional[Sequence] = None) -> None:
        """
        Create conservation heatmap visualization.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to visualize
        output_path : str
            Output file path
        title : Optional[str]
            Plot title
        reference_seq : Optional[Sequence]
            Reference sequence for highlighting
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for conservation heatmap")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate conservation data
        conservation_data = self._calculate_conservation_matrix(msa)
        conservation_scores = self._calculate_conservation_scores(conservation_data)
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(
            2, 1, figsize=self.config.style.figsize,
            gridspec_kw={'height_ratios': [4, 1]},
            constrained_layout=True
        )
        
        # Plot frequency heatmap
        seq_length = msa.max_length
        im = ax1.imshow(
            conservation_data, 
            aspect='auto', 
            cmap=get_conservation_colormap(),
            extent=(-0.5, seq_length-0.5, len(self.amino_acids)-0.5, -0.5)
        )
        
        # Add colorbar
        plt.colorbar(im, ax=ax1, label='Frequency')
        
        # Set amino acid labels
        ax1.set_yticks(range(len(self.amino_acids)))
        ax1.set_yticklabels(list(self.amino_acids))
        
        # Highlight reference sequence if provided
        if reference_seq or msa.query_sequence:
            ref_seq = reference_seq or msa.query_sequence
            self._highlight_reference_sequence(ax1, ref_seq.sequence)
        
        # Plot conservation scores
        x_positions = np.arange(seq_length)
        ax2.plot(x_positions, conservation_scores, color='blue', linewidth=1.0)
        ax2.fill_between(x_positions, conservation_scores, alpha=0.3)
        ax2.set_ylim(0, 1.03)
        
        # Set axis limits and labels
        ax1.set_xlim(-0.5, seq_length - 0.5)
        ax2.set_xlim(-0.5, seq_length - 0.5)
        ax2.set_ylabel('Conservation')
        ax2.set_xlabel('Position')
        
        # Set title
        if title is None:
            title = f"Conservation Analysis ({len(msa)} sequences)"
        ax1.set_title(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Conservation heatmap saved to: {output_path}")
    
    def plot_similarity_matrix(self,
                              msa: MSACore,
                              output_path: str,
                              method: str = 'identity',
                              cluster_sequences: bool = True,
                              title: Optional[str] = None) -> None:
        """
        Create sequence similarity matrix visualization.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to visualize
        output_path : str
            Output file path
        method : str
            Similarity calculation method ('identity' or 'blosum62')
        cluster_sequences : bool
            Whether to cluster sequences by similarity
        title : Optional[str]
            Plot title
        """
        if len(msa) < 2:
            logging.warning("Need at least 2 sequences for similarity matrix")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Calculate similarity matrix
        similarity_matrix = self._calculate_similarity_matrix(msa.sequences, method)
        
        # Cluster sequences if requested
        sequence_order = list(range(len(msa)))
        if cluster_sequences and len(msa) > 2:
            sequence_order = self._cluster_sequences(similarity_matrix)
            similarity_matrix = similarity_matrix[np.ix_(sequence_order, sequence_order)]
        
        # Create figure
        fig, ax = plt.subplots(figsize=self.config.style.figsize)
        
        # Plot similarity matrix
        im = ax.imshow(similarity_matrix, cmap='viridis', aspect='auto')
        plt.colorbar(im, ax=ax, label=f'Similarity ({method})')
        
        # Set labels
        sequence_labels = [f"Seq_{i+1}" for i in sequence_order]
        ax.set_xticks(range(len(sequence_labels)))
        ax.set_yticks(range(len(sequence_labels)))
        ax.set_xticklabels(sequence_labels, rotation=45, ha='right')
        ax.set_yticklabels(sequence_labels)
        
        # Set title
        if title is None:
            cluster_text = "with clustering" if cluster_sequences else "without clustering"
            title = f"Sequence Similarity Matrix ({method}, {cluster_text})"
        ax.set_title(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Similarity matrix saved to: {output_path}")
    
    def plot_sequence_logos(self,
                           msa: MSACore,
                           output_path: str,
                           positions: Optional[List[int]] = None,
                           title: Optional[str] = None) -> None:
        """
        Create sequence logo visualization for specific positions.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to visualize
        output_path : str
            Output file path
        positions : Optional[List[int]]
            Specific positions to show (None for all)
        title : Optional[str]
            Plot title
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for sequence logos")
            return
        
        # Apply configuration style
        self.config.apply_style()
        
        # Select positions
        if positions is None:
            positions = list(range(min(20, msa.max_length)))  # Limit to first 20 positions
        
        # Calculate position frequency matrix
        position_matrix = self._calculate_conservation_matrix(msa)
        
        # Create figure
        fig, ax = plt.subplots(figsize=(max(8, len(positions) * 0.5), 6))
        
        # Plot sequence logo (simplified version)
        for i, pos in enumerate(positions):
            if pos >= msa.max_length:
                continue
                
            # Get frequencies for this position
            freqs = position_matrix[:, pos]
            
            # Sort by frequency
            sorted_indices = np.argsort(freqs)[::-1]
            
            # Plot top amino acids
            bottom = 0
            for j, aa_idx in enumerate(sorted_indices[:5]):  # Top 5 amino acids
                if freqs[aa_idx] > 0.01:  # Only show if frequency > 1%
                    height = freqs[aa_idx]
                    ax.bar(i, height, bottom=bottom, 
                          color=get_amino_acid_colors().get(self.amino_acids[aa_idx], 'gray'),
                          width=0.8)
                    
                    # Add amino acid label
                    if height > 0.05:  # Only label if significant
                        ax.text(i, bottom + height/2, self.amino_acids[aa_idx],
                               ha='center', va='center', fontweight='bold')
                    
                    bottom += height
        
        # Set labels and title
        ax.set_xlabel('Position')
        ax.set_ylabel('Frequency')
        ax.set_xlim(-0.5, len(positions) - 0.5)
        ax.set_ylim(0, 1.0)
        ax.set_xticks(range(len(positions)))
        ax.set_xticklabels([str(p+1) for p in positions])  # 1-based positioning
        
        if title is None:
            title = f"Sequence Logo (positions {positions[0]+1}-{positions[-1]+1})"
        ax.set_title(title)
        
        # Save figure
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        self.config.save_figure(fig, output_path)
        logging.info(f"Sequence logo saved to: {output_path}")
    
    def _calculate_conservation_matrix(self, msa: MSACore) -> np.ndarray:
        """Calculate position frequency matrix for conservation analysis."""
        seq_length = msa.max_length
        n_sequences = len(msa)
        
        # Initialize position frequency matrix
        position_matrix = np.zeros((len(self.amino_acids), seq_length))
                
        # Calculate frequencies
        for seq in msa.sequences:
            # TODO: gap 있거나 deletion 있는 경우 처리
            for pos, aa in enumerate(seq.sequence.upper()):
                if pos < seq_length and aa in self.aa_to_idx:
                    position_matrix[self.aa_to_idx[aa], pos] += 1
        
        # Convert to frequencies
        position_matrix = position_matrix / n_sequences
        
        return position_matrix
    
    def _calculate_conservation_scores(self, position_matrix: np.ndarray) -> np.ndarray:
        """Calculate conservation scores using Shannon entropy."""
        seq_length = position_matrix.shape[1]
        conservation_scores = np.zeros(seq_length)
        
        for i in range(seq_length):
            freqs = position_matrix[:, i]
            nonzero_freqs = freqs[freqs > 0]
            
            if len(nonzero_freqs) > 0:
                entropy = -np.sum(nonzero_freqs * np.log2(nonzero_freqs))
                max_entropy = np.log2(len(self.amino_acids))
                conservation_scores[i] = 1 - (entropy / max_entropy)
        
        return conservation_scores
    
    def _highlight_reference_sequence(self, ax: plt.Axes, reference_seq: str) -> None:
        """Highlight reference sequence positions on heatmap."""
        for pos, aa in enumerate(reference_seq.upper()):
            if aa in self.aa_to_idx:
                rect = plt.Rectangle(
                    (pos - 0.5, self.aa_to_idx[aa] - 0.5),
                    1, 1,
                    linewidth=1.5,
                    edgecolor='red',
                    facecolor='none'
                )
                ax.add_patch(rect)
    
    def _calculate_similarity_matrix(self, sequences: List[Sequence], method: str) -> np.ndarray:
        """Calculate pairwise similarity matrix."""
        n_seqs = len(sequences)
        similarity_matrix = np.zeros((n_seqs, n_seqs))
        
        for i in range(n_seqs):
            for j in range(i, n_seqs):
                if method == 'identity':
                    similarity = sequences[i].calculate_identity(sequences[j])
                elif method == 'blosum62':
                    similarity = self._calculate_blosum_similarity(sequences[i], sequences[j])
                else:
                    similarity = 0.0
                
                similarity_matrix[i, j] = similarity
                similarity_matrix[j, i] = similarity  # Symmetric matrix
        
        return similarity_matrix
    
    def _calculate_blosum_similarity(self, seq1: Sequence, seq2: Sequence) -> float:
        """Calculate BLOSUM62-based similarity (simplified implementation)."""
        # This is a simplified version - full implementation would use complete BLOSUM62
        score = 0
        length = min(len(seq1.sequence), len(seq2.sequence))
        
        for i in range(length):
            aa1 = seq1.sequence[i].upper()
            aa2 = seq2.sequence[i].upper()
            
            if aa1 in self.blosum62 and aa2 in self.blosum62[aa1]:
                score += self.blosum62[aa1][aa2]
        
        # Normalize by length
        return max(0, score / length) if length > 0 else 0.0
    
    def _cluster_sequences(self, similarity_matrix: np.ndarray) -> List[int]:
        """Cluster sequences based on similarity matrix."""
        # Convert similarity to distance
        distance_matrix = 1 - similarity_matrix
        
        # Ensure valid distance matrix
        np.fill_diagonal(distance_matrix, 0)
        distance_matrix = np.maximum(distance_matrix, 0)
        
        # Perform hierarchical clustering
        try:
            condensed_distances = squareform(distance_matrix)
            linkage_matrix = linkage(condensed_distances, method='average')
            
            # Get dendrogram order
            dendro = dendrogram(linkage_matrix, no_plot=True)
            sequence_order = dendro['leaves']
            
            return sequence_order
        except Exception as e:
            logging.warning(f"Clustering failed: {e}. Using original order.")
            return list(range(len(similarity_matrix)))
    
    def plot_all_basic(self,
                      msa: MSACore,
                      output_dir: str,
                      prefix: str = "") -> None:
        """
        Create all basic MSA visualizations.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to visualize
        output_dir : str
            Output directory
        prefix : str
            Filename prefix
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Conservation heatmap
        conservation_path = os.path.join(output_dir, f"{prefix}conservation_heatmap.{self.config.output_format}")
        self.plot_conservation_heatmap(msa, conservation_path)
        
        # Similarity matrix
        similarity_path = os.path.join(output_dir, f"{prefix}similarity_matrix.{self.config.output_format}")
        self.plot_similarity_matrix(msa, similarity_path)
        
        # Sequence logos (if not too many positions)
        if msa.max_length <= 50:
            logo_path = os.path.join(output_dir, f"{prefix}sequence_logo.{self.config.output_format}")
            self.plot_sequence_logos(msa, logo_path)
        
        logging.info(f"Basic MSA visualizations saved in: {output_dir}") 