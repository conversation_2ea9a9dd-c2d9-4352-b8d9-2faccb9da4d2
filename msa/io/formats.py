"""
Format definitions and detection utilities.

This module provides format enums and detection logic for MSA files.
"""

import os
import logging
from enum import Enum
from pathlib import Path
from typing import Optional, Union


class SupportedFormat(Enum):
    """Supported MSA file formats."""
    FASTA = "fasta"
    A3M = "a3m"
    STOCKHOLM = "stockholm"
    CLUSTAL = "clustal"
    PHYLIP = "phylip"
    AUTO = "auto"  # Auto-detect format


class FormatDetector:
    """
    Utility class for detecting MSA file formats.
    
    This class provides methods to automatically detect the format
    of MSA files based on file extensions and content analysis.
    """
    
    # File extension mappings
    EXTENSION_MAP = {
        '.fasta': SupportedFormat.FASTA,
        '.fa': SupportedFormat.FASTA,
        '.fas': SupportedFormat.FASTA,
        '.fsa': SupportedFormat.FASTA,
        '.a3m': SupportedFormat.A3M,
        '.sto': SupportedFormat.STOCKHOLM,
        '.stockholm': SupportedFormat.STOCKHOLM,
        '.aln': SupportedFormat.CLUSTAL,
        '.clustal': SupportedFormat.CLUSTAL,
        '.phy': SupportedFormat.PHYLIP,
        '.phylip': SupportedFormat.PHYLIP
    }
    
    @classmethod
    def detect_format(cls, file_path: Union[str, Path]) -> SupportedFormat:
        """
        Detect MSA file format based on extension and content.
        
        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the MSA file
            
        Returns
        -------
        SupportedFormat
            Detected format or FASTA as default
        """
        file_path = Path(file_path)
        
        # First try extension-based detection
        extension = file_path.suffix.lower()
        if extension in cls.EXTENSION_MAP:
            return cls.EXTENSION_MAP[extension]
        
        # Fallback to content-based detection
        try:
            detected_format = cls._detect_from_content(file_path)
            if detected_format:
                return detected_format
        except Exception as e:
            logging.warning(f"Failed to detect format from content: {e}")
        
        # Default to FASTA
        logging.info(f"Could not detect format for {file_path}, defaulting to FASTA")
        return SupportedFormat.FASTA
    
    @classmethod
    def _detect_from_content(cls, file_path: Path) -> Optional[SupportedFormat]:
        """
        Detect format by analyzing file content.
        
        Parameters
        ----------
        file_path : Path
            Path to the MSA file
            
        Returns
        -------
        Optional[SupportedFormat]
            Detected format or None if couldn't detect
        """
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                first_lines = []
                for i, line in enumerate(f):
                    first_lines.append(line.strip())
                    if i >= 10:  # Read first 10 lines for detection
                        break
            
            content = '\n'.join(first_lines)
            
            # Check for A3M format
            if content.startswith('#A3M#') or any('>' in line and any(c.islower() for c in line) for line in first_lines):
                return SupportedFormat.A3M
            
            # Check for Stockholm format
            if any(line.startswith('# STOCKHOLM') for line in first_lines):
                return SupportedFormat.STOCKHOLM
            
            # Check for Clustal format
            if any('CLUSTAL' in line.upper() for line in first_lines):
                return SupportedFormat.CLUSTAL
            
            # Check for PHYLIP format (starts with numbers)
            if first_lines and first_lines[0].strip().split()[0].isdigit():
                return SupportedFormat.PHYLIP
            
            # Check for FASTA format
            if any(line.startswith('>') for line in first_lines):
                return SupportedFormat.FASTA
                
        except Exception as e:
            logging.error(f"Error reading file for format detection: {e}")
            return None
        
        return None
    
    @classmethod
    def is_supported_format(cls, format_str: str) -> bool:
        """
        Check if a format string is supported.
        
        Parameters
        ----------
        format_str : str
            Format string to check
            
        Returns
        -------
        bool
            True if format is supported
        """
        try:
            SupportedFormat(format_str.lower())
            return True
        except ValueError:
            return False
    
    @classmethod
    def get_format_description(cls, format_enum: SupportedFormat) -> str:
        """
        Get human-readable description of format.
        
        Parameters
        ----------
        format_enum : SupportedFormat
            Format enum value
            
        Returns
        -------
        str
            Format description
        """
        descriptions = {
            SupportedFormat.FASTA: "FASTA format - Most common sequence format",
            SupportedFormat.A3M: "A3M format - HH-suite MSA format with insertions",
            SupportedFormat.STOCKHOLM: "Stockholm format - Pfam/Rfam alignment format",
            SupportedFormat.CLUSTAL: "Clustal format - ClustalW/ClustalX output",
            SupportedFormat.PHYLIP: "PHYLIP format - Phylogenetic analysis format",
            SupportedFormat.AUTO: "Auto-detect format based on file content"
        }
        return descriptions.get(format_enum, "Unknown format")
    
    @classmethod
    def get_supported_extensions(cls) -> list:
        """
        Get list of supported file extensions.
        
        Returns
        -------
        list
            List of supported file extensions
        """
        return list(cls.EXTENSION_MAP.keys())


def validate_format(format_input: Union[str, SupportedFormat]) -> SupportedFormat:
    """
    Validate and convert format input to SupportedFormat enum.
    
    Parameters
    ----------
    format_input : Union[str, SupportedFormat]
        Format as string or enum
        
    Returns
    -------
    SupportedFormat
        Validated format enum
        
    Raises
    ------
    ValueError
        If format is not supported
    """
    if isinstance(format_input, SupportedFormat):
        return format_input
    
    if isinstance(format_input, str):
        try:
            return SupportedFormat(format_input.lower())
        except ValueError:
            raise ValueError(f"Unsupported format: {format_input}. "
                           f"Supported formats: {[f.value for f in SupportedFormat]}")
    
    raise ValueError(f"Invalid format type: {type(format_input)}")


def get_default_extension(format_enum: SupportedFormat) -> str:
    """
    Get default file extension for a format.
    
    Parameters
    ----------
    format_enum : SupportedFormat
        Format enum
        
    Returns
    -------
    str
        Default file extension including dot
    """
    defaults = {
        SupportedFormat.FASTA: '.fasta',
        SupportedFormat.A3M: '.a3m',
        SupportedFormat.STOCKHOLM: '.sto',
        SupportedFormat.CLUSTAL: '.aln',
        SupportedFormat.PHYLIP: '.phy'
    }
    return defaults.get(format_enum, '.txt') 