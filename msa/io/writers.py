"""
MSA file writers for exporting data to various formats.

This module provides functionality to export MSA data to different
file formats with proper formatting and validation.
"""

import os
import logging
from pathlib import Path
from typing import List, Union, Optional

from .formats import SupportedFormat, validate_format, get_default_extension
from ..core.msa_core import MSACore


class MSAWriter:
    """
    MSA file writer supporting multiple output formats.
    
    This class provides methods to export MSA data to various
    file formats with consistent interfaces and proper formatting.
    """
    
    def write_file(self, 
                  msa: MSACore,
                  file_path: Union[str, Path],
                  format: Union[str, SupportedFormat] = SupportedFormat.FASTA,
                  overwrite: bool = False) -> None:
        """
        Write MSA to file in specified format.
        
        Parameters
        ----------
        msa : MSACore
            MSA data to write
        file_path : Union[str, Path]
            Output file path
        format : Union[str, SupportedFormat]
            Output format
        overwrite : bool
            Whether to overwrite existing files
            
        Raises
        ------
        ValueError
            If format is not supported or MSA is empty
        FileExistsError
            If file exists and overwrite is False
        """
        if not msa.sequences:
            raise ValueError("Cannot write empty MSA")
        
        file_path = Path(file_path)
        format_enum = validate_format(format)
        
        if format_enum == SupportedFormat.AUTO:
            raise ValueError("AUTO format not supported for writing. Please specify explicit format.")
        
        # Check if file exists
        if file_path.exists() and not overwrite:
            raise FileExistsError(f"File already exists: {file_path}. Use overwrite=True to replace.")
        
        # Ensure directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Write based on format
            if format_enum == SupportedFormat.FASTA:
                self._write_fasta(msa, file_path)
            elif format_enum == SupportedFormat.A3M:
                self._write_a3m(msa, file_path)
            elif format_enum == SupportedFormat.STOCKHOLM:
                self._write_stockholm(msa, file_path)
            elif format_enum == SupportedFormat.CLUSTAL:
                self._write_clustal(msa, file_path)
            elif format_enum == SupportedFormat.PHYLIP:
                self._write_phylip(msa, file_path)
            else:
                raise ValueError(f"Writing format {format_enum.value} is not yet implemented")
            
            logging.info(f"Successfully wrote MSA ({len(msa)} sequences) to {file_path}")
            
        except Exception as e:
            # Clean up partial file on error
            if file_path.exists():
                try:
                    file_path.unlink()
                except:
                    pass
            raise ValueError(f"Failed to write MSA file {file_path}: {str(e)}") from e
    
    def write_sequences(self, 
                       sequences: List[str],
                       descriptions: List[str],
                       file_path: Union[str, Path],
                       format: Union[str, SupportedFormat] = SupportedFormat.FASTA,
                       overwrite: bool = False) -> None:
        """
        Write sequences and descriptions to file.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences
        descriptions : List[str]
            List of sequence descriptions
        file_path : Union[str, Path]
            Output file path
        format : Union[str, SupportedFormat]
            Output format
        overwrite : bool
            Whether to overwrite existing files
        """
        if len(sequences) != len(descriptions):
            raise ValueError("Number of sequences and descriptions must match")
        
        if not sequences:
            raise ValueError("Cannot write empty sequence list")
        
        file_path = Path(file_path)
        format_enum = validate_format(format)
        
        # Create content based on format
        if format_enum == SupportedFormat.FASTA:
            content = self._format_fasta_content(sequences, descriptions)
        elif format_enum == SupportedFormat.A3M:
            content = self._format_a3m_content(sequences, descriptions)
        else:
            raise ValueError(f"Format {format_enum.value} not supported for raw sequence writing")
        
        # Check if file exists
        if file_path.exists() and not overwrite:
            raise FileExistsError(f"File already exists: {file_path}. Use overwrite=True to replace.")
        
        # Ensure directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logging.info(f"Successfully wrote {len(sequences)} sequences to {file_path}")
    
    def _write_fasta(self, msa: MSACore, file_path: Path) -> None:
        """Write MSA in FASTA format."""
        fasta_content = msa.to_fasta()
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(fasta_content)
    
    def _write_a3m(self, msa: MSACore, file_path: Path) -> None:
        """Write MSA in A3M format."""
        content_lines = ["#A3M#"]
        
        for seq in msa.sequences:
            content_lines.append(f">{seq.description}")
            content_lines.append(seq.sequence)
        
        content = '\n'.join(content_lines) + '\n'
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _write_stockholm(self, msa: MSACore, file_path: Path) -> None:
        """Write MSA in Stockholm format using BioPython."""
        try:
            from Bio import AlignIO
            from Bio.Align import MultipleSeqAlignment
            from Bio.Seq import Seq
            from Bio.SeqRecord import SeqRecord
        except ImportError:
            raise ImportError("BioPython is required to write Stockholm format files")
        
        # Create BioPython alignment object
        records = []
        for seq in msa.sequences:
            record = SeqRecord(
                Seq(seq.sequence),
                id=seq.description.split()[0] if seq.description else f"seq_{seq.index}",
                description=seq.description or ""
            )
            records.append(record)
        
        alignment = MultipleSeqAlignment(records)
        
        # Write to file
        AlignIO.write(alignment, file_path, "stockholm")
    
    def _write_clustal(self, msa: MSACore, file_path: Path) -> None:
        """Write MSA in Clustal format using BioPython."""
        try:
            from Bio import AlignIO
            from Bio.Align import MultipleSeqAlignment
            from Bio.Seq import Seq
            from Bio.SeqRecord import SeqRecord
        except ImportError:
            raise ImportError("BioPython is required to write Clustal format files")
        
        # Create BioPython alignment object
        records = []
        for seq in msa.sequences:
            record = SeqRecord(
                Seq(seq.sequence),
                id=seq.description.split()[0] if seq.description else f"seq_{seq.index}",
                description=seq.description or ""
            )
            records.append(record)
        
        alignment = MultipleSeqAlignment(records)
        
        # Write to file
        AlignIO.write(alignment, file_path, "clustal")
    
    def _write_phylip(self, msa: MSACore, file_path: Path) -> None:
        """Write MSA in PHYLIP format using BioPython."""
        try:
            from Bio import AlignIO
            from Bio.Align import MultipleSeqAlignment
            from Bio.Seq import Seq
            from Bio.SeqRecord import SeqRecord
        except ImportError:
            raise ImportError("BioPython is required to write PHYLIP format files")
        
        # Create BioPython alignment object
        records = []
        for seq in msa.sequences:
            # PHYLIP format has restrictions on sequence names
            seq_id = seq.description.split()[0] if seq.description else f"seq_{seq.index}"
            seq_id = seq_id[:10]  # PHYLIP name length limit
            
            record = SeqRecord(
                Seq(seq.sequence),
                id=seq_id,
                description=""
            )
            records.append(record)
        
        alignment = MultipleSeqAlignment(records)
        
        # Write to file
        AlignIO.write(alignment, file_path, "phylip")
    
    def _format_fasta_content(self, sequences: List[str], descriptions: List[str]) -> str:
        """Format sequences as FASTA content."""
        lines = []
        for desc, seq in zip(descriptions, sequences):
            lines.append(f">{desc}")
            lines.append(seq)
        return '\n'.join(lines) + '\n'
    
    def _format_a3m_content(self, sequences: List[str], descriptions: List[str]) -> str:
        """Format sequences as A3M content."""
        lines = ["#A3M#"]
        for desc, seq in zip(descriptions, sequences):
            lines.append(f">{desc}")
            lines.append(seq)
        return '\n'.join(lines) + '\n'
    
    def get_suggested_filename(self, 
                              base_name: str,
                              format: Union[str, SupportedFormat]) -> str:
        """
        Get suggested filename with appropriate extension.
        
        Parameters
        ----------
        base_name : str
            Base filename without extension
        format : Union[str, SupportedFormat]
            Output format
            
        Returns
        -------
        str
            Suggested filename with extension
        """
        format_enum = validate_format(format)
        extension = get_default_extension(format_enum)
        
        # Remove existing extension if present
        base_name = Path(base_name).stem
        
        return f"{base_name}{extension}"


# Convenience functions for backward compatibility
def write_msa_file(msa: MSACore,
                  file_path: Union[str, Path],
                  format: Union[str, SupportedFormat] = SupportedFormat.FASTA,
                  overwrite: bool = False) -> None:
    """
    Convenience function to write MSA to file.
    
    Parameters
    ----------
    msa : MSACore
        MSA data to write
    file_path : Union[str, Path]
        Output file path
    format : Union[str, SupportedFormat]
        Output format
    overwrite : bool
        Whether to overwrite existing files
    """
    writer = MSAWriter()
    writer.write_file(msa, file_path, format, overwrite)


def write_sequences_to_file(sequences: List[str],
                           descriptions: List[str],
                           file_path: Union[str, Path],
                           format: Union[str, SupportedFormat] = SupportedFormat.FASTA,
                           overwrite: bool = False) -> None:
    """
    Convenience function to write sequences to file.
    
    Parameters
    ----------
    sequences : List[str]
        List of sequences
    descriptions : List[str]
        List of sequence descriptions
    file_path : Union[str, Path]
        Output file path
    format : Union[str, SupportedFormat]
        Output format
    overwrite : bool
        Whether to overwrite existing files
    """
    writer = MSAWriter()
    writer.write_sequences(sequences, descriptions, file_path, format, overwrite) 