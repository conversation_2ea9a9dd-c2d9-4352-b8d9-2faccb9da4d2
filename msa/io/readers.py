"""
Unified MSA file readers.

This module combines and refactors the existing file reading functionality
into a clean, unified interface supporting multiple formats.
"""

import logging
from pathlib import Path
from typing import List, Tuple, Union, Optional

from .formats import SupportedFormat, FormatDetector, validate_format
from ..core.msa_core import MSACore
from ..core.data_structures import ChainPolyType


class MSAReader:
    """
    Unified MSA file reader supporting multiple formats.
    
    This class provides a single interface for reading MSA files
    in various formats, automatically detecting the format when needed.
    """
    
    def __init__(self):
        """Initialize the MSA reader."""
        self._format_detector = FormatDetector()
    
    def read_file(self, 
                 file_path: Union[str, Path],
                 format: Union[str, SupportedFormat] = SupportedFormat.AUTO,
                 chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN,
                 remove_lowercase: bool = False) -> MSACore:
        """
        Read MSA file and return MSACore object.
        
        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the MSA file
        format : Union[str, SupportedFormat]
            File format. If AUTO, will auto-detect.
        chain_poly_type : ChainPolyType
            Type of polymer chain
        remove_lowercase : bool
            Whether to remove lowercase letters (insertions) from sequences
            
        Returns
        -------
        MSACore
            MSACore object containing the sequences
            
        Raises
        ------
        FileNotFoundError
            If the specified file doesn't exist
        ValueError
            If the file format is not supported or parsing fails
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"MSA file not found: {file_path}")
        
        # Validate and convert format
        format_enum = validate_format(format)
        
        # Auto-detect format if needed
        if format_enum == SupportedFormat.AUTO:
            format_enum = self._format_detector.detect_format(file_path)
            logging.info(f"Auto-detected format: {format_enum.value}")
        
        try:
            # Read sequences and descriptions
            sequences, descriptions = self._read_sequences(file_path, format_enum, remove_lowercase)
            
            if not sequences:
                raise ValueError(f"No sequences found in file: {file_path}")
            
            # Create MSACore object using from_fasta (which handles sequence creation)
            fasta_content = self._convert_to_fasta_content(sequences, descriptions)
            msa = MSACore.from_fasta(fasta_content, chain_poly_type)
            
            # Add source file to metadata
            if msa._metadata:
                msa._metadata.source_file = str(file_path)
            
            logging.info(f"Successfully loaded MSA with {len(msa)} sequences from {file_path}")
            return msa
            
        except Exception as e:
            raise ValueError(f"Failed to parse MSA file {file_path}: {str(e)}") from e
    
    def read_sequences(self, 
                      file_path: Union[str, Path],
                      format: Union[str, SupportedFormat] = SupportedFormat.AUTO,
                      remove_lowercase: bool = False) -> Tuple[List[str], List[str]]:
        """
        Read MSA file and return raw sequences and descriptions.
        
        Parameters
        ----------
        file_path : Union[str, Path]
            Path to the MSA file
        format : Union[str, SupportedFormat]
            File format. If AUTO, will auto-detect.
        remove_lowercase : bool
            Whether to remove lowercase letters (insertions) from sequences
            
        Returns
        -------
        Tuple[List[str], List[str]]
            Tuple containing sequences and descriptions
        """
        file_path = Path(file_path)
        format_enum = validate_format(format)
        
        if format_enum == SupportedFormat.AUTO:
            format_enum = self._format_detector.detect_format(file_path)
        
        return self._read_sequences(file_path, format_enum, remove_lowercase)
    
    def _read_sequences(self, 
                       file_path: Path, 
                       format_enum: SupportedFormat,
                       remove_lowercase: bool = False) -> Tuple[List[str], List[str]]:
        """
        Internal method to read sequences based on format.
        
        Parameters
        ----------
        file_path : Path
            Path to the MSA file
        format_enum : SupportedFormat
            Detected or specified format
        remove_lowercase : bool
            Whether to remove lowercase letters (insertions) from sequences
            
        Returns
        -------
        Tuple[List[str], List[str]]
            Tuple containing sequences and descriptions
        """
        if format_enum == SupportedFormat.A3M:
            return self._read_a3m_file(file_path, remove_lowercase)
        elif format_enum == SupportedFormat.STOCKHOLM:
            return self._read_stockholm_file(file_path)
        elif format_enum == SupportedFormat.CLUSTAL:
            return self._read_clustal_file(file_path)
        elif format_enum == SupportedFormat.PHYLIP:
            return self._read_phylip_file(file_path)
        else:
            # Default to FASTA
            return self._read_fasta_file(file_path)
    
    def _read_a3m_file(self, file_path: Path, remove_lowercase: bool = False) -> Tuple[List[str], List[str]]:
        """
        Read A3M format file.
        
        A3M format contains sequences with potential lowercase insertions.
        By default, lowercase letters are preserved unless remove_lowercase=True.
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        sequences, descriptions = self._parse_a3m_content(content)
        
        if remove_lowercase:
            # Remove lowercase letters (insertions) from A3M format
            cleaned_sequences = []
            for seq in sequences:
                cleaned_seq = ''.join(c for c in seq if not c.islower())
                cleaned_sequences.append(cleaned_seq)
            return cleaned_sequences, descriptions
        else:
            # Preserve lowercase letters (insertions)
            return sequences, descriptions
    
    def _read_stockholm_file(self, file_path: Path) -> Tuple[List[str], List[str]]:
        """Read Stockholm format file using BioPython."""
        try:
            from Bio import AlignIO
        except ImportError:
            raise ImportError("BioPython is required to read Stockholm format files")
        
        sequences = []
        descriptions = []
        
        alignment = AlignIO.read(file_path, "stockholm")
        for record in alignment:
            sequences.append(str(record.seq))
            descriptions.append(record.description or record.id)
        
        return sequences, descriptions
    
    def _read_fasta_file(self, file_path: Path) -> Tuple[List[str], List[str]]:
        """Read FASTA format file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return self._parse_fasta_content(content)
    
    def _read_clustal_file(self, file_path: Path) -> Tuple[List[str], List[str]]:
        """Read Clustal format file using BioPython."""
        try:
            from Bio import AlignIO
        except ImportError:
            raise ImportError("BioPython is required to read Clustal format files")
        
        sequences = []
        descriptions = []
        
        alignment = AlignIO.read(file_path, "clustal")
        for record in alignment:
            sequences.append(str(record.seq))
            descriptions.append(record.description or record.id)
        
        return sequences, descriptions
    
    def _read_phylip_file(self, file_path: Path) -> Tuple[List[str], List[str]]:
        """Read PHYLIP format file using BioPython."""
        try:
            from Bio import AlignIO
        except ImportError:
            raise ImportError("BioPython is required to read PHYLIP format files")
        
        sequences = []
        descriptions = []
        
        alignment = AlignIO.read(file_path, "phylip")
        for record in alignment:
            sequences.append(str(record.seq))
            descriptions.append(record.description or record.id)
        
        return sequences, descriptions
    
    def _parse_a3m_content(self, a3m_string: str) -> Tuple[List[str], List[str]]:
        """
        Parse an A3M formatted string into sequences and descriptions.
        
        This method maintains compatibility with existing A3M parsing logic.
        """
        sequences = []
        descriptions = []
        current_sequence_lines = []

        lines = a3m_string.splitlines()

        # Handle potential #A3M# header
        if lines and lines[0].startswith("#A3M#"):
            lines.pop(0)  # Remove header line

        for line in lines:
            line = line.strip()
            if not line:
                continue
            if line.startswith(">"):
                if current_sequence_lines:  # If there's a sequence buffered
                    sequences.append("".join(current_sequence_lines))
                    current_sequence_lines = []
                descriptions.append(line[1:])  # Store description without '>'
            else:
                current_sequence_lines.append(line)

        # Append the last sequence
        if current_sequence_lines:
            sequences.append("".join(current_sequence_lines))

        # Handle potential mismatch in lengths
        if len(descriptions) != len(sequences):
            if len(descriptions) > len(sequences) and descriptions[-1] and not current_sequence_lines:
                descriptions = descriptions[:-1]  # Last description had no sequence

        return sequences, descriptions
    
    def _parse_fasta_content(self, fasta_content: str) -> Tuple[List[str], List[str]]:
        """Parse FASTA content into sequences and descriptions."""
        sequences = []
        descriptions = []
        current_seq = []
        current_desc = None
        
        for line in fasta_content.strip().split('\n'):
            line = line.strip()
            if line.startswith('>'):
                # Save previous sequence if exists
                if current_desc is not None and current_seq:
                    sequences.append(''.join(current_seq))
                    descriptions.append(current_desc)
                
                # Start new sequence
                current_desc = line[1:]
                current_seq = []
            else:
                current_seq.append(line)
        
        # Add last sequence
        if current_desc is not None and current_seq:
            sequences.append(''.join(current_seq))
            descriptions.append(current_desc)
        
        return sequences, descriptions
    
    def _convert_to_fasta_content(self, sequences: List[str], descriptions: List[str]) -> str:
        """Convert sequences and descriptions to FASTA format string."""
        fasta_lines = []
        for desc, seq in zip(descriptions, sequences):
            fasta_lines.append(f">{desc}")
            fasta_lines.append(seq)
        return '\n'.join(fasta_lines)
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported file formats.
        
        Returns
        -------
        List[str]
            List of supported format names
        """
        return [fmt.value for fmt in SupportedFormat if fmt != SupportedFormat.AUTO]


# Convenience functions for backward compatibility
def read_msa_file(file_path: Union[str, Path], 
                 format: Union[str, SupportedFormat] = SupportedFormat.AUTO,
                 chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN) -> MSACore:
    """
    Convenience function to read MSA file.
    
    Parameters
    ----------
    file_path : Union[str, Path]
        Path to the MSA file
    format : Union[str, SupportedFormat]
        File format. If AUTO, will auto-detect.
    chain_poly_type : ChainPolyType
        Type of polymer chain
        
    Returns
    -------
    MSACore
        MSACore object containing the sequences
    """
    reader = MSAReader()
    return reader.read_file(file_path, format, chain_poly_type)


def read_sequences_from_file(file_path: Union[str, Path],
                           format: Union[str, SupportedFormat] = SupportedFormat.AUTO) -> Tuple[List[str], List[str]]:
    """
    Convenience function to read sequences from file.
    
    Parameters
    ----------
    file_path : Union[str, Path]
        Path to the MSA file
    format : Union[str, SupportedFormat]
        File format. If AUTO, will auto-detect.
        
    Returns
    -------
    Tuple[List[str], List[str]]
        Tuple containing sequences and descriptions
    """
    reader = MSAReader()
    return reader.read_sequences(file_path, format) 