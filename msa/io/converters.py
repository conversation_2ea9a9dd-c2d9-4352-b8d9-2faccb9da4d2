"""
Format conversion utilities for MSA files.

This module provides functionality to convert MSA files between
different formats while preserving sequence data and metadata.
"""

import logging
from pathlib import Path
from typing import Union, Optional

from .formats import SupportedFormat, validate_format
from .readers import MSAReader
from .writers import MSAWriter
from ..core.msa_core import <PERSON><PERSON><PERSON>
from ..core.data_structures import ChainPolyType


class FormatConverter:
    """
    Utility class for converting MSA files between formats.
    
    This class provides methods to convert MSA files from one format
    to another while preserving sequence data and metadata.
    """
    
    def __init__(self):
        """Initialize the format converter."""
        self.reader = MSAReader()
        self.writer = MSAWriter()
    
    def convert_file(self,
                    input_path: Union[str, Path],
                    output_path: Union[str, Path],
                    input_format: Union[str, SupportedFormat] = SupportedFormat.AUTO,
                    output_format: Union[str, SupportedFormat] = SupportedFormat.FASTA,
                    chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN,
                    overwrite: bool = False) -> None:
        """
        Convert MSA file from one format to another.
        
        Parameters
        ----------
        input_path : Union[str, Path]
            Path to input MSA file
        output_path : Union[str, Path]
            Path to output MSA file
        input_format : Union[str, SupportedFormat]
            Input file format. If AUTO, will auto-detect.
        output_format : Union[str, SupportedFormat]
            Output file format
        chain_poly_type : ChainPolyType
            Type of polymer chain
        overwrite : bool
            Whether to overwrite existing output files
            
        Raises
        ------
        ValueError
            If formats are invalid or conversion fails
        FileNotFoundError
            If input file doesn't exist
        FileExistsError
            If output file exists and overwrite is False
        """
        input_path = Path(input_path)
        output_path = Path(output_path)
        
        # Validate formats
        input_format_enum = validate_format(input_format)
        output_format_enum = validate_format(output_format)
        
        if output_format_enum == SupportedFormat.AUTO:
            raise ValueError("AUTO format not supported for output. Please specify explicit format.")
        
        logging.info(f"Converting {input_path} from {input_format_enum.value} to {output_format_enum.value}")
        
        try:
            # Read input file
            msa = self.reader.read_file(input_path, input_format_enum, chain_poly_type)
            
            # Write output file
            self.writer.write_file(msa, output_path, output_format_enum, overwrite)
            
            logging.info(f"Successfully converted MSA ({len(msa)} sequences) from {input_path} to {output_path}")
            
        except Exception as e:
            raise ValueError(f"Failed to convert {input_path} to {output_path}: {str(e)}") from e
    
    def convert_batch(self,
                     input_dir: Union[str, Path],
                     output_dir: Union[str, Path],
                     input_pattern: str = "*.fasta",
                     input_format: Union[str, SupportedFormat] = SupportedFormat.AUTO,
                     output_format: Union[str, SupportedFormat] = SupportedFormat.A3M,
                     chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN,
                     overwrite: bool = False) -> int:
        """
        Convert multiple MSA files in batch.
        
        Parameters
        ----------
        input_dir : Union[str, Path]
            Directory containing input files
        output_dir : Union[str, Path]
            Directory for output files
        input_pattern : str
            File pattern to match (e.g., "*.fasta")
        input_format : Union[str, SupportedFormat]
            Input file format. If AUTO, will auto-detect.
        output_format : Union[str, SupportedFormat]
            Output file format
        chain_poly_type : ChainPolyType
            Type of polymer chain
        overwrite : bool
            Whether to overwrite existing output files
            
        Returns
        -------
        int
            Number of files successfully converted
            
        Raises
        ------
        ValueError
            If directories don't exist or formats are invalid
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        
        if not input_dir.exists():
            raise ValueError(f"Input directory doesn't exist: {input_dir}")
        
        # Create output directory if it doesn't exist
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Validate formats
        input_format_enum = validate_format(input_format)
        output_format_enum = validate_format(output_format)
        
        if output_format_enum == SupportedFormat.AUTO:
            raise ValueError("AUTO format not supported for output. Please specify explicit format.")
        
        # Find input files
        input_files = list(input_dir.glob(input_pattern))
        
        if not input_files:
            logging.warning(f"No files found matching pattern {input_pattern} in {input_dir}")
            return 0
        
        logging.info(f"Found {len(input_files)} files to convert")
        
        converted_count = 0
        failed_files = []
        
        # Convert each file
        for input_file in input_files:
            try:
                # Generate output filename
                output_filename = self.writer.get_suggested_filename(
                    input_file.stem, output_format_enum
                )
                output_file = output_dir / output_filename
                
                # Convert file
                self.convert_file(
                    input_file, output_file, 
                    input_format_enum, output_format_enum,
                    chain_poly_type, overwrite
                )
                
                converted_count += 1
                
            except Exception as e:
                logging.error(f"Failed to convert {input_file}: {e}")
                failed_files.append(str(input_file))
        
        # Log summary
        logging.info(f"Batch conversion complete: {converted_count}/{len(input_files)} files converted successfully")
        
        if failed_files:
            logging.warning(f"Failed to convert {len(failed_files)} files: {failed_files}")
        
        return converted_count
    
    def convert_msa_object(self,
                          msa: MSACore,
                          output_format: Union[str, SupportedFormat]) -> str:
        """
        Convert MSA object to string in specified format.
        
        Parameters
        ----------
        msa : MSACore
            MSA object to convert
        output_format : Union[str, SupportedFormat]
            Target format
            
        Returns
        -------
        str
            MSA data as string in specified format
            
        Raises
        ------
        ValueError
            If format is not supported
        """
        output_format_enum = validate_format(output_format)
        
        if output_format_enum == SupportedFormat.AUTO:
            raise ValueError("AUTO format not supported for output. Please specify explicit format.")
        
        if output_format_enum == SupportedFormat.FASTA:
            return msa.to_fasta()
        elif output_format_enum == SupportedFormat.A3M:
            return self._msa_to_a3m_string(msa)
        else:
            raise ValueError(f"String conversion for format {output_format_enum.value} is not yet implemented")
    
    def _msa_to_a3m_string(self, msa: MSACore) -> str:
        """Convert MSA object to A3M format string."""
        lines = ["#A3M#"]
        
        for seq in msa.sequences:
            lines.append(f">{seq.description}")
            lines.append(seq.sequence)
        
        return '\n'.join(lines) + '\n'
    
    def validate_conversion(self,
                           input_path: Union[str, Path],
                           output_path: Union[str, Path],
                           chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN) -> bool:
        """
        Validate that conversion was successful by comparing sequence content.
        
        Parameters
        ----------
        input_path : Union[str, Path]
            Original input file
        output_path : Union[str, Path]
            Converted output file
        chain_poly_type : ChainPolyType
            Type of polymer chain
            
        Returns
        -------
        bool
            True if conversion is valid (sequences match)
        """
        try:
            # Read both files
            original_msa = self.reader.read_file(input_path, SupportedFormat.AUTO, chain_poly_type)
            converted_msa = self.reader.read_file(output_path, SupportedFormat.AUTO, chain_poly_type)
            
            # Compare basic properties
            if len(original_msa) != len(converted_msa):
                logging.error(f"Sequence count mismatch: {len(original_msa)} vs {len(converted_msa)}")
                return False
            
            # Compare sequences (ignoring case differences)
            for i, (orig_seq, conv_seq) in enumerate(zip(original_msa.sequences, converted_msa.sequences)):
                if orig_seq.sequence.upper() != conv_seq.sequence.upper():
                    logging.error(f"Sequence {i} content mismatch")
                    return False
            
            return True
            
        except Exception as e:
            logging.error(f"Validation failed: {e}")
            return False
    
    def get_conversion_info(self,
                           input_format: Union[str, SupportedFormat],
                           output_format: Union[str, SupportedFormat]) -> dict:
        """
        Get information about a format conversion.
        
        Parameters
        ----------
        input_format : Union[str, SupportedFormat]
            Input format
        output_format : Union[str, SupportedFormat]
            Output format
            
        Returns
        -------
        dict
            Conversion information including compatibility and notes
        """
        input_enum = validate_format(input_format)
        output_enum = validate_format(output_format)
        
        # Define format compatibility and conversion notes
        conversion_info = {
            'input_format': input_enum.value,
            'output_format': output_enum.value,
            'compatible': True,
            'lossy': False,
            'notes': []
        }
        
        # Add format-specific conversion notes
        if input_enum == SupportedFormat.A3M and output_enum != SupportedFormat.A3M:
            conversion_info['notes'].append("Lowercase insertions will be removed")
            conversion_info['lossy'] = True
        
        if output_enum in [SupportedFormat.PHYLIP]:
            conversion_info['notes'].append("Sequence names may be truncated to 10 characters")
            conversion_info['lossy'] = True
        
        if input_enum == SupportedFormat.STOCKHOLM and output_enum == SupportedFormat.FASTA:
            conversion_info['notes'].append("Structural annotations will be lost")
            conversion_info['lossy'] = True
        
        return conversion_info


# Convenience functions
def convert_msa_file(input_path: Union[str, Path],
                    output_path: Union[str, Path],
                    input_format: Union[str, SupportedFormat] = SupportedFormat.AUTO,
                    output_format: Union[str, SupportedFormat] = SupportedFormat.FASTA,
                    chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN,
                    overwrite: bool = False) -> None:
    """
    Convenience function to convert MSA file format.
    
    Parameters
    ----------
    input_path : Union[str, Path]
        Path to input MSA file
    output_path : Union[str, Path]
        Path to output MSA file
    input_format : Union[str, SupportedFormat]
        Input file format. If AUTO, will auto-detect.
    output_format : Union[str, SupportedFormat]
        Output file format
    chain_poly_type : ChainPolyType
        Type of polymer chain
    overwrite : bool
        Whether to overwrite existing output files
    """
    converter = FormatConverter()
    converter.convert_file(input_path, output_path, input_format, output_format, chain_poly_type, overwrite) 