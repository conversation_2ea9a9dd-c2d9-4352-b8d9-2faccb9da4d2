#!/usr/bin/env python3
"""
MSAAnalyzer class for analyzing MSA clustering results.
Follows single responsibility principle - handles analysis logic with integrated visualization.
"""

import os, sys
import glob
import logging
import re
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional, Union

# Conditional seaborn import
import seaborn as sns

# Import logger configuration from external module
from logger_config import create_hierarchical_logger, MAIN_INFO, SUB_INFO

# Import MSA class
from msa_pipeline import MSA

# Import utility functions
from utils.os_utils import read_input


class MSAAnalyzer:
    """
    Analyzer class for Multiple Sequence Alignment (MSA) clustering results.
    
    This class focuses on analyzing clustering results, parsing logs,
    and generating statistical reports with integrated visualization.
    """
    
    def __init__(self, msa_instance: Optional[MSA] = None):
        """
        Initialize the MSAAnalyzer with an optional MSA instance.
        
        Parameters
        ----------
        msa_instance : Optional[MSA]
            An MSA object to analyze. If None, the analyzer can still
            work with external log files and data.
        """
        self.msa = msa_instance
        self.logger = None
    
    def setup_logger(self,
        log_dir: Optional[str] = None,
        log_filename: str = 'msa_analysis.log',
        console_level: int = MAIN_INFO
    ) -> None:
        """
        Set up hierarchical logger for analysis operations.
        
        Parameters
        ----------
        log_dir : Optional[str]
            Directory to save log files. If None, uses current directory.
        log_filename : str
            Name of the log file.
        console_level : int
            Console logging level.
        """
        self.logger = create_hierarchical_logger(
            target_dir=log_dir,
            filename=log_filename,
            console_level=console_level
        )
    
    def parse_cluster_log(self,
        file_path: str # Path to the log file
    ) -> pd.DataFrame:
        """
        Parse an MSA clustering log file to extract epsilon values and cluster counts.
        
        Parameters
        ----------
        file_path (str):
            Path to the log file.
            
        Returns
        -------
        pandas.DataFrame:
            DataFrame containing epsilon values and cluster counts.
        """
        data = []
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
            
        if self.logger:
            self.logger.sub_info(f"Parsing log file: {file_path}")
        
        # Skip lines until we find the header
        header_idx = None
        for i, line in enumerate(lines):
            if "eps" in line and "min_samples" in line and "n_clusters" in line:
                header_idx = i
                break
        
        if header_idx is None:
            if self.logger:
                self.logger.warning(f"Header not found in {file_path}")
            return pd.DataFrame()  # Return empty DataFrame if header not found
        
        # Parse data lines after the header
        for line in lines[header_idx+1:]:
            line = line.strip()
            if not line:  # Skip empty lines
                continue
            
            # Extract values using regex to handle variable whitespace
            match = re.match(r'(\d+\.\d+)\s+(\d+)\s+(\d+)\s+(\d+)', line)
            if match:
                epsilon = float(match.group(1))
                min_samples = int(match.group(2))
                n_clusters = int(match.group(3))
                n_not_clustered = int(match.group(4))
                
                chain_id = int(re.search(r'(\d+)', os.path.basename(file_path)).group(1))
                
                data.append({
                    'chain_id': chain_id,
                    'epsilon': epsilon,
                    'min_samples': min_samples,
                    'n_clusters': n_clusters,
                    'n_not_clustered': n_not_clustered
                })
        
        df = pd.DataFrame(data)
        if not df.empty and self.logger:
            self.logger.sub_info(f"Extracted {len(df)} data points from {file_path}")
        
        return df

    def analyze_msa_clustering(self,
        base_dir: str, # Base directory containing the log files
        log_pattern: str, # Pattern to match log files
        min_samples: int = 3 # Filter logs by min_samples value
    ) -> pd.DataFrame:
        """
        Analyze MSA clustering results from log files.
        
        Parameters
        ----------
        base_dir (str):
            Base directory containing the log files.
        log_pattern (str):
            Pattern to match log files.
        min_samples (int, optional):
            Filter logs by min_samples value.
            
        Returns
        -------
        pandas.DataFrame:
            DataFrame with combined data from all log files.
        """
        # Find all log files matching the pattern
        log_pattern_full = os.path.join(base_dir, log_pattern)
        
        if self.logger:
            self.logger.main_info(f"Searching for log files with pattern: {log_pattern_full}")
        
        log_files = glob.glob(log_pattern_full)
        
        if not log_files:
            if self.logger:
                self.logger.warning(f"No log files found matching pattern: {log_pattern_full}")
                self.logger.warning(f"Return empty DataFrame")
            return pd.DataFrame()
        
        if self.logger:
            self.logger.main_info(f"Found {len(log_files)} log files")
        
        # Parse each log file and combine the results
        all_data = []
        for log_file in log_files:        
            df = self.parse_cluster_log(log_file) # log file parsing
            if not df.empty:
                # Filter by min_samples if specified
                if min_samples is not None:
                    df = df[df['min_samples'] == min_samples]
                all_data.append(df)
            
        if not all_data:
            if self.logger:
                self.logger.warning("No valid data found in log files")
            return pd.DataFrame()
        
        combined_df = pd.concat(all_data, ignore_index=True)
        if self.logger:
            self.logger.main_info(f"Combined data contains {len(combined_df)} rows from {len(all_data)} log files")
        
        return combined_df

    def analyze_a3m_cluster_sizes(self,
        target_dir: str, # Target directory containing chain subdirectories
        chain_pattern: str = "chain*" # Pattern to match chain directories
    ) -> pd.DataFrame:
        """
        Analyze cluster sizes from .a3m files in chain subdirectories.
        
        Parameters
        ----------
        target_dir (str):
            Target directory containing chain subdirectories.
        chain_pattern (str, optional):
            Pattern to match chain directories.
        
        Returns
        -------
        pandas.DataFrame:
            DataFrame containing cluster sizes for each chain.
        """
        
        if self.logger:
            self.logger.main_info(f"Find all chain directories")
        
        chain_dirs = glob.glob(os.path.join(target_dir, chain_pattern))
        
        if not chain_dirs:
            if self.logger:
                self.logger.warning(f"No chain directories found matching pattern: {chain_pattern}")
            return pd.DataFrame()
        else:
            if self.logger:
                self.logger.sub_info(f"found {len(chain_dirs)} chain directories")
        
        all_data = []
        
        for chain_dir in chain_dirs:
            chain_id = os.path.basename(chain_dir)
            
            # Extract numeric chain ID if possible
            try:
                numeric_chain_id = int(re.search(r'(\d+)', chain_id).group(1))
            except (AttributeError, ValueError):
                numeric_chain_id = chain_id
            
            if self.logger:
                self.logger.sub_info(f"processing chain directory: {chain_id}")
            
            # Find all .a3m files in this chain directory
            a3m_files = glob.glob(os.path.join(chain_dir, "*.a3m"))
            a3m_files = [f for f in a3m_files if not any(x in f for x in ["U10", "U100"])]
            
            if not a3m_files:
                if self.logger:
                    self.logger.sub_info(f"No valid .a3m files found in {chain_id}")
                continue
            else:
                if self.logger:
                    self.logger.sub_info(f"found {len(a3m_files)} .a3m files in {chain_id}")
            
            # Process each a3m file to get cluster size
            for a3m_file in a3m_files:
                msa_name = os.path.basename(a3m_file).replace(".a3m", "")
                
                # Count number of sequences in a3m file (cluster size)
                msa_size = len([i for i in read_input(a3m_file) if ">" in i])

                all_data.append({
                    'chain_id': numeric_chain_id,
                    'msa_name': msa_name,
                    'msa_size': msa_size
                })
        
        df = pd.DataFrame(all_data)
        
        if df.empty:
            if self.logger:
                self.logger.warning("No cluster data extracted from .a3m files")
            return df
        
        if self.logger:
            self.logger.main_info(f"Extracted cluster sizes for {df['chain_id'].nunique()} chains, {len(df)} clusters total")
            chain_summary = df.groupby('chain_id').agg({
                'msa_size': ['count', 'mean', 'min', 'max',
                            lambda x: x.quantile(0.25),  # Q1 (25th percentile)
                            lambda x: x.quantile(0.75)   # Q3 (75th percentile)
                            ]
            })
            self.logger.sub_info(f"Cluster summary by chain:\n{chain_summary}")
        
        return df

    def plot_epsilon_vs_clusters(self,
        df: pd.DataFrame, # DataFrame containing epsilon values and cluster counts
        output_dir: str = None # Directory to save the output plots
    ) -> None:
        """
        Create scatter plots of epsilon values vs number of clusters.
        
        Parameters
        ----------
        df (pandas.DataFrame):
            DataFrame containing epsilon values and cluster counts.
        output_dir (str, optional):
            Directory to save the output plots.
            
        Returns
        -------
        None
        """
        if not SEABORN_AVAILABLE:
            if self.logger:
                self.logger.warning("Seaborn not available. Creating basic matplotlib plots.")
            
            plt.figure(figsize=(12, 8))
            
            # Basic matplotlib scatter plot without seaborn styling
            for chain_id in df['chain_id'].unique():
                chain_data = df[df['chain_id'] == chain_id]
                plt.scatter(chain_data['epsilon'], chain_data['n_clusters'], 
                           label=f'Chain {chain_id}', alpha=0.7, s=100)
            
            plt.title("DBSCAN Epsilon vs Number of Clusters", fontsize=16)
            plt.xlabel("Epsilon Value used in DBSCAN", fontsize=14)
            plt.ylabel("The # of Clusters", fontsize=14)
            plt.legend(title="Chain ID", bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
        else:
            sns.set(style="whitegrid")
            plt.figure(figsize=(12, 8))
            
            # Create scatter plot
            scatter = sns.scatterplot(
                data=df, 
                x="epsilon",  y="n_clusters",
                hue="chain_id", palette="viridis",
                alpha=0.7, s=100
            )
            
            plt.title("DBSCAN Epsilon vs Number of Clusters", fontsize=16)
            plt.xlabel("Epsilon Value used in DBSCAN", fontsize=14)
            plt.ylabel("The # of Clusters", fontsize=14)
            plt.legend(title="Chain ID", bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.tight_layout()
        
        plot_path = os.path.join(output_dir, "DBSCAN_epsilon_vs_clusters.png")
        plt.savefig(plot_path, dpi=300, bbox_inches="tight")
        
        if self.logger:
            self.logger.sub_info(f"saved to {plot_path}")

    def plot_cluster_size_distribution(self,
        df: pd.DataFrame, # DataFrame containing cluster sizes
        output_dir: str = None # Directory to save the output plots
    ) -> None:
        """
        Create plots of cluster size distributions using violin plots.
        
        Parameters
        ----------
        df (pandas.DataFrame):
            DataFrame containing cluster sizes.
        output_dir (str, optional):
            Directory to save the output plots.
            
        Returns
        -------
        None
        """
        if not SEABORN_AVAILABLE:
            if self.logger:
                self.logger.warning("Seaborn not available. Creating basic matplotlib plots.")
            
            plt.figure(figsize=(12, 8))
            
            # Basic matplotlib box plot
            chain_ids = df['chain_id'].unique()
            positions = range(len(chain_ids))
            
            data_by_chain = [df[df['chain_id'] == chain_id]['msa_size'].values for chain_id in chain_ids]
            
            bp = plt.boxplot(data_by_chain, positions=positions, labels=chain_ids, 
                           patch_artist=True, whis=1.5)
            
            # Add some color to the boxes
            colors = plt.cm.viridis(np.linspace(0, 1, len(chain_ids)))
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            
            plt.title("Cluster Size Distribution by Chain", fontsize=16)
            plt.xlabel("Chain ID", fontsize=14)
            plt.ylabel("Cluster Size (Number of Sequences)", fontsize=14)
            plt.yscale("log")  # Log scale for better visualization of size distribution
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
        else:
            # Set the style
            sns.set(style="whitegrid")
            
            # Plot of cluster sizes by chain
            plt.figure(figsize=(12, 8))
            
            violinplot = sns.violinplot(
                data=df, x="chain_id", y="msa_size",
                hue="chain_id", inner="quartile", 
                cut=0, linewidth=1.5, legend=False
            )
            sns.boxplot(
                x='chain_id', y='msa_size', data=df, width=0.3, linewidth=1.5, fliersize=2
            )
            sns.swarmplot(
                x='chain_id', y='msa_size', data=df, color='black', size=5
            )
            
            # Add labels and title
            plt.title("Cluster Size Distribution by Chain", fontsize=16)
            plt.xlabel("Chain ID", fontsize=14)
            plt.ylabel("Cluster Size (Number of Sequences)", fontsize=14)
            plt.yscale("log")  # Log scale for better visualization of size distribution
            plt.tight_layout()
        
        plot_path = os.path.join(output_dir, "cluster_size_distribution.png")
        plt.savefig(plot_path, dpi=300, bbox_inches="tight")
        
        if self.logger:
            self.logger.main_info(f"Violin plot of cluster size distribution saved to {plot_path}")

    def generate_analysis_report(self,
        target_dir: str, # Target directory containing clustering results
        output_dir: str, # Directory to save analysis results
        log_pattern: str = "Asec_clvg_TMD_*.log", # Pattern to match log files
        min_samples: int = 3, # Filter logs by min_samples value
        chain_pattern: str = "chain*" # Pattern to match chain directories
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive analysis report of MSA clustering results.
        
        Parameters
        ----------
        target_dir (str):
            Target directory containing clustering results.
        output_dir (str):
            Directory to save analysis results.
        log_pattern (str, optional):
            Pattern to match log files.
        min_samples (int, optional):
            Filter logs by min_samples value.
        chain_pattern (str, optional):
            Pattern to match chain directories.
            
        Returns
        -------
        Dict[str, Any]:
            Dictionary containing analysis results and statistics.
        """
        # Set up directories
        log_dir = os.path.join(target_dir, "MSA_JKHmmer")
        os.makedirs(output_dir, exist_ok=True)
        
        # Set up logger if not already done
        if self.logger is None:
            self.setup_logger(target_dir)
        
        if self.logger:
            self.logger.info("Starting MSA clustering analysis")
            self.logger.sub_info(f"Target directory: {target_dir}")
            self.logger.sub_info(f"Log directory: {log_dir}")
            self.logger.sub_info(f"Output directory: {output_dir}")
        
        results = {}
        
        # Analyze clustering logs
        if self.logger:
            self.logger.info("Analyzing clustering logs")
        
        cluster_logs = self.analyze_msa_clustering(
            log_dir, 
            log_pattern=log_pattern,
            min_samples=min_samples
        )
        
        if not cluster_logs.empty:
            results['cluster_logs'] = cluster_logs
            
            if self.logger:
                self.logger.main_info(f"Extracted data for {cluster_logs['chain_id'].nunique()} chains from clustering logs")
                self.logger.sub_info(f"Total data points: {len(cluster_logs)} across {cluster_logs['chain_id'].nunique()} chains")
                
                # Summary statistics
                epsilon_stats = cluster_logs.groupby('chain_id')['n_clusters'].agg(['mean', 'std', 'min', 'max']).reset_index()
                for _, row in epsilon_stats.iterrows():
                    self.logger.sub_info(
                        f"chain ID {row['chain_id']:.1f}: mean={row['mean']:.1f}, std={row['std']:.1f}, min={row['min']}, max={row['max']}"
                    )
            
            # Generate epsilon vs clusters plot
            if self.logger:
                self.logger.main_info("Generate plots for DBSCAN epsilon vs clusters")
            self.plot_epsilon_vs_clusters(cluster_logs, output_dir)
        else:
            if self.logger:
                self.logger.warning("No data extracted from logs. Skipping epsilon vs clusters plots.")
        
        # Analyze cluster sizes from .a3m files
        if self.logger:
            self.logger.info("Analyze cluster sizes from .a3m files")
        
        df_clusters = self.analyze_a3m_cluster_sizes(
            log_dir, 
            chain_pattern=chain_pattern
        )
        
        if not df_clusters.empty:
            results['cluster_sizes'] = df_clusters
            
            # Generate plots for cluster size analysis
            if self.logger:
                self.logger.main_info("Generate plots for cluster size distribution")
            self.plot_cluster_size_distribution(df_clusters, output_dir)
        else:
            if self.logger:
                self.logger.warning("No data extracted from .a3m files. Skipping cluster size plots.")
        
        if self.logger:
            self.logger.main_info("Analysis completed successfully")
        
        return results


def main():
    """
    Main function for command-line interface testing.
    This replaces the original analyze_msa_clustering.py functionality.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='Analyze MSA clustering results using MSAAnalyzer')
    
    parser.add_argument('--target_dir', type=str, default="",
                        help='Target directory containing chain subdirectories')
    parser.add_argument('--log_dir', type=str,
                        help='Directory containing clustering log files (defaults to target_dir/MSA_JKHmmer)')
    parser.add_argument('--output_dir', type=str,
                        help='Directory to save output files (defaults to target_dir/cluster_analysis)')
    parser.add_argument('--log_pattern', type=str, default="Asec_clvg_TMD_*.log",
                        help='Pattern to match log files (default: Asec_clvg_TMD_*.log)')
    parser.add_argument('--min_samples', type=int, default=3,
                        help='Filter logs by min_samples value (default: 3)')
    parser.add_argument('--chain_pattern', type=str, default="chain*",
                        help='Pattern to match chain directories (default: chain*)')
    parser.add_argument('--log_level', type=str, default='INFO',
        help='Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL')
    
    args = parser.parse_args()
    
    target_dir = os.path.expanduser(args.target_dir)
    output_dir = os.path.expanduser(args.output_dir) if args.output_dir else os.path.join(target_dir, "cluster_analysis")
    
    # Configure logging level
    log_level_map = {
        'DEBUG': logging.DEBUG, 'INFO': logging.INFO, 'WARNING': logging.WARNING,
        'ERROR': logging.ERROR, 'CRITICAL': logging.CRITICAL
    }
    console_level = log_level_map.get(args.log_level.upper(), MAIN_INFO)
    
    # Create analyzer and run analysis
    analyzer = MSAAnalyzer()
    analyzer.setup_logger(target_dir, "msa_analysis.log", console_level)
    
    # Generate comprehensive analysis report
    results = analyzer.generate_analysis_report(
        target_dir=target_dir,
        output_dir=output_dir,
        log_pattern=args.log_pattern,
        min_samples=args.min_samples,
        chain_pattern=args.chain_pattern
    )
    
    print(f"Analysis completed. Results saved to: {output_dir}")


if __name__ == "__main__":
    main() 