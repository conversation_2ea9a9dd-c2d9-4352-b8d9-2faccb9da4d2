#!/usr/bin/env python3
"""
MSAVisualizer class for creating visualizations from MSA data.
Follows single responsibility principle - only handles visualization logic.
"""

import os, sys
import logging
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import List, Optional, Tuple, Literal, Union, TYPE_CHECKING

# Import dimensional reduction algorithms
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

# Import MSA class
from .msa_pipeline import MSA

# Import visualization functions for MSA
from utils.visualization.visualize_msa import plot_conservation_heatmap, plot_sequence_similarity

# Type checking imports
if TYPE_CHECKING:
    from .clustering.interfaces import ClusteringResult


class MSAVisualizer:
    """
    Visualizer class for Multiple Sequence Alignment (MSA) data.
    
    This class focuses solely on visualization functionality,
    following the single responsibility principle. It now supports
    both basic MSA visualizations and advanced clustering-based
    visualizations including dimensional reduction techniques.
    """
    
    def __init__(self, msa_instance: MSA):
        """
        Initialize the MSAVisualizer with an MSA instance.
        
        Parameters
        ----------
        msa_instance : MSA
            An MSA object containing the sequence data to visualize.
        """
        self.msa = msa_instance
    
    def visualize_conservation(self,
        output_path: str,
        title: Optional[str] = None
    ) -> None:
        """
        Create a conservation heatmap visualization.
        
        Parameters
        ----------
        output_path : str
            Path where the visualization will be saved.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        """
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Generate position labels (1-based indexing)
        max_length = max(len(seq) for seq in self.msa.sequences) if self.msa.sequences else 0
        position_labels = list(range(1, max_length + 1))
        
        # Set default title if not provided
        if title is None:
            title = f"Conservation Analysis ({len(self.msa.sequences)} sequences)"
        
        # Create conservation heatmap
        plot_conservation_heatmap(
            msa_data=self.msa.sequences,
            output_path=output_path,
            reference_seq=self.msa.query_sequence,
            position_labels=position_labels,
            title=title
        )
        logging.info(f"Conservation visualization saved to: {output_path}")

    def visualize_similarity(self,
        output_path: str,
        method: Literal['identity', 'blosum62'] = 'identity',
        cluster_sequences: bool = True,
        title: Optional[str] = None,
        cluster_assignments: Optional[List[int]] = None,
        use_sequence_numbers: bool = True
    ) -> None:
        """
        Create a sequence similarity visualization.
        
        Parameters
        ----------
        output_path : str
            Path where the visualization will be saved.
        method : Literal['identity', 'blosum62'], optional
            Method for calculating sequence similarity. Defaults to 'identity'.
        cluster_sequences : bool, optional
            Whether to cluster sequences in the visualization. Defaults to True.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        cluster_assignments : Optional[List[int]], optional
            Pre-computed cluster assignments for each sequence.
        use_sequence_numbers : bool, optional
            Whether to use sequence numbers instead of descriptions for readability.
        """        
        # Set default title if not provided
        if title is None:
            cluster_text = "with clustering" if cluster_sequences else "without clustering"
            title = f"Sequence Similarity ({method}, {cluster_text})"
        
        # Create similarity visualization
        plot_sequence_similarity(
            msa_data=self.msa.sequences,
            output_path=output_path,
            method=method,
            cluster_sequences=cluster_sequences,
            sequence_labels=self.msa.descriptions,
            title=title,
            cluster_assignments=cluster_assignments,
            use_sequence_numbers=use_sequence_numbers
        )
        logging.info(f"Similarity visualization saved to: {output_path}")

    def visualize_msa(self,
        output_dir: str,
        prefix: Optional[str] = None,
        similarity_method: Literal['identity', 'blosum62'] = 'identity',
        cluster_sequences: bool = True,
        use_sequence_numbers: bool = True,
        clustering_result: Optional['ClusteringResult'] = None
    ) -> None:
        """
        Create all MSA visualizations.
        
        Parameters
        ----------
        output_dir : str
            Directory where visualizations will be saved.
        prefix : str, optional
            Prefix for output filenames. Defaults to empty string.
        similarity_method : Literal['identity', 'blosum62'], optional
            Method for calculating sequence similarity. Defaults to 'identity'.
        cluster_sequences : bool, optional
            Whether to cluster sequences in similarity visualization. Defaults to True.
        use_sequence_numbers : bool, optional
            Whether to use sequence numbers instead of descriptions for readability.
        clustering_result : Optional[ClusteringResult], optional
            If provided, will create enhanced similarity visualization with cluster information.
        """
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        if prefix is None:
            prefix = ""
        
        # Generate conservation visualization
        conservation_path = os.path.join(output_dir, f"{prefix}conservation_heatmap.pdf")
        self.visualize_conservation(conservation_path)
        
        # Generate similarity visualization
        if clustering_result is not None:
            # Create enhanced similarity visualization with cluster information
            similarity_path = os.path.join(output_dir, f"{prefix}sequence_similarity_clustered.pdf")
            self.visualize_similarity(
                output_path=similarity_path,
                method=similarity_method,
                cluster_sequences=False,  # Don't do hierarchical clustering since we have cluster assignments
                title=f"Sequence Similarity Matrix with {clustering_result.algorithm_name} Clusters",
                cluster_assignments=clustering_result.cluster_labels,
                use_sequence_numbers=use_sequence_numbers
            )
        else:
            # Create standard similarity visualization
            similarity_path = os.path.join(output_dir, f"{prefix}sequence_similarity.pdf")
            self.visualize_similarity(
                similarity_path,
                method=similarity_method,
                cluster_sequences=cluster_sequences,
                use_sequence_numbers=use_sequence_numbers
            )
        
        logging.info(f"All MSA visualizations saved in: {output_dir}")

    # =================== CLUSTERING-SPECIFIC VISUALIZATION METHODS ===================

    def visualize_cluster_distribution(self,
        clustering_result: 'ClusteringResult',
        output_path: str,
        title: Optional[str] = None
    ) -> None:
        """
        Create cluster size distribution visualization.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_path : str
            Path where the visualization will be saved.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        """
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Calculate cluster sizes
        cluster_sizes = []
        for cluster_id in range(clustering_result.n_clusters):
            cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
            cluster_sizes.append(len(cluster_sequences))
        
        if not cluster_sizes:
            logging.warning("No clusters to visualize distribution for")
            return

        # Set default title
        if title is None:
            title = f"Cluster Size Distribution ({clustering_result.algorithm_name})"
            
        # Create the visualization
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Histogram
        ax1.hist(cluster_sizes, bins=min(20, len(cluster_sizes)), alpha=0.7, edgecolor='black')
        ax1.set_xlabel('Cluster Size (number of sequences)')
        ax1.set_ylabel('Number of Clusters')
        ax1.set_title('Cluster Size Distribution')
        ax1.grid(True, alpha=0.3)
        
        # Box plot
        ax2.boxplot(cluster_sizes)
        ax2.set_ylabel('Cluster Size (number of sequences)')
        ax2.set_title('Cluster Size Statistics')
        ax2.grid(True, alpha=0.3)
        
        # Add statistics text
        stats_text = f'Total clusters: {len(cluster_sizes)}\n'
        stats_text += f'Mean size: {np.mean(cluster_sizes):.1f}\n'
        stats_text += f'Median size: {np.median(cluster_sizes):.1f}\n'
        stats_text += f'Max size: {np.max(cluster_sizes)}\n'
        stats_text += f'Min size: {np.min(cluster_sizes)}'
        
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.suptitle(title)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logging.info(f"Cluster distribution visualization saved to: {output_path}")

    # =================== DIMENSIONAL REDUCTION VISUALIZATION METHODS ===================

    def visualize_clustering_pca(self,
        clustering_result: 'ClusteringResult',
        output_path: str,
        title: Optional[str] = None,
        encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
        show_query: bool = True,
        max_clusters_display: int = 10
    ) -> None:
        """
        Create PCA-based clustering visualization following ClusterMSA.py logic.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_path : str
            Path where the visualization will be saved.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        encoding_method : Literal['onehot', 'MSAtransformer'], optional
            Encoding method for sequences. Defaults to 'onehot'.
        show_query : bool, optional
            Whether to highlight the query sequence. Defaults to True.
        max_clusters_display : int, optional
            Maximum number of clusters to display with distinct colors. Defaults to 10.
        """
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            # Check if clustering_result has valid_indices for mapping
            if hasattr(clustering_result, 'metadata') and 'valid_indices' in clustering_result.metadata:
                valid_indices = clustering_result.metadata['valid_indices']
                # Map valid indices to original MSA (excluding query at index 0)
                sequences_for_encoding = [self.msa.sequences[i] for i in valid_indices if i > 0]
                # Get cluster labels for sequences (not including query)
                cluster_labels = [clustering_result.cluster_labels[i] for i in range(len(clustering_result.cluster_labels))]
            else:
                # Fallback: Use direct mapping, but check dimension consistency
                sequences_for_encoding = self.msa.sequences[1:] if len(self.msa.sequences) > 1 else []
                cluster_labels = clustering_result.cluster_labels[1:] if len(clustering_result.cluster_labels) > 1 else []
                
                # Ensure dimensions match
                if len(sequences_for_encoding) != len(cluster_labels):
                    logging.warning(f"Dimension mismatch: {len(sequences_for_encoding)} sequences vs {len(cluster_labels)} cluster labels")
                    # Use minimum dimension to avoid indexing errors
                    min_len = min(len(sequences_for_encoding), len(cluster_labels))
                    sequences_for_encoding = sequences_for_encoding[:min_len]
                    cluster_labels = cluster_labels[:min_len]
            
            if len(sequences_for_encoding) < 2:
                logging.warning("Not enough sequences for PCA visualization")
                return
            
            # Get maximum sequence length
            max_seq_len = max(len(seq) for seq in sequences_for_encoding)
            
            # Encode sequences using MSA's encoding method
            encoded_seqs = self.msa.encode_sequences(
                sequences=sequences_for_encoding,
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
            
            # Perform PCA
            pca_model = PCA()
            pca_embedding = pca_model.fit_transform(encoded_seqs)
            
            # Create PCA visualization following ClusterMSA.py logic
            self._plot_clustering_landscape(
                embedding=pca_embedding,
                cluster_labels=cluster_labels,
                output_path=output_path,
                plot_type='PCA',
                title=title,
                show_query=show_query,
                max_clusters_display=max_clusters_display,
                pca_model=pca_model,
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
            
            logging.info(f"PCA clustering visualization saved to: {output_path}")
            
        except Exception as e:
            logging.error(f"Failed to create PCA visualization: {e}")
            raise

    def visualize_clustering_tsne(self,
        clustering_result: 'ClusteringResult',
        output_path: str,
        title: Optional[str] = None,
        encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
        show_query: bool = True,
        max_clusters_display: int = 10,
        perplexity: float = 30.0,
        random_state: int = 42
    ) -> None:
        """
        Create t-SNE-based clustering visualization following ClusterMSA.py logic.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_path : str
            Path where the visualization will be saved.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        encoding_method : Literal['onehot', 'MSAtransformer'], optional
            Encoding method for sequences. Defaults to 'onehot'.
        show_query : bool, optional
            Whether to highlight the query sequence. Defaults to True.
        max_clusters_display : int, optional
            Maximum number of clusters to display with distinct colors. Defaults to 10.
        perplexity : float, optional
            Perplexity parameter for t-SNE. Defaults to 30.0.
        random_state : int, optional
            Random state for reproducible results. Defaults to 42.
        """
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            # Check if clustering_result has valid_indices for mapping
            if hasattr(clustering_result, 'metadata') and 'valid_indices' in clustering_result.metadata:
                valid_indices = clustering_result.metadata['valid_indices']
                # For t-SNE, include query sequence + valid sequences
                all_sequences = [self.msa.sequences[0]] + [self.msa.sequences[i] for i in valid_indices if i > 0]
                # Get cluster labels (map to valid indices, no query)
                cluster_labels = [clustering_result.cluster_labels[i] for i in range(len(clustering_result.cluster_labels))]
            else:
                # Fallback: Use direct mapping, but check dimension consistency
                all_sequences = self.msa.sequences
                cluster_labels = clustering_result.cluster_labels[1:] if len(clustering_result.cluster_labels) > 1 else []
                
                # Ensure dimensions match (excluding query from both)
                sequences_no_query = all_sequences[1:]
                if len(sequences_no_query) != len(cluster_labels):
                    logging.warning(f"Dimension mismatch for t-SNE: {len(sequences_no_query)} sequences vs {len(cluster_labels)} cluster labels")
                    # Use minimum dimension to avoid indexing errors
                    min_len = min(len(sequences_no_query), len(cluster_labels))
                    all_sequences = [all_sequences[0]] + sequences_no_query[:min_len]
                    cluster_labels = cluster_labels[:min_len]
            
            if len(all_sequences) < 2:
                logging.warning("Not enough sequences for t-SNE visualization")
                return
            
            # Get maximum sequence length
            max_seq_len = max(len(seq) for seq in all_sequences)
            
            # Encode all sequences including query
            encoded_seqs = self.msa.encode_sequences(
                sequences=all_sequences,
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
            
            # Perform t-SNE
            tsne_model = TSNE(perplexity=perplexity, random_state=random_state)
            tsne_embedding = tsne_model.fit_transform(encoded_seqs)
            
            # Split embedding: query is first, others follow
            query_embedding = tsne_embedding[0:1]  # First sequence (query)
            sequences_embedding = tsne_embedding[1:]  # Remaining sequences
            
            # Create t-SNE visualization
            self._plot_clustering_landscape(
                embedding=sequences_embedding,
                cluster_labels=cluster_labels,
                output_path=output_path,
                plot_type='t-SNE',
                title=title,
                show_query=show_query,
                max_clusters_display=max_clusters_display,
                query_embedding=query_embedding
            )
            
            logging.info(f"t-SNE clustering visualization saved to: {output_path}")
            
        except Exception as e:
            logging.error(f"Failed to create t-SNE visualization: {e}")
            raise

    def _plot_clustering_landscape(self,
        embedding: np.ndarray,
        cluster_labels: List[int],
        output_path: str,
        plot_type: str,
        title: Optional[str] = None,
        show_query: bool = True,
        max_clusters_display: int = 10,
        query_embedding: Optional[np.ndarray] = None,
        pca_model: Optional[PCA] = None,
        max_seq_len: Optional[int] = None,
        encoding_method: Optional[str] = None
    ) -> None:
        """
        Create clustering landscape plot following ClusterMSA.py logic.
        
        This method replicates the plot_landscape function from ClusterMSA.py
        with adaptations for the MSAVisualizer structure.
        
        Parameters
        ----------
        embedding : np.ndarray
            2D embedding coordinates for sequences (excluding query).
        cluster_labels : List[int]
            Cluster labels for each sequence (excluding query).
        output_path : str
            Path to save the visualization.
        plot_type : str
            Type of dimensional reduction ('PCA' or 't-SNE').
        title : Optional[str], optional
            Title for the visualization.
        show_query : bool, optional
            Whether to show the query sequence.
        max_clusters_display : int, optional
            Maximum number of clusters to display with distinct colors.
        query_embedding : Optional[np.ndarray], optional
            Query sequence embedding coordinates (for t-SNE).
        pca_model : Optional[PCA], optional
            Fitted PCA model (for PCA visualization).
        max_seq_len : Optional[int], optional
            Maximum sequence length (for PCA query transformation).
        encoding_method : Optional[str], optional
            Encoding method (for PCA query transformation).
        """
        # Convert embedding and labels to appropriate format
        x_coords = embedding[:, 0]
        y_coords = embedding[:, 1]
        
        # Set up axis labels
        if plot_type.upper() == 'PCA':
            x_label, y_label = 'PC 1', 'PC 2'
        else:  # t-SNE
            x_label, y_label = 't-SNE 1', 't-SNE 2'
        
        # Set default title
        if title is None:
            title = f"MSA subsampling with N={len(embedding)} seq. ({plot_type})"
        
        # Calculate plot limits with padding
        x_min, x_max = np.min(x_coords), np.max(x_coords)
        y_min, y_max = np.min(y_coords), np.max(y_coords)
        x_padding = (x_max - x_min) * 0.1 if x_max != x_min else 10
        y_padding = (y_max - y_min) * 0.1 if y_max != y_min else 10
        
        # Create two plots: filtered and unfiltered (following ClusterMSA.py logic)
        for plot_suffix, filter_large_clusters in [('', True), ('_total_cluster', False)]:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # Convert cluster_labels to numpy array for easier indexing
            labels_array = np.array(cluster_labels)
            
            # Plot unclustered points (noise, label = -1)
            noise_mask = labels_array == -1
            if np.any(noise_mask):
                ax.scatter(x_coords[noise_mask], y_coords[noise_mask], 
                          color='lightgray', marker='x', label='unclustered', alpha=0.7)
            
            if filter_large_clusters:
                # Filtered version: show only first 10 clusters distinctly
                large_cluster_mask = labels_array > max_clusters_display - 1
                if np.any(large_cluster_mask):
                    ax.scatter(x_coords[large_cluster_mask], y_coords[large_cluster_mask], 
                              color='black', label='other clusters', alpha=0.7)
                
                # Show first max_clusters_display clusters with distinct colors
                display_mask = (labels_array >= 0) & (labels_array <= max_clusters_display - 1)
                if np.any(display_mask):
                    # Create scatter plot with cluster colors
                    scatter = ax.scatter(x_coords[display_mask], y_coords[display_mask], 
                                       c=labels_array[display_mask], cmap='tab10', 
                                       s=50, alpha=0.8)
                    
                    # Add colorbar for cluster identification
                    cbar = plt.colorbar(scatter, ax=ax)
                    cbar.set_label('Cluster ID')
            else:
                # Unfiltered version: show all clusters
                clustered_mask = labels_array != -1
                if np.any(clustered_mask):
                    scatter = ax.scatter(x_coords[clustered_mask], y_coords[clustered_mask], 
                                       c=labels_array[clustered_mask], cmap='tab10', 
                                       s=50, alpha=0.7)
                    
                    # Add colorbar
                    cbar = plt.colorbar(scatter, ax=ax)
                    cbar.set_label('Cluster ID')
            
            # Add query sequence if requested
            if show_query:
                if query_embedding is not None:
                    # For t-SNE, query embedding is provided
                    query_x, query_y = query_embedding[0, 0], query_embedding[0, 1]
                elif pca_model is not None and max_seq_len is not None and encoding_method is not None:
                    # For PCA, transform query sequence
                    query_encoded = self.msa.encode_sequences(
                        sequences=[self.msa.query_sequence],
                        max_seq_len=max_seq_len,
                        encoding_method=encoding_method
                    )
                    query_pca = pca_model.transform(query_encoded)
                    query_x, query_y = query_pca[0, 0], query_pca[0, 1]
                else:
                    # Skip query if we can't determine its position
                    query_x, query_y = None, None
                
                if query_x is not None and query_y is not None:
                    ax.scatter(query_x, query_y, color='red', marker='*', s=150, 
                            label='Query Sequence', edgecolors='black', linewidth=1)
            
            # Set labels and title
            ax.set_xlabel(x_label)
            ax.set_ylabel(y_label)
            ax.set_title(title)
            
            # Set axis limits with padding
            ax.set_xlim(x_min - x_padding, x_max + x_padding)
            ax.set_ylim(y_min - y_padding, y_max + y_padding)
            
            # Add legend
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
            
            # Adjust layout and save
            plt.tight_layout()
            
            # Generate output filename
            base_path = Path(output_path)
            final_output_path = base_path.parent / f"{base_path.stem}{plot_suffix}{base_path.suffix}"
            
            plt.savefig(final_output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logging.info(f"Created {plot_type} landscape plot: {final_output_path}")

    def visualize_dimensional_reduction(self,
        clustering_result: 'ClusteringResult',
        output_dir: str,
        prefix: Optional[str] = None,
        encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
        create_pca: bool = True,
        create_tsne: bool = True,
        show_query: bool = True,
        max_clusters_display: int = 10,
        tsne_perplexity: float = 30.0,
        random_state: int = 42
    ) -> None:
        """
        Create comprehensive dimensional reduction visualizations.
        
        This method creates both PCA and t-SNE visualizations in one call,
        providing a complete dimensional reduction analysis.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_dir : str
            Directory where visualizations will be saved.
        prefix : Optional[str], optional
            Prefix for output filenames.
        encoding_method : Literal['onehot', 'MSAtransformer'], optional
            Encoding method for sequences. Defaults to 'onehot'.
        create_pca : bool, optional
            Whether to create PCA visualization. Defaults to True.
        create_tsne : bool, optional
            Whether to create t-SNE visualization. Defaults to True.
        show_query : bool, optional
            Whether to highlight the query sequence. Defaults to True.
        max_clusters_display : int, optional
            Maximum number of clusters to display with distinct colors. Defaults to 10.
        tsne_perplexity : float, optional
            Perplexity parameter for t-SNE. Defaults to 30.0.
        random_state : int, optional
            Random state for reproducible results. Defaults to 42.
        """
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        if prefix is None:
            prefix = ""
        
        # Create PCA visualization
        if create_pca:
            pca_output_path = os.path.join(output_dir, f"{prefix}clustering_PCA.pdf")
            self.visualize_clustering_pca(
                clustering_result=clustering_result,
                output_path=pca_output_path,
                encoding_method=encoding_method,
                show_query=show_query,
                max_clusters_display=max_clusters_display
            )
        
        # Create t-SNE visualization
        if create_tsne:
            tsne_output_path = os.path.join(output_dir, f"{prefix}clustering_tSNE.pdf")
            self.visualize_clustering_tsne(
                clustering_result=clustering_result,
                output_path=tsne_output_path,
                encoding_method=encoding_method,
                show_query=show_query,
                max_clusters_display=max_clusters_display,
                perplexity=tsne_perplexity,
                random_state=random_state
            )
        
        logging.info(f"Dimensional reduction visualizations completed in: {output_dir}")

    # =================== EXISTING CLUSTERING VISUALIZATION METHODS ===================
    # (keeping existing methods for backward compatibility)

    def visualize_cluster_summary(self,
        clustering_result: 'ClusteringResult',
        output_path: str,
        title: Optional[str] = None
    ) -> None:
        """
        Create clustering summary visualization.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_path : str
            Path where the visualization will be saved.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Prepare data
            total_sequences = len(clustering_result.sequences)
            clustered_sequences = total_sequences - clustering_result.n_noise
            
            # Create summary pie chart
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # Pie chart: Clustered vs Noise
            labels = ['Clustered', 'Noise']
            sizes = [clustered_sequences, clustering_result.n_noise]
            colors = ['lightblue', 'lightcoral']
            
            wedges, texts, autotexts = ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                              startangle=90, explode=(0.05, 0))
            ax1.set_title(title or f'Clustering Overview ({clustering_result.algorithm_name})')
            
            # Bar chart: Summary statistics
            categories = ['Total\nSequences', 'Clusters', 'Noise\nPoints', 'Largest\nCluster']
            
            # Calculate largest cluster size
            largest_cluster_size = 0
            if clustering_result.n_clusters > 0:
                cluster_sizes = []
                for cluster_id in range(clustering_result.n_clusters):
                    cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
                    cluster_sizes.append(len(cluster_sequences))
                largest_cluster_size = max(cluster_sizes) if cluster_sizes else 0
            
            values = [total_sequences, clustering_result.n_clusters, 
                     clustering_result.n_noise, largest_cluster_size]
            
            bars = ax2.bar(categories, values, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
            ax2.set_title('Clustering Statistics')
            ax2.set_ylabel('Count')
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01*max(values),
                        f'{value}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # Save plot
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logging.info(f"Cluster summary visualization saved to: {output_path}")
            
        except Exception as e:
            plt.close()  # Ensure plot is closed even if error occurs
            raise RuntimeError(f"Failed to create cluster summary plot: {e}") from e

    def visualize_cluster_comparison(self,
        clustering_result: 'ClusteringResult',
        output_path: str,
        title: Optional[str] = None
    ) -> None:
        """
        Create cluster comparison visualization.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_path : str
            Path where the visualization will be saved.
        title : Optional[str], optional
            Title for the visualization. If None, a default title will be used.
        """
        try:
            if clustering_result.n_clusters < 2:
                raise ValueError("At least 2 clusters required for comparison visualization")
            
            # Create output directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Collect cluster information
            cluster_data = []
            for cluster_id in range(clustering_result.n_clusters):
                cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
                cluster_data.append({
                    'id': cluster_id,
                    'size': len(cluster_sequences),
                    'sequences': cluster_sequences
                })
            
            # Sort clusters by size for better visualization
            cluster_data.sort(key=lambda x: x['size'], reverse=True)
            
            # Create comparison plot
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
            
            # Bar chart of cluster sizes
            cluster_ids = [f"Cluster {data['id']}" for data in cluster_data]
            cluster_sizes = [data['size'] for data in cluster_data]
            
            bars = ax1.bar(range(len(cluster_ids)), cluster_sizes, 
                          color=plt.cm.Set3(np.linspace(0, 1, len(cluster_ids))))
            ax1.set_xlabel('Clusters')
            ax1.set_ylabel('Number of Sequences')
            ax1.set_title('Cluster Size Comparison')
            ax1.set_xticks(range(len(cluster_ids)))
            ax1.set_xticklabels(cluster_ids, rotation=45, ha='right')
            
            # Add value labels on bars
            for bar, size in zip(bars, cluster_sizes):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01*max(cluster_sizes),
                        f'{size}', ha='center', va='bottom')
            
            # Cumulative percentage plot
            total_sequences = sum(cluster_sizes)
            cumulative_pct = np.cumsum(cluster_sizes) / total_sequences * 100
            
            ax2.plot(range(len(cluster_ids)), cumulative_pct, 'bo-', linewidth=2, markersize=6)
            ax2.set_xlabel('Clusters (sorted by size)')
            ax2.set_ylabel('Cumulative Percentage (%)')
            ax2.set_title('Cumulative Sequence Coverage')
            ax2.grid(True, alpha=0.3)
            ax2.set_ylim(0, 105)
            
            # Add percentage labels
            for i, pct in enumerate(cumulative_pct):
                ax2.text(i, pct + 2, f'{pct:.1f}%', ha='center', va='bottom')
            
            plt.suptitle(title or f'Cluster Analysis - {clustering_result.algorithm_name}', fontsize=14)
            plt.tight_layout()
            
            # Save plot
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logging.info(f"Cluster comparison visualization saved to: {output_path}")
            
        except Exception as e:
            plt.close()  # Ensure plot is closed even if error occurs
            raise RuntimeError(f"Failed to create cluster comparison plot: {e}") from e

    def visualize_individual_clusters(self,
        clustering_result: 'ClusteringResult',
        output_dir: str,
        prefix: Optional[str] = None,
        similarity_method: Literal['identity', 'blosum62'] = 'identity',
        min_cluster_size: int = 2
    ) -> None:
        """
        Create individual MSA visualizations for each cluster.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_dir : str
            Directory where cluster visualizations will be saved.
        prefix : str, optional
            Prefix for output filenames. Defaults to empty string.
        similarity_method : Literal['identity', 'blosum62'], optional
            Method for calculating sequence similarity. Defaults to 'identity'.
        min_cluster_size : int, optional
            Minimum cluster size to create visualizations. Defaults to 2.
        """
        try:
            if clustering_result.n_clusters == 0:
                raise ValueError("No clusters found in clustering result")
            if min_cluster_size < 1:
                raise ValueError("min_cluster_size must be at least 1")
            
            # Create subdirectory for cluster-specific visualizations
            cluster_viz_dir = Path(output_dir) / "clusters"
            cluster_viz_dir.mkdir(parents=True, exist_ok=True)
            
            clusters_processed = 0
            
            # Generate visualizations for each cluster
            for cluster_id in range(clustering_result.n_clusters):
                try:
                    cluster_sequences = clustering_result.get_cluster_sequences(cluster_id)
                    cluster_descriptions = clustering_result.get_cluster_descriptions(cluster_id)
                    
                    if len(cluster_sequences) < min_cluster_size:
                        logging.info(f"Skipping cluster {cluster_id}: size {len(cluster_sequences)} < {min_cluster_size}")
                        continue
                    
                    # Create MSA object for this cluster
                    cluster_msa = MSA(
                        query_sequence=self.msa.query_sequence,
                        chain_poly_type=self.msa.chain_poly_type,
                        sequences=[self.msa.query_sequence] + cluster_sequences,  # Include query
                        descriptions=['Query'] + cluster_descriptions,
                        deduplicated=False
                    )
                    
                    # Create visualizer for this cluster
                    cluster_visualizer = MSAVisualizer(cluster_msa)
                    
                    # Generate cluster-specific visualizations
                    file_prefix = f"{prefix}cluster_{cluster_id:03d}_" if prefix else f"cluster_{cluster_id:03d}_"
                    
                    # Conservation for this cluster
                    conservation_path = cluster_viz_dir / f"{file_prefix}conservation.pdf"
                    cluster_visualizer.visualize_conservation(
                        output_path=str(conservation_path),
                        title=f"Cluster {cluster_id} Conservation ({len(cluster_sequences)} sequences)"
                    )
                    
                    # Similarity for this cluster
                    similarity_path = cluster_viz_dir / f"{file_prefix}similarity.pdf"
                    cluster_visualizer.visualize_similarity(
                        output_path=str(similarity_path),
                        method=similarity_method,
                        cluster_sequences=False,  # Don't cluster within cluster
                        title=f"Cluster {cluster_id} Sequence Similarity"
                    )
                    
                    clusters_processed += 1
                    
                except Exception as e:
                    logging.warning(f"Failed to create visualizations for cluster {cluster_id}: {e}")
                    # Continue processing other clusters
            
            logging.info(f"Created visualizations for {clusters_processed} clusters in: {cluster_viz_dir}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to create individual cluster visualizations: {e}") from e

    def visualize_clustering_result(self,
        clustering_result: 'ClusteringResult',
        output_dir: str,
        prefix: Optional[str] = None,
        similarity_method: Literal['identity', 'blosum62'] = 'identity',
        create_individual_clusters: bool = False,
        min_cluster_size: int = 1
    ) -> None:
        """
        Create comprehensive visualizations for clustering results.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_dir : str
            Directory where visualizations will be saved.
        prefix : Optional[str], optional
            Prefix for output filenames. Defaults to empty string.
        similarity_method : Literal['identity', 'blosum62'], optional
            Method for calculating sequence similarity. Defaults to 'identity'.
        create_individual_clusters : bool, optional
            Whether to create individual cluster visualizations. Defaults to False.
        min_cluster_size : int, optional
            Minimum cluster size for individual visualizations. Defaults to 1.
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            if prefix is None:
                prefix = ""
            
            # 1. Cluster distribution visualization
            logging.info("Creating cluster distribution visualization...")
            distribution_path = os.path.join(output_dir, f"{prefix}cluster_distribution.pdf")
            self.visualize_cluster_distribution(clustering_result, distribution_path)
            
            # 2. Cluster summary visualization
            logging.info("Creating cluster summary visualization...")
            summary_path = os.path.join(output_dir, f"{prefix}cluster_summary.pdf")
            self.visualize_cluster_summary(clustering_result, summary_path)
            
            # 3. Cluster comparison visualization
            logging.info("Creating cluster comparison visualization...")
            comparison_path = os.path.join(output_dir, f"{prefix}cluster_comparison.pdf")
            self.visualize_cluster_comparison(clustering_result, comparison_path)
            
            # 4. Individual cluster visualizations if requested
            if create_individual_clusters:
                logging.info("Creating individual cluster visualizations...")
                self.visualize_individual_clusters(
                    clustering_result=clustering_result,
                    output_dir=output_dir,  # Use same directory instead of subdirectory
                    prefix=prefix,
                    similarity_method=similarity_method,
                    min_cluster_size=min_cluster_size
                )
            
            logging.info(f"✓ Comprehensive clustering visualization completed in: {output_dir}")
            
        except Exception as e:
            logging.error(f"Error in visualize_clustering_result: {e}")
            raise RuntimeError(f"Failed to create clustering visualizations: {e}")

    def visualize_msa_with_clustering(self,
        clustering_result: 'ClusteringResult',
        output_dir: str,
        prefix: Optional[str] = None,
        similarity_method: Literal['identity', 'blosum62'] = 'identity',
        create_individual_clusters: bool = False,
        min_cluster_size: int = 1
    ) -> None:
        """
        Create comprehensive MSA and clustering visualizations in a single directory.
        
        This method generates both basic MSA visualizations and comprehensive
        clustering analysis in a single, simplified directory structure.
        
        Parameters
        ----------
        clustering_result : ClusteringResult
            Clustering result object containing cluster assignments.
        output_dir : str
            Directory where visualizations will be saved.
        prefix : Optional[str], optional
            Prefix for output filenames. Defaults to empty string.
        similarity_method : Literal['identity', 'blosum62'], optional
            Method for calculating sequence similarity. Defaults to 'identity'.
        create_individual_clusters : bool, optional
            Whether to create individual cluster visualizations. Defaults to False.
        min_cluster_size : int, optional
            Minimum cluster size for individual visualizations. Defaults to 1.
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            if prefix is None:
                prefix = ""
            
            logging.info("Creating comprehensive MSA and clustering analysis...")
            
            # 1. Basic MSA visualizations with clustering information
            logging.info("  → Creating MSA conservation heatmap...")
            conservation_path = os.path.join(output_dir, f"{prefix}msa_conservation_heatmap.pdf")
            self.visualize_conservation(conservation_path)
            
            logging.info("  → Creating MSA sequence similarity with clusters...")
            similarity_path = os.path.join(output_dir, f"{prefix}msa_sequence_similarity.pdf")
            self.visualize_similarity(
                output_path=similarity_path,
                method=similarity_method,
                cluster_sequences=False,  # Don't use hierarchical clustering
                title=f"MSA Sequence Similarity with {clustering_result.algorithm_name} Clusters",
                cluster_assignments=clustering_result.cluster_labels,
                use_sequence_numbers=True
            )
            
            # 2. Clustering analysis visualizations
            logging.info("  → Creating cluster distribution analysis...")
            distribution_path = os.path.join(output_dir, f"{prefix}cluster_distribution.pdf")
            self.visualize_cluster_distribution(clustering_result, distribution_path)
            
            logging.info("  → Creating cluster summary analysis...")
            summary_path = os.path.join(output_dir, f"{prefix}cluster_summary.pdf")
            self.visualize_cluster_summary(clustering_result, summary_path)
            
            logging.info("  → Creating cluster comparison analysis...")
            comparison_path = os.path.join(output_dir, f"{prefix}cluster_comparison.pdf")
            self.visualize_cluster_comparison(clustering_result, comparison_path)
            
            # 3. Individual cluster visualizations if requested
            if create_individual_clusters:
                logging.info("  → Creating individual cluster visualizations...")
                self.visualize_individual_clusters(
                    clustering_result=clustering_result,
                    output_dir=output_dir,  # Use same directory
                    prefix=prefix,
                    similarity_method=similarity_method,
                    min_cluster_size=min_cluster_size
                )
            
            # 4. Create a comprehensive analysis summary
            logging.info("  → Creating analysis summary...")
            summary_path = os.path.join(output_dir, f"{prefix}analysis_summary.txt")
            with open(summary_path, 'w') as f:
                f.write(f"MSA and Clustering Analysis Summary\n")
                f.write(f"=====================================\n\n")
                f.write(f"MSA Information:\n")
                f.write(f"  - Total sequences: {len(self.msa.sequences)}\n")
                f.write(f"  - Sequence length: {len(self.msa.sequences[0]) if self.msa.sequences else 0}\n\n")
                f.write(f"Clustering Information:\n")
                f.write(f"  - Algorithm: {clustering_result.algorithm_name}\n")
                f.write(f"  - Number of clusters: {clustering_result.n_clusters}\n")
                f.write(f"  - Number of noise points: {clustering_result.n_noise}\n\n")
                f.write(f"Generated Files:\n")
                f.write(f"  - MSA conservation: {prefix}msa_conservation_heatmap.pdf\n")
                f.write(f"  - MSA similarity: {prefix}msa_sequence_similarity.pdf\n")
                f.write(f"  - Cluster distribution: {prefix}cluster_distribution.pdf\n")
                f.write(f"  - Cluster summary: {prefix}cluster_summary.pdf\n")
                f.write(f"  - Cluster comparison: {prefix}cluster_comparison.pdf\n")
                if create_individual_clusters:
                    f.write(f"  - Individual clusters: clusters/ subdirectory\n")
                f.write(f"\nAnalysis completed successfully!\n")
            
            logging.info(f"✓ Comprehensive MSA and clustering analysis completed in: {output_dir}")
            
        except Exception as e:
            logging.error(f"Error in visualize_msa_with_clustering: {e}")
            raise RuntimeError(f"Failed to create comprehensive visualizations: {e}")


def main():
    """
    Main function for command-line interface.
    Now uses MSA.from_file() instead of MSAVisualizer.from_file().
    """
    import argparse
    
    def setup_logging(debug: bool = False) -> None:
        """Set up logging configuration."""
        level = logging.DEBUG if debug else logging.INFO
        logging.basicConfig(
            level=level,
            format='%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    parser = argparse.ArgumentParser(description="Visualize MSA data")
    
    parser.add_argument(
        "--msa_file", required=True,
        help="Path to the MSA file (supported formats: fasta, a3m, stockholm)"
    )
    parser.add_argument(
        "--output_dir", required=True,
        help="Directory to save output files"
    )
    parser.add_argument(
        "--prefix", default="",
        help="Prefix for output filenames"
    )
    parser.add_argument(
        "--similarity_method",
        choices=['identity', 'blosum62'],
        default='identity',
        help="Method to calculate sequence similarity"
    )
    parser.add_argument(
        "--no_clustering",
        action='store_true',
        help="Disable sequence clustering in similarity visualization"
    )
    parser.add_argument(
        "--chain_poly_type",
        choices=['protein', 'rna', 'dna'],
        default='protein',
        help="Type of polymer chain"
    )
    parser.add_argument(
        "--debug",
        action='store_true',
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    
    try:
        # Create MSA from file (using the new MSA.from_file method)
        logging.info(f'Reading MSA file: {args.msa_file}')
        msa = MSA.from_file(args.msa_file, args.chain_poly_type)
        logging.info(f'Successfully loaded {msa.depth} sequences')
        
        # Create visualizer and generate visualizations
        visualizer = msa.get_visualizer()
        visualizer.visualize_msa(
            output_dir=args.output_dir,
            prefix=args.prefix,
            similarity_method=args.similarity_method,
            cluster_sequences=not args.no_clustering,
            use_sequence_numbers=True
        )
        
        logging.info("Analysis complete")
        
    except Exception as e:
        logging.error(f"Error during visualization: {e}")
        raise


if __name__ == "__main__":
    main() 