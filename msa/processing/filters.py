"""
Filtering utilities for MSA sequences and data.

This module provides specialized filters for sequences and MSAs,
consolidating filtering logic from throughout the codebase.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Callable, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..core.msa_core import MS<PERSON><PERSON>
from ..core.sequence import Sequence


@dataclass
class FilterConfig:
    """Base configuration for filters."""
    keep_query: bool = True
    log_filtered: bool = True


class BaseFilter(ABC):
    """Abstract base class for all filters."""
    
    def __init__(self, config: Optional[FilterConfig] = None):
        """
        Initialize the filter.
        
        Parameters
        ----------
        config : Optional[FilterConfig]
            Filter configuration
        """
        self.config = config or FilterConfig()
    
    @abstractmethod
    def apply(self, msa: MSACore) -> MSACore:
        """
        Apply the filter to an MSA.
        
        Parameters
        ----------
        msa : MSACore
            Input MSA
            
        Returns
        -------
        <PERSON><PERSON>ore
            Filtered MSA
        """
        pass
    
    @abstractmethod
    def test_sequence(self, sequence: Sequence) -> bool:
        """
        Test if a sequence passes the filter.
        
        Parameters
        ----------
        sequence : Sequence
            Sequence to test
            
        Returns
        -------
        bool
            True if sequence passes filter
        """
        pass


class SequenceFilter:
    """
    Composite filter for individual sequences.
    
    This class allows combining multiple sequence-level filters
    and applying them efficiently to MSAs.
    """
    
    def __init__(self):
        """Initialize the sequence filter."""
        self._filters: List[BaseFilter] = []
    
    def add_filter(self, filter_instance: BaseFilter) -> 'SequenceFilter':
        """
        Add a filter to the chain.
        
        Parameters
        ----------
        filter_instance : BaseFilter
            Filter to add
            
        Returns
        -------
        SequenceFilter
            Self for method chaining
        """
        self._filters.append(filter_instance)
        return self
    
    def apply(self, msa: MSACore) -> MSACore:
        """
        Apply all filters to the MSA.
        
        Parameters
        ----------
        msa : MSACore
            Input MSA
            
        Returns
        -------
        MSACore
            Filtered MSA
        """
        filtered_msa = msa
        
        for filter_instance in self._filters:
            filtered_msa = filter_instance.apply(filtered_msa)
            
            if filter_instance.config.log_filtered:
                logging.debug(f"Applied {filter_instance.__class__.__name__}: "
                            f"{len(msa)} -> {len(filtered_msa)} sequences")
        
        return filtered_msa
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """
        Test if a sequence passes all filters.
        
        Parameters
        ----------
        sequence : Sequence
            Sequence to test
            
        Returns
        -------
        bool
            True if sequence passes all filters
        """
        return all(f.test_sequence(sequence) for f in self._filters)


class LengthFilter(BaseFilter):
    """
    Filter sequences by length.
    
    Note: Length filtering is currently disabled by default in preprocessing
    as per project requirements, but this filter remains available for custom use.
    """
    
    def __init__(self, 
                 min_length: Optional[int] = None,
                 max_length: Optional[int] = None,
                 config: Optional[FilterConfig] = None):
        """
        Initialize length filter.
        
        Parameters
        ----------
        min_length : Optional[int]
            Minimum sequence length
        max_length : Optional[int]
            Maximum sequence length
        config : Optional[FilterConfig]
            Filter configuration
        """
        super().__init__(config)
        self.min_length = min_length
        self.max_length = max_length
    
    def apply(self, msa: MSACore) -> MSACore:
        """Apply length filter to MSA."""
        return msa.filter_by_length(
            min_length=self.min_length,
            max_length=self.max_length,
            keep_query=self.config.keep_query
        )
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """Test if sequence passes length filter."""
        if self.min_length is not None and sequence.length < self.min_length:
            return False
        if self.max_length is not None and sequence.length > self.max_length:
            return False
        return True


class GapContentFilter(BaseFilter):
    """Filter sequences by gap content."""
    
    def __init__(self, 
                 max_gap_content: float,
                 config: Optional[FilterConfig] = None):
        """
        Initialize gap content filter.
        
        Parameters
        ----------
        max_gap_content : float
            Maximum allowed gap content (0-1)
        config : Optional[FilterConfig]
            Filter configuration
        """
        super().__init__(config)
        self.max_gap_content = max_gap_content
    
    def apply(self, msa: MSACore) -> MSACore:
        """Apply gap content filter to MSA."""
        return msa.filter_by_gap_content(
            max_gap_content=self.max_gap_content,
            keep_query=self.config.keep_query
        )
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """Test if sequence passes gap content filter."""
        return sequence.gap_content <= self.max_gap_content


class IdentityFilter(BaseFilter):
    """Filter sequences by identity to query or other sequences."""
    
    def __init__(self, 
                 min_identity: Optional[float] = None,
                 max_identity: Optional[float] = None,
                 reference_sequence: Optional[Sequence] = None,
                 config: Optional[FilterConfig] = None):
        """
        Initialize identity filter.
        
        Parameters
        ----------
        min_identity : Optional[float]
            Minimum identity to reference (0-1)
        max_identity : Optional[float]
            Maximum identity to reference (0-1)
        reference_sequence : Optional[Sequence]
            Reference sequence (uses query if None)
        config : Optional[FilterConfig]
            Filter configuration
        """
        super().__init__(config)
        self.min_identity = min_identity
        self.max_identity = max_identity
        self.reference_sequence = reference_sequence
    
    def apply(self, msa: MSACore) -> MSACore:
        """Apply identity filter to MSA."""
        if not msa.sequences:
            return msa
        
        # Use query as reference if not specified
        reference = self.reference_sequence or msa.query_sequence
        if reference is None:
            logging.warning("No reference sequence available for identity filter")
            return msa
        
        filtered_sequences = []
        
        for seq in msa.sequences:
            # Always keep query if requested
            if self.config.keep_query and seq.is_query:
                filtered_sequences.append(seq)
                continue
            
            # Calculate identity to reference
            identity = seq.calculate_identity(reference)
            
            # Apply identity thresholds
            if self.min_identity is not None and identity < self.min_identity:
                continue
            if self.max_identity is not None and identity > self.max_identity:
                continue
            
            filtered_sequences.append(seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=msa.chain_poly_type
        )
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """Test if sequence passes identity filter."""
        if self.reference_sequence is None:
            return True  # Cannot test without reference
        
        identity = sequence.calculate_identity(self.reference_sequence)
        
        if self.min_identity is not None and identity < self.min_identity:
            return False
        if self.max_identity is not None and identity > self.max_identity:
            return False
        
        return True


class CompositionFilter(BaseFilter):
    """Filter sequences by amino acid composition."""
    
    def __init__(self, 
                 min_amino_acid_fraction: float = 0.5,
                 forbidden_chars: Optional[str] = None,
                 config: Optional[FilterConfig] = None):
        """
        Initialize composition filter.
        
        Parameters
        ----------
        min_amino_acid_fraction : float
            Minimum fraction of valid amino acids (excluding gaps, X)
        forbidden_chars : Optional[str]
            Characters that make sequence invalid
        config : Optional[FilterConfig]
            Filter configuration
        """
        super().__init__(config)
        self.min_amino_acid_fraction = min_amino_acid_fraction
        self.forbidden_chars = set(forbidden_chars) if forbidden_chars else set()
    
    def apply(self, msa: MSACore) -> MSACore:
        """Apply composition filter to MSA."""
        filtered_sequences = []
        
        for seq in msa.sequences:
            # Always keep query if requested
            if self.config.keep_query and seq.is_query:
                filtered_sequences.append(seq)
                continue
            
            if self.test_sequence(seq):
                filtered_sequences.append(seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=msa.chain_poly_type
        )
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """Test if sequence passes composition filter."""
        seq_str = sequence.sequence
        
        # Check for forbidden characters
        if self.forbidden_chars and any(char in self.forbidden_chars for char in seq_str):
            return False
        
        # Count valid amino acids
        valid_amino_acids = set("ACDEFGHIKLMNPQRSTVWY")
        valid_count = sum(1 for char in seq_str if char in valid_amino_acids)
        
        # Calculate fraction
        if len(seq_str) == 0:
            return False
        
        fraction = valid_count / len(seq_str)
        return fraction >= self.min_amino_acid_fraction


class ConservationFilter(BaseFilter):
    """Filter sequences based on conservation at specific positions."""
    
    def __init__(self, 
                 conserved_positions: List[int],
                 allowed_chars: Optional[Dict[int, str]] = None,
                 config: Optional[FilterConfig] = None):
        """
        Initialize conservation filter.
        
        Parameters
        ----------
        conserved_positions : List[int]
            Positions that must be conserved (0-indexed)
        allowed_chars : Optional[Dict[int, str]]
            Allowed characters at each position
        config : Optional[FilterConfig]
            Filter configuration
        """
        super().__init__(config)
        self.conserved_positions = conserved_positions
        self.allowed_chars = allowed_chars or {}
    
    def apply(self, msa: MSACore) -> MSACore:
        """Apply conservation filter to MSA."""
        if not msa.sequences or not self.conserved_positions:
            return msa
        
        # Use query sequence as reference for conservation
        query = msa.query_sequence
        if query is None:
            logging.warning("No query sequence for conservation filter")
            return msa
        
        filtered_sequences = []
        
        for seq in msa.sequences:
            # Always keep query if requested
            if self.config.keep_query and seq.is_query:
                filtered_sequences.append(seq)
                continue
            
            if self.test_sequence_against_reference(seq, query):
                filtered_sequences.append(seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=msa.chain_poly_type
        )
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """Test sequence (needs reference for conservation)."""
        # Cannot test without reference sequence
        return True
    
    def test_sequence_against_reference(self, 
                                      sequence: Sequence, 
                                      reference: Sequence) -> bool:
        """Test sequence conservation against reference."""
        for pos in self.conserved_positions:
            if pos >= len(sequence.sequence) or pos >= len(reference.sequence):
                continue
            
            seq_char = sequence.sequence[pos]
            ref_char = reference.sequence[pos]
            
            # Check if position is conserved
            if pos in self.allowed_chars:
                # Use specific allowed characters
                if seq_char not in self.allowed_chars[pos]:
                    return False
            else:
                # Must match reference
                if seq_char != ref_char:
                    return False
        
        return True


class CustomFilter(BaseFilter):
    """Custom filter using user-defined function."""
    
    def __init__(self, 
                 filter_function: Callable[[Sequence], bool],
                 name: str = "custom",
                 config: Optional[FilterConfig] = None):
        """
        Initialize custom filter.
        
        Parameters
        ----------
        filter_function : Callable[[Sequence], bool]
            Function that returns True if sequence should be kept
        name : str
            Name for logging purposes
        config : Optional[FilterConfig]
            Filter configuration
        """
        super().__init__(config)
        self.filter_function = filter_function
        self.name = name
    
    def apply(self, msa: MSACore) -> MSACore:
        """Apply custom filter to MSA."""
        filtered_sequences = []
        
        for seq in msa.sequences:
            # Always keep query if requested
            if self.config.keep_query and seq.is_query:
                filtered_sequences.append(seq)
                continue
            
            if self.test_sequence(seq):
                filtered_sequences.append(seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=msa.chain_poly_type
        )
    
    def test_sequence(self, sequence: Sequence) -> bool:
        """Test if sequence passes custom filter."""
        try:
            return self.filter_function(sequence)
        except Exception as e:
            logging.warning(f"Custom filter '{self.name}' failed for sequence "
                          f"{sequence.description}: {e}")
            return False


class MSAFilter:
    """
    High-level MSA filtering interface.
    
    This class provides convenient methods for common filtering operations
    and can combine multiple filters efficiently.
    """
    
    def __init__(self):
        """Initialize MSA filter."""
        self.sequence_filter = SequenceFilter()
    
    def by_length(self, 
                  min_length: Optional[int] = None,
                  max_length: Optional[int] = None,
                  keep_query: bool = True) -> 'MSAFilter':
        """
        Add length filter.
        
        Note: Length filtering is disabled by default in preprocessing.
        This method is maintained for custom filtering needs.
        
        Parameters
        ----------
        min_length : Optional[int]
            Minimum sequence length
        max_length : Optional[int]
            Maximum sequence length
        keep_query : bool
            Whether to always keep query sequence
            
        Returns
        -------
        MSAFilter
            Self for method chaining
        """
        config = FilterConfig(keep_query=keep_query)
        self.sequence_filter.add_filter(LengthFilter(min_length, max_length, config))
        return self
    
    def by_gap_content(self, 
                      max_gap_content: float,
                      keep_query: bool = True) -> 'MSAFilter':
        """
        Add gap content filter.
        
        Parameters
        ----------
        max_gap_content : float
            Maximum allowed gap content (0-1)
        keep_query : bool
            Whether to always keep query sequence
            
        Returns
        -------
        MSAFilter
            Self for method chaining
        """
        config = FilterConfig(keep_query=keep_query)
        self.sequence_filter.add_filter(GapContentFilter(max_gap_content, config))
        return self
    
    def by_identity(self, 
                   min_identity: Optional[float] = None,
                   max_identity: Optional[float] = None,
                   keep_query: bool = True) -> 'MSAFilter':
        """
        Add identity filter.
        
        Parameters
        ----------
        min_identity : Optional[float]
            Minimum identity to query (0-1)
        max_identity : Optional[float]
            Maximum identity to query (0-1)
        keep_query : bool
            Whether to always keep query sequence
            
        Returns
        -------
        MSAFilter
            Self for method chaining
        """
        config = FilterConfig(keep_query=keep_query)
        self.sequence_filter.add_filter(IdentityFilter(
            min_identity, max_identity, None, config))
        return self
    
    def by_composition(self, 
                      min_amino_acid_fraction: float = 0.5,
                      forbidden_chars: Optional[str] = None,
                      keep_query: bool = True) -> 'MSAFilter':
        """
        Add composition filter.
        
        Parameters
        ----------
        min_amino_acid_fraction : float
            Minimum fraction of valid amino acids
        forbidden_chars : Optional[str]
            Characters that make sequence invalid
        keep_query : bool
            Whether to always keep query sequence
            
        Returns
        -------
        MSAFilter
            Self for method chaining
        """
        config = FilterConfig(keep_query=keep_query)
        self.sequence_filter.add_filter(CompositionFilter(
            min_amino_acid_fraction, forbidden_chars, config))
        return self
    
    def custom(self, 
              filter_function: Callable[[Sequence], bool],
              name: str = "custom",
              keep_query: bool = True) -> 'MSAFilter':
        """
        Add custom filter.
        
        Parameters
        ----------
        filter_function : Callable[[Sequence], bool]
            Function that returns True if sequence should be kept
        name : str
            Name for logging purposes
        keep_query : bool
            Whether to always keep query sequence
            
        Returns
        -------
        MSAFilter
            Self for method chaining
        """
        config = FilterConfig(keep_query=keep_query)
        self.sequence_filter.add_filter(CustomFilter(filter_function, name, config))
        return self
    
    def apply(self, msa: MSACore) -> MSACore:
        """
        Apply all filters to MSA.
        
        Parameters
        ----------
        msa : MSACore
            Input MSA
            
        Returns
        -------
        MSACore
            Filtered MSA
        """
        initial_count = len(msa)
        filtered_msa = self.sequence_filter.apply(msa)
        final_count = len(filtered_msa)
        
        logging.info(f"Filtering completed: {initial_count} -> {final_count} sequences "
                    f"({initial_count - final_count} removed)")
        
        return filtered_msa 