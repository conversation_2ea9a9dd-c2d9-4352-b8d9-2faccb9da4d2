"""
MSA postprocessing utilities.

This module provides utilities for processing analysis results,
calculating metrics, and formatting outputs.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

from ..core.msa_core import MS<PERSON>ore
from ..core.sequence import Sequence
from ..core.data_structures import ClusteringResultData


@dataclass
class PostprocessingConfig:
    """Configuration for MSA postprocessing."""
    # Output formatting
    output_format: str = "tsv"  # tsv, csv, json
    include_metadata: bool = True
    include_statistics: bool = True
    
    # Clustering result processing
    filter_noise: bool = True
    min_cluster_size: int = 2
    
    # Metrics calculation
    calculate_diversity_metrics: bool = True
    calculate_conservation_metrics: bool = True
    
    # File output
    save_intermediate_results: bool = False
    output_directory: Optional[str] = None


class MSAPostprocessor:
    """
    MSA postprocessor for analysis results.
    
    This class handles processing of analysis results, metrics calculation,
    and output formatting for various MSA analysis workflows.
    """
    
    def __init__(self, config: Optional[PostprocessingConfig] = None):
        """
        Initialize the postprocessor.
        
        Parameters
        ----------
        config : Optional[PostprocessingConfig]
            Postprocessing configuration
        """
        self.config = config or PostprocessingConfig()
        self._results = {}
    
    def process_clustering_results(self, 
                                 clustering_result: ClusteringResultData,
                                 msa: MSACore) -> Dict[str, Any]:
        """
        Process clustering results and calculate metrics.
        
        Parameters
        ----------
        clustering_result : ClusteringResultData
            Clustering results to process
        msa : MSACore
            Original MSA data
            
        Returns
        -------
        Dict[str, Any]
            Processed clustering results with metrics
        """
        logging.info(f"Processing clustering results from {clustering_result.algorithm_name}")
        
        # Basic cluster statistics
        cluster_stats = self._calculate_cluster_statistics(clustering_result)
        
        # Sequence assignments
        assignments = self._create_sequence_assignments(clustering_result, msa)
        
        # Quality metrics
        quality_metrics = self._calculate_clustering_quality(clustering_result, msa)
        
        # Diversity metrics if requested
        diversity_metrics = {}
        if self.config.calculate_diversity_metrics:
            diversity_metrics = self._calculate_diversity_metrics(clustering_result, msa)
        
        processed_results = {
            'algorithm': clustering_result.algorithm_name,
            'parameters': clustering_result.algorithm_params,
            'cluster_statistics': cluster_stats,
            'sequence_assignments': assignments,
            'quality_metrics': quality_metrics,
            'diversity_metrics': diversity_metrics,
            'metadata': clustering_result.metadata
        }
        
        # Store results
        self._results['clustering'] = processed_results
        
        return processed_results
    
    def _calculate_cluster_statistics(self, 
                                    clustering_result: ClusteringResultData) -> Dict[str, Any]:
        """Calculate basic cluster statistics."""
        cluster_sizes = clustering_result.get_cluster_sizes()
        
        stats = {
            'n_clusters': clustering_result.n_clusters,
            'n_noise': clustering_result.n_noise,
            'total_points': clustering_result.total_points,
            'noise_percentage': clustering_result.noise_percentage,
            'cluster_sizes': cluster_sizes
        }
        
        if cluster_sizes:
            sizes = list(cluster_sizes.values())
            stats.update({
                'mean_cluster_size': np.mean(sizes),
                'median_cluster_size': np.median(sizes),
                'std_cluster_size': np.std(sizes),
                'min_cluster_size': min(sizes),
                'max_cluster_size': max(sizes)
            })
        
        return stats
    
    def _create_sequence_assignments(self, 
                                   clustering_result: ClusteringResultData,
                                   msa: MSACore) -> List[Dict[str, Any]]:
        """Create sequence-to-cluster assignments."""
        assignments = []
        
        for i, (seq, label) in enumerate(zip(msa.sequences, clustering_result.cluster_labels)):
            assignment = {
                'sequence_index': i,
                'sequence_description': seq.description,
                'cluster_label': label,
                'is_noise': label == -1,
                'is_query': seq.is_query,
                'sequence_length': seq.length,
                'gap_content': seq.gap_content
            }
            
            # Add cluster-specific information
            if label >= 0:
                cluster_sizes = clustering_result.get_cluster_sizes()
                assignment['cluster_size'] = cluster_sizes.get(label, 0)
            
            assignments.append(assignment)
        
        return assignments
    
    def _calculate_clustering_quality(self, 
                                    clustering_result: ClusteringResultData,
                                    msa: MSACore) -> Dict[str, float]:
        """Calculate clustering quality metrics."""
        quality_metrics = {}
        
        # Silhouette score if available
        if clustering_result.silhouette_score is not None:
            quality_metrics['silhouette_score'] = clustering_result.silhouette_score
        
        # Cluster cohesion (average intra-cluster identity)
        if clustering_result.n_clusters > 0:
            intra_cluster_identities = self._calculate_intra_cluster_identity(
                clustering_result, msa
            )
            quality_metrics['mean_intra_cluster_identity'] = np.mean(intra_cluster_identities)
            quality_metrics['std_intra_cluster_identity'] = np.std(intra_cluster_identities)
        
        # Inter-cluster separation (average inter-cluster identity)
        if clustering_result.n_clusters > 1:
            inter_cluster_identity = self._calculate_inter_cluster_identity(
                clustering_result, msa
            )
            quality_metrics['mean_inter_cluster_identity'] = inter_cluster_identity
        
        return quality_metrics
    
    def _calculate_intra_cluster_identity(self, 
                                        clustering_result: ClusteringResultData,
                                        msa: MSACore) -> List[float]:
        """Calculate average identity within each cluster."""
        cluster_identities = []
        
        # Group sequences by cluster
        clusters = {}
        for i, label in enumerate(clustering_result.cluster_labels):
            if label >= 0:  # Exclude noise
                if label not in clusters:
                    clusters[label] = []
                clusters[label].append(i)
        
        # Calculate intra-cluster identities
        for cluster_id, seq_indices in clusters.items():
            if len(seq_indices) < 2:
                continue
            
            identities = []
            sequences = [msa.sequences[i] for i in seq_indices]
            
            # Pairwise identities within cluster
            for i in range(len(sequences)):
                for j in range(i + 1, len(sequences)):
                    identity = sequences[i].calculate_identity(sequences[j])
                    identities.append(identity)
            
            if identities:
                cluster_identities.append(np.mean(identities))
        
        return cluster_identities
    
    def _calculate_inter_cluster_identity(self, 
                                        clustering_result: ClusteringResultData,
                                        msa: MSACore) -> float:
        """Calculate average identity between different clusters."""
        # Group sequences by cluster
        clusters = {}
        for i, label in enumerate(clustering_result.cluster_labels):
            if label >= 0:  # Exclude noise
                if label not in clusters:
                    clusters[label] = []
                clusters[label].append(i)
        
        inter_identities = []
        cluster_ids = list(clusters.keys())
        
        # Calculate inter-cluster identities
        for i in range(len(cluster_ids)):
            for j in range(i + 1, len(cluster_ids)):
                cluster1_seqs = [msa.sequences[idx] for idx in clusters[cluster_ids[i]]]
                cluster2_seqs = [msa.sequences[idx] for idx in clusters[cluster_ids[j]]]
                
                # Sample sequences for efficiency if clusters are large
                if len(cluster1_seqs) > 10:
                    cluster1_seqs = np.random.choice(cluster1_seqs, 10, replace=False)
                if len(cluster2_seqs) > 10:
                    cluster2_seqs = np.random.choice(cluster2_seqs, 10, replace=False)
                
                # Calculate pairwise identities between clusters
                for seq1 in cluster1_seqs:
                    for seq2 in cluster2_seqs:
                        identity = seq1.calculate_identity(seq2)
                        inter_identities.append(identity)
        
        return np.mean(inter_identities) if inter_identities else 0.0
    
    def _calculate_diversity_metrics(self, 
                                   clustering_result: ClusteringResultData,
                                   msa: MSACore) -> Dict[str, Any]:
        """Calculate sequence diversity metrics."""
        diversity_metrics = {}
        
        # Overall sequence diversity
        all_identities = []
        sequences = msa.sequences
        
        # Sample sequences for efficiency if MSA is large
        if len(sequences) > 100:
            sample_size = min(100, len(sequences))
            sequences = np.random.choice(sequences, sample_size, replace=False)
        
        # Calculate pairwise identities
        for i in range(len(sequences)):
            for j in range(i + 1, len(sequences)):
                identity = sequences[i].calculate_identity(sequences[j])
                all_identities.append(identity)
        
        if all_identities:
            diversity_metrics.update({
                'mean_pairwise_identity': np.mean(all_identities),
                'std_pairwise_identity': np.std(all_identities),
                'min_pairwise_identity': np.min(all_identities),
                'max_pairwise_identity': np.max(all_identities)
            })
        
        # Effective number of sequences (based on entropy)
        effective_sequences = self._calculate_effective_sequences(msa)
        diversity_metrics['effective_sequences'] = effective_sequences
        
        return diversity_metrics
    
    def _calculate_effective_sequences(self, msa: MSACore) -> float:
        """Calculate effective number of sequences based on sequence entropy."""
        if not msa.sequences:
            return 0.0
        
        # Calculate position-wise entropy
        max_length = msa.max_length
        entropies = []
        
        for pos in range(max_length):
            char_counts = {}
            total_valid = 0
            
            for seq in msa.sequences:
                if pos < len(seq.sequence):
                    char = seq.sequence[pos]
                    if char not in '-.':  # Exclude gaps
                        char_counts[char] = char_counts.get(char, 0) + 1
                        total_valid += 1
            
            # Calculate entropy for this position
            if total_valid > 0:
                entropy = 0.0
                for count in char_counts.values():
                    p = count / total_valid
                    if p > 0:
                        entropy -= p * np.log2(p)
                entropies.append(entropy)
        
        # Average entropy across positions
        avg_entropy = np.mean(entropies) if entropies else 0.0
        
        # Convert to effective number of sequences
        # This is a rough approximation
        effective_sequences = 2 ** avg_entropy * len(msa.sequences) / 20  # 20 amino acids
        
        return min(effective_sequences, len(msa.sequences))
    
    def format_results(self, 
                      results: Dict[str, Any],
                      output_format: Optional[str] = None) -> str:
        """
        Format results for output.
        
        Parameters
        ----------
        results : Dict[str, Any]
            Results to format
        output_format : Optional[str]
            Output format (tsv, csv, json)
            
        Returns
        -------
        str
            Formatted results string
        """
        format_type = output_format or self.config.output_format
        
        if format_type.lower() == "json":
            import json
            return json.dumps(results, indent=2, default=str)
        
        elif format_type.lower() in ["tsv", "csv"]:
            delimiter = "\t" if format_type.lower() == "tsv" else ","
            return self._format_tabular(results, delimiter)
        
        else:
            raise ValueError(f"Unsupported output format: {format_type}")
    
    def _format_tabular(self, results: Dict[str, Any], delimiter: str) -> str:
        """Format results as tabular data."""
        lines = []
        
        # Header
        if 'sequence_assignments' in results:
            assignments = results['sequence_assignments']
            if assignments:
                # Use first assignment to determine columns
                columns = list(assignments[0].keys())
                lines.append(delimiter.join(columns))
                
                # Data rows
                for assignment in assignments:
                    row = [str(assignment.get(col, '')) for col in columns]
                    lines.append(delimiter.join(row))
        
        return '\n'.join(lines)
    
    def save_results(self, 
                    results: Dict[str, Any],
                    output_path: Union[str, Path],
                    format_type: Optional[str] = None) -> None:
        """
        Save results to file.
        
        Parameters
        ----------
        results : Dict[str, Any]
            Results to save
        output_path : Union[str, Path]
            Output file path
        format_type : Optional[str]
            Output format
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        formatted_results = self.format_results(results, format_type)
        
        with open(output_path, 'w') as f:
            f.write(formatted_results)
        
        logging.info(f"Results saved to {output_path}")
    
    def generate_summary_report(self, 
                              results: Dict[str, Any]) -> str:
        """
        Generate a summary report of the results.
        
        Parameters
        ----------
        results : Dict[str, Any]
            Results to summarize
            
        Returns
        -------
        str
            Summary report text
        """
        report_lines = []
        
        # Header
        report_lines.append("MSA Analysis Summary Report")
        report_lines.append("=" * 40)
        report_lines.append("")
        
        # Clustering results
        if 'clustering' in results:
            clustering = results['clustering']
            report_lines.append("Clustering Results:")
            report_lines.append(f"  Algorithm: {clustering['algorithm']}")
            
            stats = clustering['cluster_statistics']
            report_lines.append(f"  Number of clusters: {stats['n_clusters']}")
            report_lines.append(f"  Noise points: {stats['n_noise']} ({stats['noise_percentage']:.1f}%)")
            
            if 'mean_cluster_size' in stats:
                report_lines.append(f"  Average cluster size: {stats['mean_cluster_size']:.1f}")
                report_lines.append(f"  Largest cluster: {stats['max_cluster_size']}")
            
            # Quality metrics
            if 'quality_metrics' in clustering:
                quality = clustering['quality_metrics']
                report_lines.append("  Quality metrics:")
                
                if 'silhouette_score' in quality:
                    report_lines.append(f"    Silhouette score: {quality['silhouette_score']:.3f}")
                
                if 'mean_intra_cluster_identity' in quality:
                    report_lines.append(f"    Intra-cluster identity: {quality['mean_intra_cluster_identity']:.3f}")
                
                if 'mean_inter_cluster_identity' in quality:
                    report_lines.append(f"    Inter-cluster identity: {quality['mean_inter_cluster_identity']:.3f}")
            
            report_lines.append("")
        
        # Diversity metrics
        if 'diversity_metrics' in results.get('clustering', {}):
            diversity = results['clustering']['diversity_metrics']
            report_lines.append("Diversity Metrics:")
            
            if 'mean_pairwise_identity' in diversity:
                report_lines.append(f"  Mean pairwise identity: {diversity['mean_pairwise_identity']:.3f}")
            
            if 'effective_sequences' in diversity:
                report_lines.append(f"  Effective sequences: {diversity['effective_sequences']:.1f}")
            
            report_lines.append("")
        
        return '\n'.join(report_lines)
    
    def get_processed_results(self) -> Dict[str, Any]:
        """Get all processed results."""
        return self._results.copy()
    
    def clear_results(self) -> None:
        """Clear stored results."""
        self._results.clear()
    
    @staticmethod
    def create_default_config(**kwargs) -> PostprocessingConfig:
        """
        Create a default postprocessing configuration.
        
        Parameters
        ----------
        **kwargs
            Override default configuration parameters
            
        Returns
        -------
        PostprocessingConfig
            Default configuration with any overrides applied
        """
        config = PostprocessingConfig()
        
        # Apply any overrides
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                logging.warning(f"Unknown configuration parameter: {key}")
        
        return config 