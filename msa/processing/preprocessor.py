"""
MSA preprocessing utilities.

This module consolidates preprocessing logic from various parts of the codebase,
providing a unified interface for MSA data preprocessing.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tuple, Union
from dataclasses import dataclass

from ..core.msa_core import <PERSON><PERSON><PERSON>
from ..core.sequence import Sequence
from ..core.data_structures import ChainPolyType


@dataclass
class PreprocessingConfig:
    """Configuration for MSA preprocessing."""
    # Length filtering - PERMANENTLY DISABLED as per requirements
    # min_length and max_length parameters have been removed
    
    # Gap content filtering - Changed from 0.8 to 0.4 (40% threshold)
    max_gap_content: float = 0.4
    
    # Deduplication - Changed from 0.95 to 1.0 (only remove 100% identical)
    deduplicate: bool = True
    identity_threshold: float = 1.0
    
    # Sequence cleaning - PRESERVE lowercase for clustering workflow
    remove_lowercase: bool = False  # Keep lowercase letters (insertions)
    remove_special_chars: bool = True
    standardize_gaps: bool = True
    
    # Conservation filtering
    min_conservation_score: Optional[float] = None
    
    # Query sequence handling
    keep_query: bool = True
    
    # Maximum number of sequences
    max_sequences: Optional[int] = None


class MSAPreprocessor:
    """
    Unified MSA preprocessor.
    
    This class consolidates preprocessing functionality previously scattered
    across multiple modules, providing a clean interface for MSA preparation.
    """
    
    def __init__(self, config: Optional[PreprocessingConfig] = None):
        """
        Initialize the preprocessor.
        
        Parameters
        ----------
        config : Optional[PreprocessingConfig]
            Preprocessing configuration
        """
        self.config = config or PreprocessingConfig()
        self._processing_stats = {}
    
    def preprocess(self, msa: MSACore) -> MSACore:
        """
        Apply full preprocessing pipeline to MSA.
        
        Parameters
        ----------
        msa : MSACore
            Input MSA to preprocess
            
        Returns
        -------
        MSACore
            Preprocessed MSA
        """
        if not msa.sequences:
            logging.warning("Empty MSA provided for preprocessing")
            return msa
        
        logging.info(f"Starting preprocessing of MSA with {len(msa)} sequences")
        
        # Initialize stats
        self._processing_stats = {
            'initial_sequences': len(msa),
            'initial_max_length': msa.max_length
        }
        
        processed_msa = msa
        
        # Step 1: Clean sequences
        if (self.config.remove_lowercase or 
            self.config.remove_special_chars or 
            self.config.standardize_gaps):
            processed_msa = self._clean_sequences(processed_msa)
        
        # Step 2: Length filtering - PERMANENTLY DISABLED
        # Length filtering has been removed as per requirements
        
        # Step 3: Filter by gap content
        if self.config.max_gap_content < 1.0:
            processed_msa = self._filter_by_gap_content(processed_msa)
        
        # Step 4: Deduplicate sequences
        if self.config.deduplicate:
            processed_msa = self._deduplicate_sequences(processed_msa)
        
        # Step 5: Limit number of sequences
        if self.config.max_sequences:
            processed_msa = self._limit_sequences(processed_msa)
        
        # Update final stats
        self._processing_stats.update({
            'final_sequences': len(processed_msa),
            'final_max_length': processed_msa.max_length,
            'sequences_removed': self._processing_stats['initial_sequences'] - len(processed_msa)
        })
        
        # Add processing notes to metadata
        if processed_msa.metadata:
            processed_msa.metadata.add_processing_note(
                f"Preprocessed: {self._processing_stats['sequences_removed']} sequences removed"
            )
        
        logging.info(f"Preprocessing completed. {len(processed_msa)} sequences remain")
        
        return processed_msa
    
    def _clean_sequences(self, msa: MSACore) -> MSACore:
        """Clean sequences by removing unwanted characters."""
        logging.debug("Cleaning sequences")
        
        cleaned_sequences = []
        
        for seq in msa.sequences:
            cleaned_seq = seq.sequence
            
            # Remove lowercase if requested
            if self.config.remove_lowercase:
                cleaned_seq = ''.join(char for char in cleaned_seq if not char.islower())
            
            # Remove special characters if requested
            if self.config.remove_special_chars:
                # Keep amino acids, gaps, and X
                valid_chars = set("ACDEFGHIKLMNPQRSTVWY-.*X")
                cleaned_seq = ''.join(char for char in cleaned_seq if char.upper() in valid_chars)
            
            # Standardize gaps
            if self.config.standardize_gaps:
                cleaned_seq = cleaned_seq.replace('.', '-')
            
            # Create new sequence with cleaned sequence
            if cleaned_seq != seq.sequence:
                new_seq = Sequence(
                    sequence=cleaned_seq,
                    description=seq.description,
                    index=seq.index,
                    is_query=seq.is_query,
                    metadata=seq.metadata.copy()
                )
                cleaned_sequences.append(new_seq)
            else:
                cleaned_sequences.append(seq)
        
        return MSACore(
            sequences=cleaned_sequences,
            chain_poly_type=msa.chain_poly_type
        )
    
    # Length filtering method removed - permanently disabled as per requirements
    
    def _filter_by_gap_content(self, msa: MSACore) -> MSACore:
        """Filter sequences by gap content."""
        logging.debug(f"Filtering by gap content: max={self.config.max_gap_content}")
        
        return msa.filter_by_gap_content(
            max_gap_content=self.config.max_gap_content,
            keep_query=self.config.keep_query
        )
    
    def _deduplicate_sequences(self, msa: MSACore) -> MSACore:
        """Remove duplicate sequences."""
        logging.debug(f"Deduplicating with threshold {self.config.identity_threshold}")
        
        return msa.deduplicate(identity_threshold=self.config.identity_threshold)
    
    def _limit_sequences(self, msa: MSACore) -> MSACore:
        """Limit the number of sequences."""
        if len(msa) <= self.config.max_sequences:
            return msa
        
        logging.debug(f"Limiting sequences to {self.config.max_sequences}")
        
        # Keep query sequence if requested
        sequences_to_keep = []
        if self.config.keep_query and msa.query_sequence:
            sequences_to_keep.append(msa.query_sequence)
            remaining_slots = self.config.max_sequences - 1
        else:
            remaining_slots = self.config.max_sequences
        
        # Add other sequences up to the limit
        for seq in msa.sequences:
            if len(sequences_to_keep) >= self.config.max_sequences:
                break
            
            if not (self.config.keep_query and seq.is_query):
                sequences_to_keep.append(seq)
                remaining_slots -= 1
        
        return MSACore(
            sequences=sequences_to_keep,
            chain_poly_type=msa.chain_poly_type
        )
    
    def calculate_conservation_scores(self, msa: MSACore) -> List[float]:
        """
        Calculate conservation scores for each position.
        
        Parameters
        ----------
        msa : MSACore
            MSA to analyze
            
        Returns
        -------
        List[float]
            Conservation scores for each position (0-1)
        """
        if not msa.sequences:
            return []
        
        max_length = msa.max_length
        conservation_scores = []
        
        for pos in range(max_length):
            # Count characters at this position
            char_counts = {}
            valid_sequences = 0
            
            for seq in msa.sequences:
                if pos < len(seq.sequence):
                    char = seq.sequence[pos]
                    if char not in '-.':  # Ignore gaps
                        char_counts[char] = char_counts.get(char, 0) + 1
                        valid_sequences += 1
            
            # Calculate conservation (most frequent character frequency)
            if valid_sequences > 0:
                max_count = max(char_counts.values()) if char_counts else 0
                conservation = max_count / valid_sequences
            else:
                conservation = 0.0
            
            conservation_scores.append(conservation)
        
        return conservation_scores
    
    def filter_by_conservation(self, 
                             msa: MSACore, 
                             min_conservation: float) -> MSACore:
        """
        Filter MSA positions by conservation score.
        
        Parameters
        ----------
        msa : MSACore
            Input MSA
        min_conservation : float
            Minimum conservation score to keep position
            
        Returns
        -------
        MSACore
            MSA with filtered positions
        """
        conservation_scores = self.calculate_conservation_scores(msa)
        
        # Find positions to keep
        positions_to_keep = [
            i for i, score in enumerate(conservation_scores)
            if score >= min_conservation
        ]
        
        if not positions_to_keep:
            logging.warning("No positions meet conservation threshold")
            return MSACore(chain_poly_type=msa.chain_poly_type)
        
        # Create filtered sequences
        filtered_sequences = []
        for seq in msa.sequences:
            filtered_seq = ''.join(
                seq.sequence[i] if i < len(seq.sequence) else '-'
                for i in positions_to_keep
            )
            
            new_seq = Sequence(
                sequence=filtered_seq,
                description=seq.description,
                index=seq.index,
                is_query=seq.is_query,
                metadata=seq.metadata.copy()
            )
            filtered_sequences.append(new_seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=msa.chain_poly_type
        )
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get preprocessing statistics."""
        return self._processing_stats.copy()
    
    @staticmethod
    def create_default_config(**kwargs) -> PreprocessingConfig:
        """
        Create a default preprocessing configuration.
        
        Parameters
        ----------
        **kwargs
            Override default configuration parameters
            
        Returns
        -------
        PreprocessingConfig
            Default configuration with any overrides applied
        """
        config = PreprocessingConfig()
        
        # Apply any overrides
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                logging.warning(f"Unknown configuration parameter: {key}")
        
        return config
    
    @staticmethod
    def create_strict_config() -> PreprocessingConfig:
        """Create a strict preprocessing configuration."""
        return PreprocessingConfig(
            max_gap_content=0.5,
            deduplicate=True,
            identity_threshold=0.90,
            remove_lowercase=True,
            remove_special_chars=True,
            standardize_gaps=True,
            min_conservation_score=0.1
        )
    
    @staticmethod
    def create_lenient_config() -> PreprocessingConfig:
        """Create a lenient preprocessing configuration."""
        return PreprocessingConfig(
            max_gap_content=0.95,
            deduplicate=True,
            identity_threshold=0.99,
            remove_lowercase=False,
            remove_special_chars=False,
            standardize_gaps=True
        ) 