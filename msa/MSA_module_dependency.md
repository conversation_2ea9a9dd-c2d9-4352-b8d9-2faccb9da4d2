graph TB
    %% Core Data Structures
    MSACore[MSACore<br/>핵심 MSA 데이터 관리]
    Sequence[Sequence<br/>개별 시퀀스 관리]
    DataStructures[SequenceData & MSAMetadata<br/>데이터 구조체]
    
    %% I/O Components
    MSAReader[MSAReader<br/>파일 읽기]
    MSAWriter[MSAWriter<br/>파일 쓰기]
    FormatConverter[FormatConverter<br/>포맷 변환]
    FormatDetector[FormatDetector<br/>포맷 자동 감지]
    
    %% Encoding Components
    SequenceEncoder[SequenceEncoder<br/>시퀀스 인코딩 관리]
    OneHotEncoder[OneHotEncoder<br/>원핫 인코딩]
    ESMEmbedder[ESMEmbedder<br/>트랜스포머 임베딩]
    EncoderFactory[EncoderFactory<br/>인코더 팩토리]
    
    %% Processing Components
    MSAPreprocessor[MSAPreprocessor<br/>전처리]
    MSAFilter[MSAFilter<br/>필터링]
    MSAPostprocessor[MSAPostprocessor<br/>후처리]
    
    %% Analysis Components
    MSAStatistics[MSAStatistics<br/>통계 분석]
    MSAMetrics[MSAMetrics<br/>품질 메트릭]
    DiversityAnalyzer[DiversityAnalyzer<br/>다양성 분석]
    ComparativeAnalyzer[ComparativeAnalyzer<br/>비교 분석]
    
    %% Clustering Components
    ClusteringManager[ClusteringManager<br/>클러스터링 관리]
    ClusteringFactory[ClusteringFactory<br/>알고리즘 팩토리]
    DBSCANClusterer[DBSCANClusterer<br/>DBSCAN 알고리즘]
    HierarchicalClusterer[HierarchicalClusterer<br/>계층적 클러스터링]
    ClusteringResult[ClusteringResult<br/>클러스터링 결과]
    
    %% Visualization Components
    MSAPlotter[MSAPlotter<br/>기본 플롯]
    MSACharts[MSACharts<br/>차트 생성]
    ClusteringVisualizer[ClusteringVisualizer<br/>클러스터링 시각화]
    VisualizationConfig[VisualizationConfig<br/>시각화 설정]
    
    %% Core Dependencies
    MSACore --> Sequence
    MSACore --> DataStructures
    Sequence --> DataStructures
    
    %% I/O Dependencies
    MSAReader --> MSACore
    MSAReader --> FormatDetector
    MSAWriter --> MSACore
    FormatConverter --> MSAReader
    FormatConverter --> MSAWriter
    
    %% Encoding Dependencies
    SequenceEncoder --> EncoderFactory
    EncoderFactory --> OneHotEncoder
    EncoderFactory --> ESMEmbedder
    
    %% Processing Dependencies
    MSAPreprocessor --> MSACore
    MSAPreprocessor --> MSAFilter
    MSAFilter --> MSACore
    MSAPostprocessor --> ClusteringResult
    
    %% Analysis Dependencies
    MSAStatistics --> MSACore
    MSAMetrics --> MSACore
    DiversityAnalyzer --> MSACore
    ComparativeAnalyzer --> MSACore
    ComparativeAnalyzer --> MSAStatistics
    ComparativeAnalyzer --> MSAMetrics
    
    %% Clustering Dependencies
    ClusteringManager --> MSACore
    ClusteringManager --> ClusteringFactory
    ClusteringFactory --> DBSCANClusterer
    ClusteringFactory --> HierarchicalClusterer
    DBSCANClusterer --> ClusteringResult
    HierarchicalClusterer --> ClusteringResult
    ClusteringManager --> SequenceEncoder
    
    %% Visualization Dependencies
    MSAPlotter --> MSACore
    MSAPlotter --> VisualizationConfig
    MSACharts --> MSACore
    MSACharts --> MSAStatistics
    ClusteringVisualizer --> ClusteringResult
    ClusteringVisualizer --> SequenceEncoder
    
    %% Main Workflow
    classDef coreClass fill:#e1f5fe
    classDef ioClass fill:#f3e5f5
    classDef encClass fill:#e8f5e8
    classDef procClass fill:#fff3e0
    classDef analysisClass fill:#fce4ec
    classDef clusterClass fill:#e0f2f1
    classDef vizClass fill:#f9fbe7
    
    class MSACore,Sequence,DataStructures coreClass
    class MSAReader,MSAWriter,FormatConverter,FormatDetector ioClass
    class SequenceEncoder,OneHotEncoder,ESMEmbedder,EncoderFactory encClass
    class MSAPreprocessor,MSAFilter,MSAPostprocessor procClass
    class MSAStatistics,MSAMetrics,DiversityAnalyzer,ComparativeAnalyzer analysisClass
    class ClusteringManager,ClusteringFactory,DBSCANClusterer,HierarchicalClusterer,ClusteringResult clusterClass
    class MSAPlotter,MSACharts,ClusteringVisualizer,VisualizationConfig vizClass