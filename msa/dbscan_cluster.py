import os, sys
import logging
import numpy as np
import pandas as pd

from dataclasses import dataclass
from typing import List, Tuple, Dict, Any, Optional, Sequence, Literal, Union

import torch
from sklearn.cluster import DBSCAN
try:
    if torch.cuda.is_available():
        from cuml import DBSCAN 
    print("cuML's DBSCAN imported successfully")
except ImportError:
    print("cuML is not installed. Using scikit-learn's DBSCAN instead.")
    cuml_DBSCAN = None

try:
    from .msa_pipeline import MSA
except ImportError:
    # Fallback for different import contexts
    from msa_pipeline import MSA


# Implementation of AF_cluster reported in "Predicting multiple conformations via sequence clustering and AlphaFold2"

@dataclass
class ClusteringResult:
    """
    Data class to store clustering results from any clustering algorithm.

    Attributes
    ----------
    clusters : List[MSA]
        List of MSA objects, one for each cluster
    cluster_metadata : pd.DataFrame
        DataFrame with cluster information (cluster_id, size, consensus_sequence, etc.)
    clustering_assignments : pd.DataFrame
        DataFrame with sequence assignments (includes 'cluster_label' column)
    clustering_params : Dict[str, Any]
        Dictionary with clustering parameters and metrics
    algorithm_type : str
        Type of clustering algorithm used ('dbscan' or 'hierarchical')
    """
    clusters: List['MSA']
    cluster_metadata: pd.DataFrame
    clustering_assignments: pd.DataFrame
    clustering_params: Dict[str, Any]
    algorithm_type: str


class DBSCANClusterMSA(MSA):
    """
    Multiple Sequence Alignment container with clustering capabilities.
    Inherits from MSA class and adds clustering functionality.
    """

    def cluster(self,
        output_dir: Optional[str] = None,
        gap_cutoff: float = 0.25,
        min_eps: float = 3.0,
        max_eps: float = 20.0,
        eps_step: float = 0.5,
        min_samples: int = 3,
        encoding_method: Literal['onehot', 'MSAtransformer'] = 'onehot',
        save_a3m_files: bool = False,
        keyword: str = "cluster",
        remove_lowercase: bool = False,
        remove_special_chars: bool = True,
    ) -> ClusteringResult:
        """
        Clusters sequences in the MSA using DBSCAN algorithm.
        Based on the AF_Cluster/scripts/ClusterMSA.py implementation.

        Parameters
        ----------
        output_dir (Optional[str], optional):
            Directory to save output files. If None, files are not saved.
            Defaults to None.
        gap_cutoff (float, optional):
            Maximum fraction of gaps allowed in a sequence.
            Sequences with more gaps will be removed.
            Defaults to 0.25.
        min_eps (float, optional):
            Minimum epsilon value for DBSCAN parameter optimization.
            Defaults to 3.0.
        max_eps (float, optional):
            Maximum epsilon value for DBSCAN parameter optimization.
            Defaults to 20.0.
        eps_step (float, optional):
            Step size for epsilon values during parameter optimization.
            Defaults to 0.5.
        min_samples (int, optional):
            Minimum number of samples in a neighborhood for a point to be considered a core point.
            Defaults to 3.
        encoding_method (Literal['onehot', 'MSAtransformer'], optional):
            Method to use for encoding sequences.
            Defaults to 'onehot'.
        save_a3m_files (bool, optional):
            If True, saves A3M files for each cluster.
            Defaults to False.
        keyword (str, optional):
            Prefix for output files.
            Defaults to "cluster".
        remove_lowercase (bool, optional):
            Whether to remove lowercase letters when using MSAtransformer encoding.
            Default is False (preserve lowercase letters).
        remove_special_chars (bool, optional):
            Whether to remove special characters like '.' and '*' when using MSAtransformer encoding.
            Default is True.

        Returns
        -------
        ClusteringResult:
            A ClusteringResult object containing:
            - 'clusters': List of DBSCANClusterMSA objects, one for each cluster
            - 'cluster_metadata': DataFrame with cluster information
            - 'clustering_assignments': DataFrame with sequence assignments
            - 'best_params': Dictionary with best DBSCAN parameters
        """
        # Create output directory if specified
        if output_dir and save_a3m_files:
            os.makedirs(output_dir, exist_ok=True)
            logging.info(f"Created output directory: {output_dir}")

        # Setup logging
        logging.info(f"Starting clustering for MSA with {self.depth} sequences")

        # 1. Filter sequences based on gap content
        original_sequences, filtered_descriptions, filtered_indices = self._filter_sequences(gap_cutoff)
        logging.debug(f"Number of sequences after gap-filtering: {len(self.sequences)} -> {len(original_sequences)}")


        # Create sequences without lowercase for clustering, but keep original sequences
        filtered_sequences = [
            ''.join([ char for char in seq if not char.islower() ])
            for seq in original_sequences
        ]
        if not filtered_sequences:
            logging.warning(f"No sequences remain after gap filtering gap-cutoff={gap_cutoff}")
            logging.warning(f"It is an unexpected situtation with gap-cutoff={gap_cutoff}. Please change gap-cutoff value.")
            sys.exit(1)

        # 2. Create DataFrame for clustering
        df = pd.DataFrame({
            'SequenceName': filtered_descriptions,
            'sequence': filtered_sequences,
            'original_sequence': original_sequences,  # Store original sequences with lowercase [NOTE]
            'original_index': filtered_indices
        })
    
        # Separate query sequence
        # [NOTE] Do not change the order of the following two lines
        query_df = df.iloc[:1].copy()
        df = df.iloc[1:].copy()
        if df.empty:
            logging.warning("No sequences to cluster after removing query")
            # Return the original MSA since we can't cluster
            return ClusteringResult(
                clusters=[self],
                cluster_metadata=pd.DataFrame(),
                clustering_assignments=pd.DataFrame(),
                clustering_params={'eps': None, 'min_samples': None, 'n_clusters': 0},
                algorithm_type='dbscan'
            )

        # 3. Encode sequences for clustering
        seq_lens = set(len(seq) for seq in filtered_sequences)
        assert len(seq_lens) == 1, "All sequences must be of the same length"
        max_seq_len = max(seq_lens)
        
        # Use encode_sequences method based on encoding_method        
        if encoding_method == 'onehot': # use sequence with only uppercase letters
            sequences_to_encode = df.sequence.tolist()
        elif encoding_method == 'MSAtransformer': # preserve original sequences with lowercase
            sequences_to_encode = df.original_sequence.tolist()
        else:
            raise NotImplementedError(f"The input encoding method {encoding_method} is not implemented")
            
        # Use the unified encode_sequences method 
        if encoding_method == 'MSAtransformer':
            # For MSA Transformer, use the _encode_MSAtransformer method directly
            encoded_seqs = self._encode_MSAtransformer(
                sequences=sequences_to_encode,
                max_seq_len=max_seq_len,
                remove_lowercase=remove_lowercase,
                remove_special_chars=remove_special_chars
            )
        else:
            # For other encoding methods, use the standard encode_sequences method
            encoded_seqs = self.encode_sequences(
                sequences=sequences_to_encode,
                max_seq_len=max_seq_len,
                encoding_method=encoding_method
            )
        
        logging.info(f"Encoded sequences shape: {encoded_seqs.shape}")
        
        # 4. Optimize DBSCAN parameters
        best_params = self._optimize_dbscan_parameters(
            encoded_seqs, min_eps, max_eps, eps_step, min_samples
        )

        # 5. Perform clustering with best parameters
        eps_to_use = best_params['eps']
        min_samples_to_use = best_params['min_samples']

        logging.info(f"Performing clustering with eps={eps_to_use}, min_samples={min_samples_to_use}")
        clustering = DBSCAN(eps=eps_to_use, min_samples=min_samples_to_use).fit(encoded_seqs)

        # 6. Add clustering results to DataFrame
        df['cluster_label'] = clustering.labels_

        # 7. Calculate cluster sizes and sort
        cluster_sizes = df['cluster_label'].value_counts().to_dict()
        df['cluster_size'] = df['cluster_label'].map(cluster_sizes)
        df = df.sort_values(by=['cluster_size'], ascending=[False])

        # 8. Generate cluster metadata
        clusters = [x for x in df.cluster_label.unique() if x >= 0]
        unclustered = len(df.loc[df.cluster_label == -1])

        logging.info(f"Found {len(clusters)} clusters, {unclustered} of {len(df)} not clustered ({unclustered/len(df):.2f})")

        # 9. Get sorted clusters by size (excluding noise)
        sorted_clusters = sorted(
            [label for label in cluster_sizes if label != -1],
            key=lambda x: cluster_sizes[x],
            reverse=True
        )

        # 10. Generate cluster metadata and MSA objects for each cluster
        cluster_metadata = []
        cluster_msas = []

        for idx, clust in enumerate(sorted_clusters):
            # Get sequences in this cluster
            tmp = df.loc[df.cluster_label == clust]

            # Generate consensus sequence
            consensus_seq = self._generate_consensus_sequence(tmp.sequence.tolist())

            # Calculate distances using normalized edit distance
            avg_dist_to_consensus = np.mean([
                self._calculate_sequence_similarity(x, consensus_seq) for x in tmp.sequence.tolist()
            ])

            avg_dist_to_query = np.mean([
                self._calculate_sequence_similarity(x, query_df.sequence.iloc[0]) for x in tmp.sequence.tolist()
            ])

            # Create combined DataFrame with query at the top
            combined_df = pd.concat([query_df, tmp], axis=0)

            # Add to metadata
            cluster_metadata.append({
                'cluster_id': clust,
                'cluster_rank': idx,
                'consensus_sequence': consensus_seq,
                'avg_similarity_within_cluster': avg_dist_to_consensus,
                'avg_similarity_to_query': avg_dist_to_query,
                'size': len(tmp)
            })

            # Use original sequences with lowercase letters preserved
            cluster_sequences = combined_df.original_sequence.tolist()
            cluster_descriptions = combined_df.SequenceName.tolist()

            # Ensure chain_poly_type is one of the allowed literal values
            poly_type: Literal['protein', 'rna', 'dna'] = 'protein'
            if self.chain_poly_type in ('protein', 'rna', 'dna'):
                poly_type = self.chain_poly_type  # type: ignore

            # Create DBSCANClusterMSA object for this cluster
            cluster_msa = DBSCANClusterMSA(
                query_sequence=self.query_sequence,
                chain_poly_type=poly_type,
                sequences=cluster_sequences,
                descriptions=cluster_descriptions,
                deduplicated=False  # Already deduplicated during clustering
            )

            cluster_msas.append(cluster_msa)

            # Save A3M file if requested
            if save_a3m_files and output_dir:
                output_filename = os.path.join(output_dir, f"{keyword}_{idx:03d}.a3m")
                with open(output_filename, 'w') as f:
                    f.write(cluster_msa.to_a3m())
                logging.info(f"Wrote cluster {idx} (original label {clust}, size {len(tmp)}) to {output_filename}")

        # 11. Create DataFrames for return
        cluster_metadata_df = pd.DataFrame(cluster_metadata)

        # 12. Return results
        result = ClusteringResult(
            clusters=cluster_msas,
            cluster_metadata=cluster_metadata_df,
            clustering_assignments=df,
            clustering_params={
                'eps': best_params['eps'],
                'min_samples': best_params['min_samples'],
                'n_clusters': best_params['n_clusters'],
                'algorithm_type': 'dbscan'
            },
            algorithm_type='dbscan'
        )

        return result

    @staticmethod
    def _optimize_dbscan_parameters(
        encoded_seqs: np.ndarray,
        min_eps: float=3.0,
        max_eps: float=20.0,
        eps_step: float=0.5,
        min_samples: int=3,
    ) -> Dict[str, Any]:
        """
        Optimizes DBSCAN parameters by testing different epsilon values.

        Parameters
        ----------
        encoded_seqs (np.ndarray):
            Encoded sequences for clustering.
        min_eps (float, optional):
            Minimum epsilon value to test.
            Defaults to 3.0.
        max_eps (float, optional):
            Maximum epsilon value to test.
            Defaults to 20.0.
        eps_step (float, optional):
            Step size for epsilon values.
            Defaults to 0.5.
        min_samples (int, optional):
            Minimum number of samples in a neighborhood for a point to be considered a core point.
            Defaults to 3.

        Returns
        -------
        Dict[str, Any]:
            Dictionary with best parameters:
            - 'eps': Best epsilon value
            - 'min_samples': Best min_samples value
            - 'n_clusters': Number of clusters with these parameters
        """
        eps_test_vals = np.arange(min_eps, max_eps + eps_step, eps_step)
        best_params = {'eps': None, 'min_samples': None, 'n_clusters': 0}

        logging.info(f"Testing {len(eps_test_vals)} epsilon values from {min_eps} to {max_eps}")

        for eps in eps_test_vals:
            # Sample a subset of data for faster parameter optimization
            sample_size = int(len(encoded_seqs) * 0.5)
            sample_size = len(encoded_seqs) if sample_size > len(encoded_seqs) else sample_size
            indices = np.random.choice(len(encoded_seqs), sample_size, replace=False)
            sampled_data = encoded_seqs[indices]

            # Run DBSCAN with current parameters
            clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(sampled_data)

            # Count clusters (excluding noise points labeled as -1)
            labels = clustering.labels_
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
            n_not_clustered = np.sum(labels == -1)

            logging.info(f"eps={eps:.2f}, min_samples={min_samples}, n_clusters={n_clusters}, n_not_clustered={n_not_clustered}")

            # Update best parameters if we found more clusters
            if n_clusters > best_params['n_clusters']:
                best_params = {'eps': eps, 'min_samples': min_samples, 'n_clusters': n_clusters}

        # If no good parameters found, use defaults
        if best_params['eps'] is None:
            best_params = {'eps': 10.0, 'min_samples': min_samples, 'n_clusters': 1}
            logging.warning("No good clustering parameters found. Using defaults.")

        logging.info(f"Best parameters: eps={best_params['eps']}, min_samples={best_params['min_samples']}, n_clusters={best_params['n_clusters']}")

        return best_params