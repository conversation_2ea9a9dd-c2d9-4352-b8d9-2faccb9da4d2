"""
ESM transformer embedding implementation for protein sequences.

This module provides ESM (Evolutionary Scale Modeling) transformer embeddings
for protein sequences, based on the working logic from msa/esm_embedding.py.
"""

import os
import sys
import numpy as np
import string
import logging
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional, Union

import torch
torch.set_num_threads(10)

from .sequence_encoder import BaseEncoder, EncodingConfig

# ESM model dimensions mapping
ESM_MODEL_NAME_TO_DIM = {
    "esm_msa1b_t12_100M_UR50S": 768,
    "esm2_t33_650M_UR50D": 1280,
}

# ESM MSA Transformer sequence limits
ESM_MSA_MAX_SEQUENCES = 1024  # Hard limit for MSA Transformer
ESM_MSA_DEFAULT_MAX_SEQS = 16000  # Default max sequences
ESM_MSA_DEFAULT_MAX_SEQ_LEN = 1022  # Default max sequence length


class ESMEmbedder(BaseEncoder):
    """
    ESM transformer embedder for protein sequences.
    
    This class provides ESM (Evolutionary Scale Modeling) embeddings for protein sequences,
    based on the proven working logic from the original esm_embedding.py.
    """
    
    def __init__(self, 
                 model_name: str = "esm_msa1b_t12_100M_UR50S", 
                 device: Optional[str] = None,
                 repr_layer: int = 12):
        """
        Initialize the ESM embedder.
        
        Parameters
        ----------
        model_name : str
            Name of the ESM model to use
        device : Optional[str]
            Device to use for inference (cuda/cpu)
        repr_layer : int
            Representation layer to extract embeddings from
        """
        # Set device
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device
        
        self.model_name = model_name
        self.repr_layer = repr_layer
        
        # Get embedding dimension
        self.embedding_dim = ESM_MODEL_NAME_TO_DIM.get(model_name, None)
        if self.embedding_dim is None:
            raise ValueError(f"Unsupported model name: {model_name}")
    
        # Initialize model components - using working logic from original
        logging.info(f"Loading ESM model {model_name} on {self.device} device")
        try:
            self.model, self.alphabet = self._load_model(model_name)
            self.batch_converter = self.alphabet.get_batch_converter()
            logging.info(f"Model loaded successfully")
            
            # Move model to device and set to eval mode
            self.model = self.model.to(self.device)
            self.model.eval()
            
        except Exception as e:
            logging.error(f"Failed to load ESM model: {e}")
            raise
    
    @staticmethod
    def _load_model(model_name: str) -> Tuple[torch.nn.Module, Any]:
        """
        Load ESM model and alphabet.
        
        Parameters
        ----------
        model_name : str
            Name of the ESM model to use.
            
        Returns
        -------
        Tuple[torch.nn.Module, Any]
            Tuple containing the model and alphabet.
        """
        try:
            import esm
        except ImportError as e:
            raise ImportError(f"ESM library not available: {e}") from e
        
        if model_name == "esm_msa1b_t12_100M_UR50S":
            return esm.pretrained.esm_msa1b_t12_100M_UR50S()
        elif model_name == "esm2_t33_650M_UR50D":
            return esm.pretrained.esm2_t33_650M_UR50D()
        else:
            raise ValueError(f"Unsupported model name: {model_name}")
    
    def encode(self, sequences: List[str], config: EncodingConfig) -> np.ndarray:
        """
        Encode sequences using ESM transformer embeddings.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to encode
        config : EncodingConfig
            Encoding configuration parameters
            
        Returns
        -------
        np.ndarray
            ESM embeddings of shape (n_sequences, flattened_embedding_dim)
        """
        if not sequences:
            raise ValueError("No sequences provided for ESM encoding")
        
        # Extract parameters from config
        max_seq_len = config.max_seq_len or ESM_MSA_DEFAULT_MAX_SEQ_LEN
        remove_lowercase = getattr(config, 'remove_lowercase', False)
        remove_special_chars = getattr(config, 'remove_special_chars', True)
        max_seqs = getattr(config, 'max_seqs', ESM_MSA_DEFAULT_MAX_SEQS)
        
        # Create descriptions for ESM input format
        descriptions = [f"seq_{i}" for i in range(len(sequences))]
        
        # Use the proven working embed_msa method
        embeddings = self.embed_msa(
            sequences=sequences,
            descriptions=descriptions,
            max_seqs=max_seqs,
            max_seq_len=max_seq_len,
            remove_lowercase=remove_lowercase,
            remove_special_chars=remove_special_chars
        )
        
        # Reshape embeddings for consistency with other encoders
        # Original returns shape: [batch, n_sequences, seq_length, embedding_dim]
        # We need: [n_sequences, seq_length * embedding_dim]
        
        if len(embeddings.shape) == 4:
            # Remove batch dimension and flatten
            batch_size, n_sequences, seq_length, emb_dim = embeddings.shape
            embeddings = embeddings[0]  # Remove batch dimension: [n_sequences, seq_length, emb_dim]
            embeddings = embeddings.reshape(n_sequences, seq_length * emb_dim)  # Flatten: [n_sequences, flattened_dim]
        elif len(embeddings.shape) == 3:
            # Already has correct shape [n_sequences, seq_length, emb_dim], just flatten
            n_sequences, seq_length, emb_dim = embeddings.shape
            embeddings = embeddings.reshape(n_sequences, seq_length * emb_dim)
        
        return embeddings
    
    def embed_msa(self,
                  sequences: List[str],
                  descriptions: List[str],
                  max_seqs: Optional[int] = ESM_MSA_DEFAULT_MAX_SEQS,
                  max_seq_len: Optional[int] = ESM_MSA_DEFAULT_MAX_SEQ_LEN,
                  remove_lowercase: bool = False,
                  remove_special_chars: bool = True) -> np.ndarray:
        """
        Embed MSA sequences using ESM MSA Transformer.
        
        This method uses the proven working logic from the original esm_embedding.py.
        
        Parameters
        ----------
        sequences : List[str]
            List of MSA sequences.
        descriptions : List[str]
            List of sequence descriptions.
        max_seq_len : Optional[int]
            Maximum sequence length. Default is 1022.
        max_seqs : Optional[int]
            Maximum number of sequences to embed. Default is 16000.
        remove_lowercase : bool, optional
            Whether to remove lowercase letters.
        remove_special_chars : bool, optional
            Whether to remove special characters.
            
        Returns
        -------
        np.ndarray
            MSA embedding array of shape (batch, n_sequences, seq_length, embedding_dim).
        """
        with torch.no_grad():
            try:
                # Apply limits if specified - using original logic
                if max_seqs is not None and len(sequences) > max_seqs:
                    logging.warning(f"Truncating the # of sequences from {len(sequences)} sequences to {max_seqs} sequences")
                    sequences = sequences[:max_seqs]
                    descriptions = descriptions[:max_seqs]
                
                # Preprocess MSA with specified options - using original logic
                msa_data = self.preprocess_msa(
                    sequences, 
                    descriptions, 
                    max_seq_len=max_seq_len,
                    remove_lowercase=remove_lowercase,
                    remove_special_chars=remove_special_chars
                )
                
                # Convert to tokens - using original logic
                batch_labels, batch_strs, batch_tokens = self.batch_converter(msa_data)
                
                # Move to device - using original logic
                batch_tokens = batch_tokens.to(self.device)  # [batch, n_seqs, padding_seq_len]
                
                # Get embeddings - using original logic
                results = self.model(
                    batch_tokens,
                    repr_layers=[self.repr_layer],
                    return_contacts=False
                )
                
                # Extract embeddings from specified layer - using original logic
                embeddings = results["representations"][self.repr_layer]  # [batch, n_seqs, padding_seq_len, dim]
                
                # Convert to numpy and return - using original logic
                return embeddings.cpu().numpy()
                
            except Exception as e:
                logging.error(f"Error during MSA embedding: {e}")
                raise
    
    @staticmethod
    def preprocess_msa(sequences: List[str], 
                       descriptions: List[str],
                       max_seq_len: int,
                       remove_lowercase: bool = False,
                       remove_special_chars: bool = True,
                       pad_to_equal_length: bool = True) -> List[Tuple[str, str]]:
        """
        Preprocess MSA sequences for ESM embedding.
        
        This method uses the proven working logic from the original esm_embedding.py.
        
        Parameters
        ----------
        sequences : List[str]
            List of MSA sequences.
        descriptions : List[str]
            List of sequence descriptions.
        max_seq_len : int
            Maximum sequence length.
        remove_lowercase : bool, optional
            Whether to remove lowercase letters.
        remove_special_chars : bool, optional
            Whether to remove special characters.
        pad_to_equal_length : bool, optional
            Whether to pad sequences to equal length.
            
        Returns
        -------
        List[Tuple[str, str]]
            List of (description, sequence) tuples in ESM input format.
        """
        # Check if sequences and descriptions have the same length
        assert len(sequences) == len(descriptions), "Sequences and descriptions must have the same length"
        
        # Create translation table based on parameters - using original logic
        deletekeys = {}
        
        if remove_lowercase:
            logging.debug('Adding lowercase letters to deletion table for ESM input')
            deletekeys.update(dict.fromkeys(string.ascii_lowercase))
        
        if remove_special_chars:
            logging.debug('Adding special characters to deletion table for ESM input')
            deletekeys["."] = None
            deletekeys["*"] = None
        
        # Create translation table - using original logic
        translation = str.maketrans(deletekeys)
        
        # Apply translation to each sequence - using original logic
        processed_sequences = [seq.translate(translation) for seq in sequences]
        
        # Replace invalid amino acid characters with 'X' to '-' gap - using original logic
        processed_sequences = [
            seq.replace('X', '-') for seq in processed_sequences
        ]
        processed_sequences = [
            ''.join(char.upper() for char in seq)
            for seq in processed_sequences
        ]
        
        # Pad sequences to equal length if needed - using original logic
        if pad_to_equal_length:
            processed_sequences = [seq.ljust(max_seq_len, '-') for seq in processed_sequences]
        
        # Create final MSA data - using original logic
        processed_msa = list(zip(descriptions, processed_sequences))
        
        return processed_msa
    
    def get_feature_dim(self, config: EncodingConfig) -> int:
        """
        Get the feature dimension for ESM embeddings.
        
        Parameters
        ----------
        config : EncodingConfig
            Encoding configuration
            
        Returns
        -------
        int
            Feature dimension (max_seq_len * embedding_dim)
        """
        max_seq_len = config.max_seq_len or ESM_MSA_DEFAULT_MAX_SEQ_LEN
        return max_seq_len * self.embedding_dim
    
    def save_embeddings(self, embeddings: np.ndarray, output_path: str) -> None:
        """
        Save embeddings to file.
        
        Parameters
        ----------
        embeddings : np.ndarray
            Embeddings to save
        output_path : str
            Path to save embeddings to
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Save embeddings
        np.save(output_path, embeddings)
        logging.info(f"Saved embeddings to {output_path}")
    
    @staticmethod
    def get_available_models() -> List[str]:
        """Get list of available ESM models."""
        return list(ESM_MODEL_NAME_TO_DIM.keys())
    
    @staticmethod
    def get_model_dim(model_name: str) -> int:
        """
        Get embedding dimension for a specific model.
        
        Parameters
        ----------
        model_name : str
            Name of the ESM model
            
        Returns
        -------
        int
            Embedding dimension
            
        Raises
        ------
        ValueError
            If model name is not supported
        """
        if model_name not in ESM_MODEL_NAME_TO_DIM:
            raise ValueError(f"Unsupported model: {model_name}")
        
        return ESM_MODEL_NAME_TO_DIM[model_name] 