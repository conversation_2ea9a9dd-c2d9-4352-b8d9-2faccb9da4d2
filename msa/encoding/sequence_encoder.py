"""
Unified sequence encoder interface for MSA sequences.

This module provides a single, consistent interface for encoding protein sequences
using various methods, consolidating the previously scattered encoding logic.
"""

import numpy as np
import logging
from abc import ABC, abstractmethod
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass


class EncodingMethod(Enum):
    """Enumeration of available encoding methods."""
    ONEHOT = "onehot"
    MSA_TRANSFORMER = "MSAtransformer"
    ESM_EMBEDDING = "esm_embedding"


@dataclass
class EncodingConfig:
    """Configuration for sequence encoding."""
    method: EncodingMethod
    max_seq_len: Optional[int] = None
    alphabet: str = "ACDEFGHIKLMNPQRSTVWY-X"
    
    # One-hot specific parameters
    normalize: bool = False
    
    # ESM/Transformer specific parameters
    model_name: str = "esm_msa1b_t12_100M_UR50S"
    repr_layer: int = 12
    device: Optional[str] = None
    remove_lowercase: bool = False
    remove_special_chars: bool = True
    batch_size: int = 100


class BaseEncoder(ABC):
    """Abstract base class for sequence encoders."""
    
    @abstractmethod
    def encode(self, 
            sequences: List[str], 
            config: EncodingConfig) -> np.ndarray:
        """
        Encode sequences using the specific encoding method.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to encode
        config : EncodingConfig
            Encoding configuration parameters
            
        Returns
        -------
        np.ndarray
            Encoded sequences array
        """
        pass
    
    @abstractmethod
    def get_feature_dim(self, config: EncodingConfig) -> int:
        """Get the feature dimension for this encoding method."""
        pass


class SequenceEncoder:
    """
    Unified sequence encoder that provides a single interface for all encoding methods.
    
    This class consolidates the previously scattered encoding logic from:
    - msa_pipeline.py (encode_sequences, _encode_onehot, _encode_MSAtransformer)
    - clustering/utils/preprocessing.py (encode_sequences)
    - esm_embedding.py (ESMEmbedder)
    """
    
    def __init__(self):
        """Initialize the sequence encoder."""
        self._encoders: Dict[EncodingMethod, BaseEncoder] = {}
        self._setup_encoders()
    
    def _setup_encoders(self) -> None:
        """Initialize all available encoders."""
        # Only setup OneHot encoder initially to avoid import errors
        # Other encoders will be lazily loaded when needed
        from .onehot_encoder import OneHotEncoder
        self._encoders[EncodingMethod.ONEHOT] = OneHotEncoder()
    
    def _get_encoder(self, method: EncodingMethod) -> BaseEncoder:
        """Get encoder for the specified method, loading it if necessary."""
        if method not in self._encoders:
            if method in [EncodingMethod.MSA_TRANSFORMER, EncodingMethod.ESM_EMBEDDING]:
                try:
                    from .esm_embedding import ESMEmbedder
                    self._encoders[method] = ESMEmbedder()
                except ImportError as e:
                    raise ImportError(f"ESM encoder not available: {e}")
            else:
                raise ValueError(f"Unknown encoding method: {method}")
        
        return self._encoders[method]
    
    def encode(self, 
               sequences: List[str],
               method: Union[str, EncodingMethod] = EncodingMethod.ONEHOT,
               max_seq_len: Optional[int] = None,
               **kwargs) -> np.ndarray:
        """
        Encode sequences using the specified method.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to encode
        method : Union[str, EncodingMethod]
            Encoding method to use
        max_seq_len : Optional[int]
            Maximum sequence length for padding/truncation
        **kwargs
            Additional encoding parameters
            
        Returns
        -------
        np.ndarray
            Encoded sequences array
            
        Raises
        ------
        ValueError
            If encoding method is not supported
        """
        if not sequences:
            raise ValueError("No sequences provided for encoding")
        
        # Convert string to enum if necessary
        if isinstance(method, str):
            try:
                method = EncodingMethod(method)
            except ValueError:
                raise ValueError(f"Unsupported encoding method: {method}")
        
        # Determine max sequence length if not provided
        if max_seq_len is None:
            max_seq_len = max(len(seq) for seq in sequences)
        
        # Filter kwargs to only include valid encoding parameters
        # This prevents clustering parameters (eps, min_samples) from being passed to EncodingConfig
        valid_encoding_params = {
            'alphabet', 'normalize',  # One-hot parameters
            'model_name', 'repr_layer', 'device', 'remove_lowercase', 
            'remove_special_chars', 'batch_size'  # ESM/Transformer parameters
        }
        
        # Filter out non-encoding parameters (like clustering params)
        filtered_kwargs = {
            k: v for k, v in kwargs.items() 
            if k in valid_encoding_params
        }
        
        # Log filtered parameters for debugging
        if len(kwargs) != len(filtered_kwargs):
            filtered_out = set(kwargs.keys()) - set(filtered_kwargs.keys())
            logging.debug(f"Filtered out non-encoding parameters: {filtered_out}")
        
        # Create encoding configuration with filtered parameters
        config = EncodingConfig(
            method=method,
            max_seq_len=max_seq_len,
            **filtered_kwargs
        )
        
        # Get appropriate encoder (with lazy loading)
        encoder = self._get_encoder(method)
        
        try:
            logging.info(f"Encoding {len(sequences)} sequences using {method.value}")
            encoded = encoder.encode(sequences, config)
            logging.info(f"Encoding completed. Shape: {encoded.shape}")
            return encoded
            
        except Exception as e:
            logging.error(f"Encoding failed with method {method.value}: {str(e)}")
            raise RuntimeError(f"Encoding failed: {str(e)}") from e
    
    def get_feature_dim(self, 
                       method: Union[str, EncodingMethod],
                       max_seq_len: int,
                       **kwargs) -> int:
        """
        Get the feature dimension for the specified encoding method.
        
        Parameters
        ----------
        method : Union[str, EncodingMethod]
            Encoding method
        max_seq_len : int
            Maximum sequence length
        **kwargs
            Additional parameters
            
        Returns
        -------
        int
            Feature dimension
        """
        if isinstance(method, str):
            method = EncodingMethod(method)
        
        config = EncodingConfig(
            method=method,
            max_seq_len=max_seq_len,
            **kwargs
        )
        
        return self._encoders[method].get_feature_dim(config)
    
    def available_methods(self) -> List[str]:
        """Get list of available encoding methods."""
        return [method.value for method in self._encoders.keys()]
    
    @classmethod
    def create_config(cls,
                     method: Union[str, EncodingMethod],
                     **kwargs) -> EncodingConfig:
        """
        Create an encoding configuration.
        
        Parameters
        ----------
        method : Union[str, EncodingMethod]
            Encoding method
        **kwargs
            Configuration parameters
            
        Returns
        -------
        EncodingConfig
            Encoding configuration object
        """
        if isinstance(method, str):
            method = EncodingMethod(method)
        
        return EncodingConfig(method=method, **kwargs)


# Global encoder instance for convenience
default_encoder = SequenceEncoder()


def encode_sequences(sequences: List[str],
                    method: Union[str, EncodingMethod] = "onehot",
                    **kwargs) -> np.ndarray:
    """
    Convenience function for encoding sequences.
    
    This function provides backward compatibility with the existing
    encode_sequences functions scattered throughout the codebase.
    
    Parameters
    ----------
    sequences : List[str]
        List of sequences to encode
    method : Union[str, EncodingMethod]
        Encoding method to use
    **kwargs
        Additional encoding parameters
        
    Returns
    -------
    np.ndarray
        Encoded sequences array
    """
    return default_encoder.encode(sequences, method, **kwargs) 