"""
Encoding module for MSA sequence encoding.

This module provides unified interfaces for encoding protein sequences
using various methods including one-hot encoding and transformer embeddings.
"""

from .sequence_encoder import SequenceEncoder, EncodingMethod
from .onehot_encoder import OneHotEncoder
from .esm_embedding import ESMEmbedder
from .encoder_factory import EncoderFactory

__all__ = [
    'SequenceEncoder',
    'EncodingMethod', 
    'OneHotEncoder',
    'ESMEmbedder',
    'EncoderFactory'
] 