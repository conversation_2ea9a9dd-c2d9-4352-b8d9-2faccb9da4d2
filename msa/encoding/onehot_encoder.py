"""
One-hot encoding implementation for protein sequences.

This module consolidates the one-hot encoding logic previously scattered across:
- msa_pipeline.py (_encode_onehot)
- clustering/utils/preprocessing.py (_encode_onehot)
"""

import numpy as np
import logging
from typing import List
from sklearn.preprocessing import StandardScaler, MinMaxScaler

from .sequence_encoder import BaseEncoder, EncodingConfig


class OneHotEncoder(BaseEncoder):
    """
    One-hot encoder for protein sequences.
    
    This encoder converts amino acid sequences into binary vectors where each
    position represents a specific amino acid character.
    """
    
    def __init__(self):
        """Initialize the one-hot encoder."""
        self._scaler = None
    
    def encode(self, sequences: List[str], config: EncodingConfig) -> np.ndarray:
        """
        Encode sequences using one-hot encoding.
        
        Parameters
        ----------
        sequences : List[str]
            List of sequences to encode
        config : EncodingConfig
            Encoding configuration containing alphabet and other parameters
            
        Returns
        -------
        np.ndarray
            One-hot encoded sequences of shape (n_sequences, max_seq_len * n_features)
        """
        if not sequences:
            raise ValueError("No sequences provided for one-hot encoding")
        
        max_seq_len = config.max_seq_len or max(len(seq) for seq in sequences)
        alphabet = config.alphabet
        n_sequences = len(sequences)
        n_features = len(alphabet)
        
        logging.debug(f"One-hot encoding {n_sequences} sequences with alphabet size {n_features}")
        
        # Create encoding matrix
        encoded = np.zeros((n_sequences, max_seq_len * n_features), dtype=np.float32)
        
        # Create character to index mapping
        char_to_idx = {char: i for i, char in enumerate(alphabet)}
        
        # Encode each sequence
        for seq_idx, sequence in enumerate(sequences):
            for pos, char in enumerate(sequence[:max_seq_len]):
                if char in char_to_idx:
                    feature_idx = pos * n_features + char_to_idx[char]
                    encoded[seq_idx, feature_idx] = 1.0
                # Unknown characters are left as zeros (treated as gaps)
        
        # Apply normalization if requested
        if config.normalize:
            encoded = self._normalize_features(encoded)
        
        logging.debug(f"One-hot encoding completed. Shape: {encoded.shape}")
        return encoded
    
    def get_feature_dim(self, config: EncodingConfig) -> int:
        """
        Get the feature dimension for one-hot encoding.
        
        Parameters
        ----------
        config : EncodingConfig
            Encoding configuration
            
        Returns
        -------
        int
            Feature dimension (max_seq_len * alphabet_size)
        """
        max_seq_len = config.max_seq_len or 1000  # Default fallback
        alphabet_size = len(config.alphabet)
        return max_seq_len * alphabet_size
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """
        Normalize features using standard scaling.
        
        Parameters
        ----------
        features : np.ndarray
            Features to normalize
            
        Returns
        -------
        np.ndarray
            Normalized features
        """
        if self._scaler is None:
            self._scaler = StandardScaler()
        
        return self._scaler.fit_transform(features)
    
    @staticmethod
    def get_default_alphabet() -> str:
        """Get the default amino acid alphabet."""
        return "ACDEFGHIKLMNPQRSTVWY-X"
    
    @staticmethod
    def validate_sequences(sequences: List[str], alphabet: str) -> bool:
        """
        Validate that sequences contain only characters from the alphabet.
        
        Parameters
        ----------
        sequences : List[str]
            Sequences to validate
        alphabet : str
            Allowed characters
            
        Returns
        -------
        bool
            True if all sequences are valid
        """
        valid_chars = set(alphabet)
        
        for seq in sequences:
            if not all(char in valid_chars for char in seq):
                return False
        
        return True
    
    @staticmethod
    def create_alphabet_mapping(alphabet: str) -> dict:
        """
        Create a mapping from characters to indices.
        
        Parameters
        ----------
        alphabet : str
            Alphabet string
            
        Returns
        -------
        dict
            Mapping from character to index
        """
        return {char: i for i, char in enumerate(alphabet)} 