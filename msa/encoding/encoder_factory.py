"""
Factory for creating sequence encoders.

This module provides a factory pattern implementation for creating
different types of sequence encoders, similar to the clustering factory pattern.
"""

import logging
from typing import Dict, Optional, List

from .sequence_encoder import BaseEncoder, EncodingMethod
from .onehot_encoder import OneHotEncoder
from .esm_embedding import ESMEmbedder


class EncoderFactory:
    """
    Factory for creating sequence encoder instances.
    
    This factory provides a centralized way to create and manage
    different types of sequence encoders.
    """
    
    def __init__(self):
        """Initialize the encoder factory."""
        self._encoder_cache: Dict[str, BaseEncoder] = {}
    
    def create_encoder(self, 
                      method: EncodingMethod,
                      use_cache: bool = True,
                      **kwargs) -> BaseEncoder:
        """
        Create an encoder instance for the specified method.
        
        Parameters
        ----------
        method : EncodingMethod
            Encoding method to create encoder for
        use_cache : bool
            Whether to use cached encoder instances
        **kwargs
            Additional parameters for encoder initialization
            
        Returns
        -------
        BaseEncoder
            Encoder instance for the specified method
            
        Raises
        ------
        ValueError
            If encoding method is not supported
        """
        # Create cache key
        cache_key = f"{method.value}_{hash(str(sorted(kwargs.items())))}"
        
        # Return cached instance if available and caching is enabled
        if use_cache and cache_key in self._encoder_cache:
            return self._encoder_cache[cache_key]
        
        # Create new encoder instance
        if method == EncodingMethod.ONEHOT:
            encoder = OneHotEncoder(**kwargs)
        elif method in [EncodingMethod.MSA_TRANSFORMER, EncodingMethod.ESM_EMBEDDING]:
            encoder = ESMEmbedder(**kwargs)
        else:
            raise ValueError(f"Unsupported encoding method: {method}")
        
        # Cache the instance if caching is enabled
        if use_cache:
            self._encoder_cache[cache_key] = encoder
        
        logging.debug(f"Created encoder for method: {method.value}")
        return encoder
    
    def get_available_methods(self) -> List[str]:
        """
        Get list of available encoding methods.
        
        Returns
        -------
        List[str]
            List of available encoding method names
        """
        return [method.value for method in EncodingMethod]
    
    def clear_cache(self) -> None:
        """Clear the encoder cache."""
        self._encoder_cache.clear()
        logging.debug("Cleared encoder cache")
    
    def get_method_info(self, method: EncodingMethod) -> Dict[str, str]:
        """
        Get information about an encoding method.
        
        Parameters
        ----------
        method : EncodingMethod
            Encoding method to get information for
            
        Returns
        -------
        Dict[str, str]
            Information about the encoding method
        """
        info = {
            'method': method.value,
            'description': self._get_method_description(method),
            'encoder_class': self._get_encoder_class_name(method)
        }
        
        return info
    
    def _get_method_description(self, method: EncodingMethod) -> str:
        """Get description for an encoding method."""
        descriptions = {
            EncodingMethod.ONEHOT: "One-hot encoding for amino acid sequences",
            EncodingMethod.MSA_TRANSFORMER: "ESM MSA Transformer embeddings",
            EncodingMethod.ESM_EMBEDDING: "ESM protein language model embeddings"
        }
        return descriptions.get(method, "Unknown encoding method")
    
    def _get_encoder_class_name(self, method: EncodingMethod) -> str:
        """Get encoder class name for an encoding method."""
        class_names = {
            EncodingMethod.ONEHOT: "OneHotEncoder",
            EncodingMethod.MSA_TRANSFORMER: "ESMEmbedder",
            EncodingMethod.ESM_EMBEDDING: "ESMEmbedder"
        }
        return class_names.get(method, "Unknown")


# Global factory instance for convenience
default_factory = EncoderFactory()


def create_encoder(method: EncodingMethod, **kwargs) -> BaseEncoder:
    """
    Convenience function for creating encoders.
    
    Parameters
    ----------
    method : EncodingMethod
        Encoding method
    **kwargs
        Additional parameters for encoder initialization
        
    Returns
    -------
    BaseEncoder
        Encoder instance
    """
    return default_factory.create_encoder(method, **kwargs) 