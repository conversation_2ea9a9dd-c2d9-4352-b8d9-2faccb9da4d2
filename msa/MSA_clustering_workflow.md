graph TD
    A[MSA 파일<br/>KaiB_101.a3m] --> B[MSAReader로 파일 읽기]
    B --> C[MSACore 객체 생성]
    C --> D[MSAPreprocessor<br/>전처리]
    
    D --> E[시퀀스 필터링<br/>길이, 갭 내용]
    E --> F[중복 제거<br/>Identity threshold]
    F --> G[시퀀스 정리<br/>대소문자, 특수문자]
    
    G --> H[SequenceEncoder<br/>시퀀스 인코딩]
    H --> I{인코딩 방법}
    I -->|OneHot| J[OneHotEncoder]
    I -->|Transformer| K[ESMEmbedder]
    
    J --> L[클러스터링 준비]
    K --> L
    L --> M[ClusteringManager<br/>클러스터링 수행]
    
    M --> N{클러스터링 알고리즘}
    N -->|DBSCAN| O[DBSCANClusterer]
    N -->|Hierarchical| P[HierarchicalClusterer]
    N -->|HDBSCAN| Q[HDBSCANClusterer]
    
    O --> R[ClusteringResult<br/>클러스터링 결과]
    P --> R
    Q --> R
    
    R --> S[MSAPostprocessor<br/>후처리]
    S --> T[결과 분석]
    
    T --> U[MSAStatistics<br/>통계 분석]
    T --> V[MSAMetrics<br/>품질 평가]
    T --> W[DiversityAnalyzer<br/>다양성 분석]
    
    U --> X[결과 시각화]
    V --> X
    W --> X
    R --> X
    
    X --> Y[MSAPlotter<br/>기본 플롯]
    X --> Z[ClusteringVisualizer<br/>클러스터링 시각화]
    X --> AA[MSACharts<br/>통계 차트]
    
    Y --> BB[보존성 히트맵<br/>유사도 매트릭스]
    Z --> CC[PCA/t-SNE 플롯<br/>클러스터 분포]
    AA --> DD[길이 분포<br/>아미노산 조성]
    
    BB --> EE[최종 보고서 생성]
    CC --> EE
    DD --> EE
    
    classDef inputClass fill:#e3f2fd
    classDef procClass fill:#f3e5f5
    classDef clusterClass fill:#e8f5e8
    classDef analysisClass fill:#fff3e0
    classDef vizClass fill:#fce4ec
    classDef outputClass fill:#e0f2f1
    
    class A inputClass
    class B,C,D,E,F,G,H,I,J,K,L procClass
    class M,N,O,P,Q,R,S clusterClass
    class T,U,V,W analysisClass
    class X,Y,Z,AA,BB,CC,DD vizClass
    class EE outputClass