import sys
import numpy as np
import string # Required for deduplication logic
import logging
import re
import pandas as pd
from typing import List, Tuple, Any, Sequence, Literal, Dict, Optional, Union
# from collections.abc import MutableMapping, Sequence # Sequence is imported from typing

try:
    from .esm_embedding import ESMEmbedder
    from .file_io.file_readers import MSAFileReader
    ESM_AVAILABLE = True
except ImportError:
    try:
        # Fallback for standalone execution
        from esm_embedding import ESMEmbedder
        from file_io.file_readers import MSAFileReader
        ESM_AVAILABLE = True
    except ImportError:
        # ESM not available, set flag
        ESM_AVAILABLE = False
        ESMEmbedder = None
        try:
            from .file_io.file_readers import MSAFileReader
        except ImportError:
            from file_io.file_readers import MSAFileReader

class Error(Exception):
    pass


def _featurize(seq: str, chain_poly_type: Literal['protein', 'rna', 'dna']) -> Union[str, List[int]]:
    """
    Featurizes a sequence based on its polymer type.

    For standard polymer types, this would typically convert the sequence to a numerical representation.
    For non-standard types, it returns the sequence as is.

    Parameters
    ----------
    seq (str):
        The sequence to featurize.
    chain_poly_type (Literal['protein', 'rna', 'dna']):
        The polymer type of the sequence.

    Returns
    -------
    Union[str, List[int]]:
        The featurized sequence, either as a list of integers or as the original string.
    """
    # This is a simplified version - in a real implementation, you would use a proper
    # featurization method based on the polymer type
    if chain_poly_type in ('protein', 'rna', 'dna'):
        # Simple example: convert to ASCII values
        return [ord(c) for c in seq.upper() if c not in string.ascii_lowercase]
    # For anything else, return the sequence as is
    return seq


def sequences_are_feature_equivalent(
    sequence1: str,
    sequence2: str,
    chain_poly_type: Literal['protein', 'rna', 'dna'],
) -> bool:
    """
    Checks if two sequences are equivalent after featurization.

    This is useful for comparing sequences that might have different representations
    but are functionally the same (e.g., due to mutations introduced by search tools).

    Parameters
    ----------
    sequence1 (str):
        The first sequence to compare.
    sequence2 (str):
        The second sequence to compare.
    chain_poly_type (Literal['protein', 'rna', 'dna']):
        The polymer type of the sequences.

    Returns
    -------
    bool:
        True if the sequences are feature-equivalent, False otherwise.
    """
    feat1 = _featurize(sequence1, chain_poly_type)
    feat2 = _featurize(sequence2, chain_poly_type)
    return feat1 == feat2


class MSA:
    """Multiple Sequence Alignment container with methods for manipulating it."""
    def __init__(self,
        query_sequence: str, # The query sequence for the MSA
        chain_poly_type: Literal['protein', 'rna', 'dna'], # Polymer type of the chain
        sequences: Sequence[str],    # List of sequences in the MSA
        descriptions: Sequence[str], # List of descriptions for each sequence
        deduplicated: bool=True,     # Whether to deduplicate sequences
        gap_off: float=0.25,         # Gap cutoff for filtering sequences    
        # clustering_method: Tuple[str]=['dbscan', 'hierachical'], # This parameter is not used yet
    ):
        """
        Initializes the MSA object.

        Parameters
        ----------
        query_sequence (str):
            The main sequence that was used to generate the MSA.
        chain_poly_type (Literal['protein', 'rna', 'dna']):
            The polymer type of the sequences.
        sequences (Sequence[str]):
            A list of strings, where each string is a sequence in the MSA.
        descriptions (Sequence[str]):
            A list of strings, providing a description for each corresponding sequence.
        deduplicated (bool, optional):
            If True (default), removes duplicate sequences. Lowercase characters are ignored during deduplication.
        """
        if len(sequences) != len(descriptions):
            raise ValueError("sequences and descriptions must have the same length")

        self.query_sequence = query_sequence
        self.chain_poly_type = chain_poly_type

        if not deduplicated: # Corrected variable name from 'deduplicate' to 'deduplicated'
            self.sequences = list(sequences) # Store as list
            self.descriptions = list(descriptions) # Store as list
        else: # [NOTE] I do not use this code lines
            self.sequences = []
            self.descriptions = []
            # A replacement table that removes all lowercase characters for uniqueness check.
            deletion_table = str.maketrans('', '', string.ascii_lowercase)
            unique_sequences = set()
            for seq, desc in zip(sequences, descriptions):
                # Using string.translate is faster than re.sub('[a-z]+', '').
                sequence_no_deletions = seq.translate(deletion_table)
                if sequence_no_deletions not in unique_sequences:
                    unique_sequences.add(sequence_no_deletions)
                    self.sequences.append(seq)
                    self.descriptions.append(desc)

        # Make sure the MSA always has at least the query sequence if all others were duplicates.
        if not self.sequences:
            self.sequences = [query_sequence]
            self.descriptions = ['Original query']

        # Check if the 1st MSA sequence matches the query sequence.
        # This might be complex due to potential mutations by search tools like jackhmmer.
        if self.sequences and not sequences_are_feature_equivalent(
            self.sequences[0], query_sequence, chain_poly_type
        ):
            logging.warning(
                f'First MSA sequence {self.sequences[0]} does not match the query sequence {query_sequence}'
            )
            # Instead of raising an error, we'll just log a warning
            
        # # Pre-processe MSA
        # self.sequences, self.descriptions, _ = self._filter_sequences(0.25)

    
    ## ======================= ##
    ## -- Sequence encoding -- ##
    ## ======================= ##
    @classmethod
    def encode_sequences(cls,
        sequences: List[str],
        max_seq_len: int,  # Maximum length to pad/truncate sequences to
        encoding_method: Literal['onehot', 'MSAtransformer']='onehot', # Encoding method
        alphabet: str="ACDEFGHIKLMNPQRSTVWY-X", # Alphabet for one-hot encoding
    ) -> np.ndarray:
        """
        Encodes sequences using the specified method.

        Parameters
        ----------
        sequences (List[str]):
            A list of sequences to encode.
        max_seq_len (int):
            The maximum length for each sequence. Sequences will be padded or truncated.
        encoding_method (Literal['onehot', 'MSAtransformer'], optional):
            The method to use for encoding. Currently supports 'onehot' and 'MSAtransformer'.
            Defaults to 'onehot'.
        alphabet (str, optional):
            The alphabet string to use for one-hot encoding.
            Defaults to "ACDEFGHIKLMNPQRSTVWY-X".

        Returns
        -------
        np.ndarray:
            A NumPy array containing the encoded sequences.
            For 'onehot', shape is (num_sequences, max_seq_len * len(alphabet)).
            For 'MSAtransformer', shape is (num_sequences, flattened_dimension).
        """
        if encoding_method == 'onehot':
            encoded_seqs = cls._encode_onehot(sequences, max_seq_len, alphabet)
        elif encoding_method == 'MSAtransformer':            
            encoded_seqs = cls._encode_MSAtransformer(sequences, max_seq_len)
        else:
            raise ValueError(f"Unsupported encoding method: {encoding_method}")
        return encoded_seqs

    @classmethod
    def _encode_onehot(cls,
        sequences: Sequence[str], # List of sequences to encode
        max_seq_len: int, # Maximum length for encoding
        alphabet: str = "ACDEFGHIKLMNPQRSTVWY-X", # Alphabet for one-hot encoding
    ) -> np.ndarray:
        """
        Encodes sequences into one-hot format.
        Inspired by AF_Cluster/scripts/utils.py encode_seqs function.

        Parameters
        ----------
        sequences (Sequence[str]):
            A list of protein sequences.
        max_seq_len (int):
            The maximum length to pad or truncate sequences to.
        alphabet (str, optional):
            The alphabet string to use for one-hot encoding.
            Defaults to "ACDEFGHIKLMNPQRSTVWY-X".

        Returns
        -------
        np.ndarray:
            A 3D NumPy array of shape (num_sequences, max_seq_len, len(alphabet))
            representing the one-hot encoded sequences, reshaped to (num_sequences, max_seq_len * len(alphabet)).
        """
        num_sequences = len(sequences)
        len_alphabet = len(alphabet)

        # Initialize the array with zeros
        encoded_arr = np.zeros((num_sequences, max_seq_len, len_alphabet), dtype=np.float32)

        # Create a mapping from character to index for quick lookups
        char_to_index = {char: idx for idx, char in enumerate(alphabet)}

        for seq_idx, seq in enumerate(sequences):
            for char_idx, char_in_seq in enumerate(seq):
                if char_idx >= max_seq_len: # Truncate if sequence is longer than max_seq_len
                    break

                if char_in_seq in char_to_index:
                    alphabet_idx = char_to_index[char_in_seq]
                    encoded_arr[seq_idx, char_idx, alphabet_idx] = 1.0
                # else:
                # Characters not in the alphabet are ignored (effectively zero in the one-hot vector)
                # Consider adding a warning or error for unknown characters if necessary

        return encoded_arr.reshape(num_sequences, -1)

    @classmethod
    def _encode_MSAtransformer(cls,
        sequences: Sequence[str],
        max_seq_len: Optional[int] = None, # Maximum length for MSA Transformer (if applicable)
        model_name: str = "esm_msa1b_t12_100M_UR50S",
        repr_layer: int = 12,
        device: Optional[str] = None,
        remove_lowercase: bool = False,
        remove_special_chars: bool = True,
        batch_size: int = 100, # Maximum batch size for processing (MSA Transformer limit ~1024)
    ) -> np.ndarray:
        """
        Encodes sequences using ESM MSA Transformer with batch processing.
        Uses ESMEmbedder class from msa.esm_embedding module.

        Parameters
        ----------
        sequences (Sequence[str]):
            A list of protein sequences.
        max_seq_len (Optional[int], optional):
            The maximum length to consider for sequences. If None, will be calculated.
            Defaults to None.
        model_name (str, optional):
            The name of the ESM model to use.
            Defaults to "esm_msa1b_t12_100M_UR50S".
        repr_layer (int, optional):
            The representation layer to extract embeddings from.
            Defaults to 12.
        device (Optional[str], optional):
            Device to use for inference. If None, will use CUDA if available.
            Defaults to None.
        remove_lowercase (bool, optional):
            Whether to remove lowercase letters (insertions in MSA format).
            Default is False (preserve lowercase letters).
        remove_special_chars (bool, optional):
            Whether to remove special characters like '.' and '*'.
            Default is True.
        batch_size (int, optional):
            Maximum number of sequences to process in a single batch.
            MSA Transformer can handle up to ~1024 sequences at once.
            Defaults to 100.

        Returns
        -------
        np.ndarray:
            A NumPy array containing the encoded sequences, reshaped to (num_sequences, flattened_dimension).
        """
        # Check if ESM is available
        if not ESM_AVAILABLE or ESMEmbedder is None:
            raise ImportError(
                "ESM (Evolutionary Scale Modeling) is not available. "
                "To use MSAtransformer encoding, please install ESM by running: "
                "pip install fair-esm"
            )
            
        if len(sequences) == 0:
            logging.warning("No sequences provided for MSA Transformer encoding")
            return np.array([])
        
        try:
            # Calculate maximum sequence length if not provided
            if max_seq_len is None:
                max_seq_len = max(len(seq) for seq in sequences)
                
            total_sequences = len(sequences)
            
            # Initialize ESMEmbedder
            embedder = ESMEmbedder(
                model_name=model_name,
                device=device,
                repr_layer=repr_layer
            )

            # Standard embedding dimension for ESM models
            embedding_dim = embedder.embedding_dim
            
            # If sequences fit in a single batch, process them directly
            if total_sequences <= batch_size:
                # Generate descriptions if not provided (since ESMEmbedder requires them)
                descriptions = [f"sequence_{i}" for i in range(total_sequences)]
                
                # Get embeddings with the specified options
                embeddings = embedder.embed_msa(
                    sequences=sequences,
                    descriptions=descriptions,
                    max_seq_len=max_seq_len,
                    remove_lowercase=remove_lowercase,
                    remove_special_chars=remove_special_chars
                )
                
                # embeddings shape: (1, n_sequences, seq_len, embedding_dim)
                # Reshape to (n_sequences, seq_len * embedding_dim) for clustering
                if len(embeddings.shape) == 4:
                    # Extract sequences from the single batch and flatten
                    embeddings = embeddings[0]  # Remove batch dimension: (n_sequences, seq_len, embedding_dim)
                    embeddings = embeddings.reshape(total_sequences, -1)  # Flatten: (n_sequences, seq_len * embedding_dim)
                
                return embeddings
            
            # For large sequence sets, process in batches
            logging.info(f"Processing {total_sequences} sequences for MSA Transformer in batches of {batch_size}")
            
            # Initialize array to store all embeddings
            all_embeddings = np.zeros((total_sequences, max_seq_len + 1, embedding_dim), dtype=np.float32)
            
            # Process sequences in batches
            total_batches = (total_sequences - 1) // batch_size + 1
            
            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, total_sequences)
                batch_sequences = sequences[start_idx:end_idx]
                
                # Generate descriptions for this batch
                batch_descriptions = [f"sequence_{i}" for i in range(start_idx, end_idx)]
                
                logging.info(f"  processing batch {batch_idx + 1}/{total_batches} with {len(batch_sequences)} sequences")
                
                # Get embeddings for this batch
                batch_embeddings = embedder.embed_msa(
                    sequences=batch_sequences,
                    descriptions=batch_descriptions,
                    max_seq_len=max_seq_len,
                    remove_lowercase=remove_lowercase,
                    remove_special_chars=remove_special_chars
                )
                
                # Reshape batch embeddings to the expected format
                # First dimension (0) is batch index, we want to extract all sequences from this batch
                batch_embeddings = batch_embeddings[0, :, :, :].reshape(len(batch_sequences), -1, embedding_dim)
                
                # Store this batch's embeddings in the combined result
                all_embeddings[start_idx:end_idx] = batch_embeddings
                
                logging.info(f"  batch {batch_idx + 1} processed, embedding shape: {batch_embeddings.shape}")
            
            logging.info(f"All batches processed, final embedding shape: {all_embeddings.shape}")
            
            # Return the combined embeddings, reshaped for consistency with _encode_onehot
            return all_embeddings.reshape(total_sequences, -1)
            
        except Exception as e:
            logging.error(f"Error during MSA Transformer encoding: {str(e)}")
            raise RuntimeError(f"MSA Transformer encoding failed: {str(e)}")


    ## ======================= ##
    ## -- Init classmethods -- ##
    ## ======================= ##
    @classmethod
    def from_a3m(cls,
        query_sequence: str,
        chain_poly_type: Literal['protein', 'rna', 'dna'],
        a3m: str,
        max_depth: Optional[int] = None,
        deduplicated: bool = True,
    ) -> 'MSA':
        """
        Parses a single A3M string and builds an MSA object.

        Parameters
        ----------
        query_sequence (str):
            The query sequence that was used to generate the MSA.
        chain_poly_type (Literal['protein', 'rna', 'dna']):
            The polymer type of the sequences.
        a3m (str):
            The A3M formatted string containing the MSA.
        max_depth (Optional[int], optional):
            If provided, limits the number of sequences to include.
            Defaults to None (include all sequences).
        deduplicated (bool, optional):
            If True, the MSA sequences will be deduplicated.
            Defaults to True.

        Returns
        -------
        MSA:
            An MSA object created from the A3M string.
        """
        # Parse the A3M string to get sequences and descriptions
        sequences, descriptions = MSAFileReader._parse_a3m_content(a3m)

        # Crop the MSA if max_depth is specified
        if max_depth is not None and 0 < max_depth < len(sequences):
            logging.info(
                'MSA cropped from depth of %d to %d for %s.',
                len(sequences),
                max_depth,
                query_sequence,
            )
            sequences = sequences[:max_depth]
            descriptions = descriptions[:max_depth]

        return cls(
            query_sequence=query_sequence,
            chain_poly_type=chain_poly_type,
            sequences=sequences,
            descriptions=descriptions,
            deduplicated=deduplicated,
        )

    @classmethod
    def from_multiple_a3ms(cls,
        a3ms: Sequence[str],
        chain_poly_type: Literal['protein', 'rna', 'dna'],
        deduplicated: bool = True,
    ) -> 'MSA':
        """
        Initializes the MSA from multiple A3M strings.

        Parameters
        ----------
        a3ms (Sequence[str]):
            A sequence of A3M strings representing individual MSAs produced by
            different tools/databases.
        chain_poly_type (Literal['protein', 'rna', 'dna']):
            The polymer type of the sequences.
        deduplicated (bool, optional):
            If True, the MSA sequences will be deduplicated.
            Defaults to True.

        Returns
        -------
        MSA:
            An MSA object created by merging multiple A3Ms.

        Raises
        ------
        ValueError:
            If no A3M strings are provided or if query sequences don't match.
        """
        if not a3ms:
            raise ValueError('At least one A3M must be provided.')

        query_sequence = None
        all_sequences = []
        all_descriptions = []

        for a3m in a3ms:
            sequences, descriptions = MSAFileReader._parse_a3m_content(a3m)
            if not sequences:
                continue  # Skip empty A3Ms

            if query_sequence is None:
                query_sequence = sequences[0]

            if sequences[0] != query_sequence:
                raise ValueError(
                    f'Query sequences must match: {sequences[0]} != {query_sequence}'
                )
            all_sequences.extend(sequences)
            all_descriptions.extend(descriptions)

        if not query_sequence:
            raise ValueError('No valid sequences found in any of the provided A3Ms.')

        return cls(
            query_sequence=query_sequence,
            chain_poly_type=chain_poly_type,
            sequences=all_sequences,
            descriptions=all_descriptions,
            deduplicated=deduplicated,
        )

    @classmethod
    def from_multiple_msas(
        cls,
        msas: Sequence['MSA'],
        deduplicated: bool = True,
    ) -> 'MSA':
        """
        Initializes the MSA from multiple MSA objects.

        Parameters
        ----------
        msas (Sequence[MSA]):
            A sequence of MSA objects representing individual MSAs produced by
            different tools/databases.
        deduplicated (bool, optional):
            If True, the MSA sequences will be deduplicated.
            Defaults to True.

        Returns
        -------
        MSA:
            An MSA object created by merging multiple MSAs.

        Raises
        ------
        ValueError:
            If no MSA objects are provided or if query sequences or chain types don't match.
        """
        if not msas:
            raise ValueError('At least one MSA must be provided.')

        query_sequence = msas[0].query_sequence
        chain_poly_type = msas[0].chain_poly_type
        sequences = []
        descriptions = []

        for msa in msas:
            if msa.query_sequence != query_sequence:
                raise ValueError(
                    f'Query sequences must match: {[m.query_sequence for m in msas]}'
                )
            if msa.chain_poly_type != chain_poly_type:
                raise ValueError(
                    f'Chain poly types must match: {[m.chain_poly_type for m in msas]}'
                )
            sequences.extend(msa.sequences)
            descriptions.extend(msa.descriptions)

        # Ensure chain_poly_type is one of the allowed literal values
        if chain_poly_type not in ('protein', 'rna', 'dna'):
            raise ValueError(f"Invalid chain_poly_type: {chain_poly_type}. Must be one of: 'protein', 'rna', 'dna'")

        # Cast to the correct type for the constructor
        poly_type: Literal['protein', 'rna', 'dna'] = chain_poly_type  # type: ignore

        return cls(
            query_sequence=query_sequence,
            chain_poly_type=poly_type,
            sequences=sequences,
            descriptions=descriptions,
            deduplicated=deduplicated,
        )

    @classmethod
    def from_empty(
        cls,
        query_sequence: str,
        chain_poly_type: Literal['protein', 'rna', 'dna'],
    ) -> 'MSA':
        """
        Creates an empty MSA containing just the query sequence.

        Parameters
        ----------
        query_sequence (str):
            The query sequence.
        chain_poly_type (Literal['protein', 'rna', 'dna']):
            The polymer type of the sequence.

        Returns
        -------
        MSA:
            An MSA object containing only the query sequence.
        """
        return cls(
            query_sequence=query_sequence,
            chain_poly_type=chain_poly_type,
            sequences=[query_sequence],
            descriptions=['Original query'],
            deduplicated=False,
        )

    @classmethod
    def from_file(cls,
        file_path: str,
        chain_poly_type: Literal['protein', 'rna', 'dna'],
        max_depth: Optional[int] = None,
        deduplicated: bool = True,
    ) -> 'MSA':
        """
        Creates an MSA object from a file.

        Supports multiple file formats including FASTA, A3M, and Stockholm.

        Parameters
        ----------
        file_path (str):
            Path to the MSA file.
        chain_poly_type (Literal['protein', 'rna', 'dna']):
            The polymer type of the sequences.
        max_depth (Optional[int], optional):
            If provided, limits the number of sequences to include.
            Defaults to None (include all sequences).
        deduplicated (bool, optional):
            If True, the MSA sequences will be deduplicated.
            Defaults to True.

        Returns
        -------
        MSA:
            An MSA object created from the file.

        Raises
        ------
        FileNotFoundError:
            If the specified file doesn't exist.
        ValueError:
            If the file format is not supported or parsing fails.
        """
        try:
            sequences, descriptions = MSAFileReader.read_file(file_path)
            
            if not sequences:
                raise ValueError(f"No sequences found in file: {file_path}")
            
            # Use the first sequence as query sequence
            query_sequence = sequences[0]
            
            # Crop the MSA if max_depth is specified
            if max_depth is not None and 0 < max_depth < len(sequences):
                logging.info(
                    'MSA cropped from depth of %d to %d for file %s.',
                    len(sequences),
                    max_depth,
                    file_path,
                )
                sequences = sequences[:max_depth]
                descriptions = descriptions[:max_depth]
            
            return cls(
                query_sequence=query_sequence,
                chain_poly_type=chain_poly_type,
                sequences=sequences,
                descriptions=descriptions,
                deduplicated=deduplicated,
            )
            
        except Exception as e:
            raise ValueError(f"Failed to parse MSA file {file_path}: {str(e)}") from e

    def to_a3m(self) -> str:
        """
        Returns the MSA in the A3M format.

        Returns
        -------
        str:
            A string in A3M format representing the MSA.
        """
        a3m_lines = ["#A3M#"]  # Start with A3M header
        for desc, seq in zip(self.descriptions, self.sequences):
            a3m_lines.append(f">{desc}")
            a3m_lines.append(seq)
        return '\n'.join(a3m_lines) + '\n'

    def featurize(self) -> Dict[str, np.ndarray]:
        """
        Featurizes the MSA and returns a map of feature names to features.

        This method converts the MSA sequences into numerical features that can be
        used for machine learning or other computational analyses.

        Returns
        -------
        Dict[str, np.ndarray]:
            A dictionary mapping feature names to their corresponding NumPy arrays.

        Raises
        ------
        Error:
            If there's an error extracting features or if the MSA is empty.
        """
        try:
            # Extract features from sequences
            # This is a simplified implementation - in a real-world scenario,
            # you would use a more sophisticated feature extraction method

            # Create a simple one-hot encoding of the sequences
            max_len = max(len(seq) for seq in self.sequences)
            msa_features = self.encode_sequences(max_len=max_len, encoding_method='onehot')

            # Create a simple deletion matrix (tracking lowercase letters)
            deletion_matrix = np.zeros((len(self.sequences), max_len), dtype=np.int32)
            for i, seq in enumerate(self.sequences):
                for j, char in enumerate(seq):
                    if j < max_len and char in string.ascii_lowercase:
                        deletion_matrix[i, j] = 1

            # Extract species IDs from descriptions (simplified)
            species_ids = np.array([desc.split()[0] if ' ' in desc else desc
                                   for desc in self.descriptions], dtype=object)

            if msa_features.shape[0] == 0:
                raise Error(f"Empty MSA feature for {self}")

            return {
                'msa': msa_features,
                'deletion_matrix_int': deletion_matrix,
                'msa_species_identifiers': species_ids,
                'num_alignments': np.array(self.depth, dtype=np.int32),
            }

        except Exception as e:
            raise Error(f"Error extracting MSA or deletion features: {e}") from e


    ## ======================== ##
    ## -- Clustering Methods -- ##
    ## ======================== ##
    def AFCluster(self,
        output_dir: Optional[str] = None,
        gap_cutoff: float = 0.25,
        min_eps: float = 3.0,
        max_eps: float = 20.0,
        eps_step: float = 0.5,
        min_samples: int = 3,
        save_a3m_files: bool = False,
        keyword: str = "cluster",
    ) -> Dict[str, Any]:
        """
        Clusters sequences in the MSA using DBSCAN algorithm.
        This method is now deprecated. Use to_dbscan_cluster_msa().cluster() instead.

        Parameters
        ----------
        output_dir (Optional[str], optional):
            Directory to save output files. If None, files are not saved.
        gap_cutoff (float, optional):
            Maximum fraction of gaps allowed in a sequence.
        min_eps (float, optional):
            Minimum epsilon value for DBSCAN parameter optimization.
        max_eps (float, optional):
            Maximum epsilon value for DBSCAN parameter optimization.
        eps_step (float, optional):
            Step size for epsilon values during parameter optimization.
        min_samples (int, optional):
            Minimum number of samples in a neighborhood for a point to be considered a core point.
        n_clusters (int, optional):
            Number of top clusters to return.
        save_a3m_files (bool, optional):
            If True, saves A3M files for each cluster.
        keyword (str, optional):
            Prefix for output files.

        Returns
        -------
        Dict[str, Any]:
            A dictionary containing clustering results.

        Notes
        -----
        This method is deprecated. Use to_dbscan_cluster_msa().cluster() instead.
        """
        logging.warning("AFCluster method is deprecated. Use to_dbscan_cluster_msa().cluster() instead.")

        # Convert to DBSCANClusterMSA and call cluster method
        dbscan_cluster_msa = self.to_dbscan_cluster_msa()
        result = dbscan_cluster_msa.cluster(
            output_dir=output_dir,
            gap_cutoff=gap_cutoff,
            min_eps=min_eps,
            max_eps=max_eps,
            eps_step=eps_step,
            min_samples=min_samples,
            save_a3m_files=save_a3m_files,
            keyword=keyword
        )

        # Convert ClusteringResult to dictionary for backward compatibility
        return {
            'clusters': result.clusters,
            'cluster_metadata': result.cluster_metadata,
            'clustering_assignments': result.clustering_assignments,
            'best_params': result.best_params
        }


    def to_dbscan_cluster_msa(self) -> 'DBSCANClusterMSA':
        """
        Converts this MSA object to a DBSCANClusterMSA object.

        This allows using DBSCAN clustering functionality when needed without
        having to create DBSCANClusterMSA objects directly.

        Returns
        -------
        DBSCANClusterMSA:
            A DBSCANClusterMSA object with the same data as this MSA.
        """
        # Import here to avoid circular imports
        try:
            from .dbscan_cluster import DBSCANClusterMSA
        except ImportError:
            from dbscan_cluster import DBSCANClusterMSA

        # Ensure chain_poly_type is one of the allowed literal values
        poly_type: Literal['protein', 'rna', 'dna'] = 'protein'
        if self.chain_poly_type in ('protein', 'rna', 'dna'):
            poly_type = self.chain_poly_type  # type: ignore

        return DBSCANClusterMSA(
            query_sequence=self.query_sequence,
            chain_poly_type=poly_type,
            sequences=self.sequences,
            descriptions=self.descriptions,
            deduplicated=False  # Already deduplicated in the original MSA
        )


    def to_hierarchical_cluster_msa(self) -> 'HierarchicalClusterMSA':
        """
        Converts this MSA object to a HierarchicalClusterMSA object.

        This allows using hierarchical clustering functionality when needed without
        having to create HierarchicalClusterMSA objects directly.

        Returns
        -------
        HierarchicalClusterMSA:
            A HierarchicalClusterMSA object with the same data as this MSA.
        """
        # Import here to avoid circular imports
        try:
            from .hierarchical_cluster import HierarchicalClusterMSA
        except ImportError:
            from hierarchical_cluster import HierarchicalClusterMSA

        # Ensure chain_poly_type is one of the allowed literal values
        poly_type: Literal['protein', 'rna', 'dna'] = 'protein'
        if self.chain_poly_type in ('protein', 'rna', 'dna'):
            poly_type = self.chain_poly_type  # type: ignore

        return HierarchicalClusterMSA(
            query_sequence=self.query_sequence,
            chain_poly_type=poly_type,
            sequences=self.sequences,
            descriptions=self.descriptions,
            deduplicated=False  # Already deduplicated in the original MSA
        )


    def AFsample2(self,
        min_cluster_size: int, # Minimum number of sequences in a sample/cluster
        max_cluster_distance: float, # Maximum distance for sampling/clustering
    ):
        # Placeholder for AFsample2 method
        raise NotImplementedError("AFsample2 method is not yet implemented.")


    ## ===================== ##
    ## -- Utility methods -- ##
    ## ===================== ##
    def _filter_sequences(self, gap_cutoff: float = 0.25
        ) -> Tuple[List[str], List[str], List[int]]:
        """
        Filters sequences based on gap content.

        Parameters
        ----------
        gap_cutoff (float, optional):
            Maximum fraction of gaps allowed in a sequence. Sequences with more gaps will be removed.

        Returns
        -------
        Tuple[List[str], List[str], List[int]]:
            A tuple containing:
            - List of filtered sequences
            - List of corresponding descriptions
            - List of original indices
        """
        filtered_indices = []
        filtered_sequences = []
        filtered_descriptions = []

        for i, (seq, desc) in enumerate(zip(self.sequences, self.descriptions)):
            seq_len = len(seq)
            if seq_len == 0:
                continue  # Skip empty sequences

            # Calculate fraction of gaps
            gap_count = seq.count('-')
            gap_fraction = gap_count / seq_len

            # Include sequence if gap fraction is below cutoff
            if gap_fraction < gap_cutoff:
                filtered_indices.append(i)
                filtered_sequences.append(seq)
                filtered_descriptions.append(desc)

        return filtered_sequences, filtered_descriptions, filtered_indices
    
    @staticmethod
    def _calculate_sequence_similarity(seq1: str, seq2: str) -> float:
        """
        Calculates the similarity between two sequences.

        Uses a simple normalized edit distance approach.

        Parameters
        ----------
        seq1 (str):
            First sequence.
        seq2 (str):
            Second sequence.

        Returns
        -------
        float:
            Similarity score between 0 and 1, where 1 means identical sequences.
        """
        # Ensure sequences are of the same length
        max_len = max(len(seq1), len(seq2))
        if max_len == 0:
            return 1.0  # Empty sequences are considered identical

        # Count matching positions
        matches = sum(1 for a, b in zip(seq1.ljust(max_len, '-'), seq2.ljust(max_len, '-')) if a == b)

        # Return similarity score
        return matches / max_len

    @staticmethod
    def _generate_consensus_sequence(sequences: List[str]) -> str:
        """
        Generates a consensus sequence from a list of sequences.

        Parameters
        ----------
        sequences (List[str]):
            List of sequences to generate consensus from.

        Returns
        -------
        str:
            Consensus sequence.
        """
        if not sequences:
            return ""

        # Determine the length of sequences
        seq_len = len(sequences[0])

        # Check if all sequences have the same length
        if not all(len(seq) == seq_len for seq in sequences):
            logging.warning("Sequences have different lengths. Using the length of the first sequence.")

        # Define the alphabet (standard amino acids + gap)
        alphabet = "ACDEFGHIKLMNPQRSTVWY-"

        # Generate consensus sequence
        consensus = ""
        for i in range(seq_len):
            # Get all characters at this position
            column = [seq[i] if i < len(seq) else '-' for seq in sequences]

            # Count occurrences of each character
            counts = {char: column.count(char) for char in alphabet}

            # Find the most common character
            most_common = max(counts.items(), key=lambda x: x[1])[0]
            consensus += most_common

        return consensus

    @property
    def depth(self) -> int:
        """
        Returns the number of sequences in the MSA.

        Returns
        -------
        int:
            The number of sequences in the MSA.
        """
        return len(self.sequences)

    def __repr__(self) -> str:
        return f'MSA({self.depth} sequences, {self.chain_poly_type})' # Corrected class name in repr

    def get_clustering_manager(self) -> 'ClusteringManager':
        """
        Returns a clustering manager for this MSA instance.
        
        This provides access to the new pluggable clustering framework that supports
        multiple algorithms (DBSCAN, Hierarchical, HDBSCAN) through a unified interface.
        The manager handles algorithm selection, parameter validation, and result 
        standardization, making it easy to compare different clustering approaches.
        
        Example usage:
        ```python
        clustering_mgr = msa.get_clustering_manager()
        
        # Use HDBSCAN clustering
        result = clustering_mgr.cluster('hdbscan', min_cluster_size=15)
        
        # Compare multiple algorithms
        results = clustering_mgr.compare_algorithms(['dbscan', 'hdbscan'])
        
        # Get algorithm information
        info = clustering_mgr.get_algorithm_info('hdbscan')
        ```
        
        Returns
        -------
        ClusteringManager
            A clustering manager instance configured for this MSA object.
        """
        try:
            from .clustering.manager import ClusteringManager
        except ImportError:
            from clustering.manager import ClusteringManager
        return ClusteringManager(self)

    def get_visualizer(self) -> 'MSAVisualizer':
        """
        Returns a visualizer for this MSA instance.
        
        This provides a convenient way to access visualization functionality
        while keeping the core MSA logic separate from visualization code,
        following the single responsibility principle.
        
        Returns
        -------
        MSAVisualizer
            A visualizer instance configured for this MSA object.
        """
        try:
            from .msa_visualizer import MSAVisualizer
        except ImportError:
            from msa_visualizer import MSAVisualizer
        return MSAVisualizer(self)

    def get_analyzer(self) -> 'MSAAnalyzer':
        """
        Returns an analyzer for this MSA instance.
        
        This provides a convenient way to access analysis functionality
        while keeping the core MSA logic separate from analysis code,
        following the single responsibility principle.
        
        Returns
        -------
        MSAAnalyzer
            An analyzer instance configured for this MSA object.
        """
        # Import here to avoid circular imports
        try:
            from .msa_analyzer import MSAAnalyzer
        except ImportError:
            from msa_analyzer import MSAAnalyzer
        return MSAAnalyzer(self)

    def sample_sequences(self, 
        n_samples: int,
        random_seed: Optional[int] = 42,
        keep_query: bool = True
    ) -> 'MSA':
        """
        Create a sampled version of the MSA for testing purposes.
        
        Parameters
        ----------
        n_samples : int
            Number of sequences to sample.
        random_seed : Optional[int], optional
            Random seed for reproducible sampling. Defaults to 42.
        keep_query : bool, optional
            Whether to always keep the query sequence. Defaults to True.
            
        Returns
        -------
        MSA
            A new MSA object with sampled sequences.
        """
        if n_samples >= len(self.sequences):
            logging.warning(f"Requested {n_samples} samples, but MSA only has {len(self.sequences)} sequences. Returning original MSA.")
            return self
            
        import random
        if random_seed is not None:
            random.seed(random_seed)
            
        # Determine which sequences to sample
        if keep_query and len(self.sequences) > 0:
            # Always include first sequence (query) and sample n_samples-1 from the rest
            remaining_indices = list(range(1, len(self.sequences)))
            if n_samples > 1:
                sampled_indices = random.sample(remaining_indices, min(n_samples - 1, len(remaining_indices)))
                selected_indices = [0] + sampled_indices
            else:
                selected_indices = [0]
        else:
            # Sample n_samples from all sequences
            selected_indices = random.sample(range(len(self.sequences)), n_samples)
            
        # Create sampled sequences and descriptions
        sampled_sequences = [self.sequences[i] for i in selected_indices]
        sampled_descriptions = [self.descriptions[i] for i in selected_indices]
        
        # Create new MSA object with sampled data
        sampled_msa = MSA(
            query_sequence=self.query_sequence,
            chain_poly_type=self.chain_poly_type,
            sequences=sampled_sequences,
            descriptions=sampled_descriptions,
            deduplicated=False  # Already processed
        )
        
        logging.info(f"Created sampled MSA with {len(sampled_sequences)} sequences from original {len(self.sequences)}")
        return sampled_msa
