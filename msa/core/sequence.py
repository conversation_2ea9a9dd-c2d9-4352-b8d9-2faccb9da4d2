"""
Individual sequence management class.

This module provides a class for managing individual protein sequences
with validation, manipulation, and utility methods.
"""

import re
from typing import Optional, Dict, Any, Set
from dataclasses import dataclass

from .data_structures import SequenceData


class Sequence:
    """
    Class for managing individual protein sequences.
    
    This class provides validation, manipulation, and utility methods
    for individual sequences, consolidating sequence handling logic.
    """
    
    # Standard amino acid alphabet
    STANDARD_AA = "ACDEFGHIKLMNPQRSTVWY"
    EXTENDED_AA = "ACDEFGHIKLMNPQRSTVWYUXBZOJ"
    GAP_CHARS = "-."
    
    def __init__(self, 
                 sequence: str, 
                 description: str,
                 index: Optional[int] = None,
                 is_query: bool = False,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize sequence object.
        
        Parameters
        ----------
        sequence : str
            Amino acid sequence
        description : str
            Sequence description/identifier
        index : Optional[int]
            Sequence index in MSA
        is_query : bool
            Whether this is the query sequence
        metadata : Optional[Dict[str, Any]]
            Additional metadata
        """
        self._data = SequenceData(
            sequence=sequence,  # Preserve original case (including lowercase insertions)
            description=description,
            index=index,
            is_query=is_query,
            metadata=metadata or {}
        )
        
        self._validate_sequence()
    
    @property
    def sequence(self) -> str:
        """Get the sequence string."""
        return self._data.sequence
    
    @property
    def description(self) -> str:
        """Get the sequence description."""
        return self._data.description
    
    @property
    def index(self) -> Optional[int]:
        """Get the sequence index."""
        return self._data.index
    
    @property
    def is_query(self) -> bool:
        """Check if this is the query sequence."""
        return self._data.is_query
    
    @property
    def metadata(self) -> Dict[str, Any]:
        """Get sequence metadata."""
        return self._data.metadata
    
    @property
    def length(self) -> int:
        """Get sequence length."""
        return len(self.sequence)
    
    @property
    def ungapped_length(self) -> int:
        """Get sequence length without gaps."""
        return len(self.remove_gaps())
    
    @property
    def gap_content(self) -> float:
        """Get gap content as fraction."""
        return self._data.gap_content
    
    def _validate_sequence(self) -> None:
        """Validate the sequence."""
        if not self.sequence:
            raise ValueError("Sequence cannot be empty")
        
        # Check for valid characters (amino acids + gaps + special chars)
        # Include both uppercase and lowercase for insertions
        valid_chars = set(self.EXTENDED_AA + self.EXTENDED_AA.lower() + self.GAP_CHARS + "X*x")
        invalid_chars = set(self.sequence) - valid_chars
        
        if invalid_chars:
            raise ValueError(f"Invalid characters in sequence: {invalid_chars}")
    
    def remove_gaps(self) -> str:
        """
        Remove gap characters from sequence.
        
        Returns
        -------
        str
            Sequence without gaps
        """
        gap_pattern = f"[{re.escape(self.GAP_CHARS)}]"
        return re.sub(gap_pattern, "", self.sequence)
    
    def get_valid_positions(self) -> list:
        """
        Get positions of non-gap characters.
        
        Returns
        -------
        list
            List of positions with valid amino acids
        """
        return [i for i, char in enumerate(self.sequence) 
                if char not in self.GAP_CHARS]
    
    def get_conservation_mask(self, other_sequences: list) -> list:
        """
        Get conservation mask comparing with other sequences.
        
        Parameters
        ----------
        other_sequences : list
            List of other Sequence objects for comparison
            
        Returns
        -------
        list
            Boolean mask indicating conserved positions
        """
        if not other_sequences:
            return [True] * self.length
        
        conservation_mask = []
        for i in range(self.length):
            if i >= self.length:
                conservation_mask.append(False)
                continue
                
            this_char = self.sequence[i]
            if this_char in self.GAP_CHARS:
                conservation_mask.append(False)
                continue
            
            # Check conservation with other sequences
            conserved = True
            for other_seq in other_sequences:
                if (i >= other_seq.length or 
                    other_seq.sequence[i] != this_char or
                    other_seq.sequence[i] in self.GAP_CHARS):
                    conserved = False
                    break
            
            conservation_mask.append(conserved)
        
        return conservation_mask
    
    def calculate_identity(self, other: 'Sequence') -> float:
        """
        Calculate sequence identity with another sequence.
        
        Parameters
        ----------
        other : Sequence
            Other sequence to compare with
            
        Returns
        -------
        float
            Sequence identity as fraction (0-1)
        """
        if not isinstance(other, Sequence):
            raise TypeError("Comparison object must be a Sequence instance")
        
        # Use shorter length for comparison
        min_length = min(self.length, other.length)
        if min_length == 0:
            return 0.0
        
        identical_positions = 0
        for i in range(min_length):
            if (self.sequence[i] == other.sequence[i] and
                self.sequence[i] not in self.GAP_CHARS):
                identical_positions += 1
        
        return identical_positions / min_length
    
    def slice_sequence(self, start: int, end: int) -> 'Sequence':
        """
        Create a new sequence object with sliced sequence.
        
        Parameters
        ----------
        start : int
            Start position (0-indexed)
        end : int
            End position (0-indexed, exclusive)
            
        Returns
        -------
        Sequence
            New sequence object with sliced sequence
        """
        sliced_seq = self.sequence[start:end]
        new_description = f"{self.description}_slice_{start}_{end}"
        
        return Sequence(
            sequence=sliced_seq,
            description=new_description,
            index=self.index,
            is_query=self.is_query,
            metadata=self.metadata.copy()
        )
    
    def to_fasta(self) -> str:
        """
        Convert to FASTA format.
        
        Returns
        -------
        str
            FASTA formatted string
        """
        return f">{self.description}\n{self.sequence}"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary representation.
        
        Returns
        -------
        Dict[str, Any]
            Dictionary representation of the sequence
        """
        return self._data.to_dict()
    
    @classmethod
    def from_fasta_entry(cls, 
                        fasta_entry: str, 
                        index: Optional[int] = None) -> 'Sequence':
        """
        Create sequence from FASTA entry.
        
        Parameters
        ----------
        fasta_entry : str
            FASTA formatted entry (header + sequence)
        index : Optional[int]
            Sequence index
            
        Returns
        -------
        Sequence
            New sequence object
        """
        lines = fasta_entry.strip().split('\n')
        if len(lines) < 2:
            raise ValueError("Invalid FASTA entry format")
        
        description = lines[0][1:]  # Remove '>' character
        sequence = ''.join(lines[1:])  # Join multi-line sequences
        
        return cls(
            sequence=sequence,
            description=description,
            index=index
        )
    
    @classmethod
    def from_sequence_data(cls, data: SequenceData) -> 'Sequence':
        """
        Create sequence from SequenceData object.
        
        Parameters
        ----------
        data : SequenceData
            SequenceData object
            
        Returns
        -------
        Sequence
            New sequence object
        """
        return cls(
            sequence=data.sequence,
            description=data.description,
            index=data.index,
            is_query=data.is_query,
            metadata=data.metadata
        )
    
    def __str__(self) -> str:
        """String representation."""
        return f"Sequence({self.description}, length={self.length})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return (f"Sequence(description='{self.description}', "
                f"length={self.length}, "
                f"gap_content={self.gap_content:.2f}, "
                f"is_query={self.is_query})")
    
    def __len__(self) -> int:
        """Get sequence length."""
        return self.length
    
    def __eq__(self, other) -> bool:
        """Check equality with another sequence."""
        if not isinstance(other, Sequence):
            return False
        return (self.sequence == other.sequence and 
                self.description == other.description)
    
    def __hash__(self) -> int:
        """Hash based on sequence and description."""
        return hash((self.sequence, self.description)) 