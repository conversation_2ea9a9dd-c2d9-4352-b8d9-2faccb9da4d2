"""
Core MSA data management class.

This module provides a lightweight MSA class focused on data management
and basic operations, extracting essential functionality from the bloated
msa_pipeline.py file.
"""

import logging
from typing import List, Optional, Dict, Any, Union, Tuple
from pathlib import Path

from .sequence import Sequence
from .data_structures import SequenceData, MSAMetadata, ChainPolyType


class MSACore:
    """
    Lightweight core class for MSA data management.
    
    This class focuses on essential MSA data operations without the bloat
    of the original MSA class. It handles sequence storage, basic filtering,
    and metadata management.
    """
    
    def __init__(self, 
                 sequences: Optional[List[Union[Sequence, SequenceData, str]]] = None,
                 descriptions: Optional[List[str]] = None,
                 chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN,
                 metadata: Optional[MSAMetadata] = None):
        """
        Initialize MSA core object.
        
        Parameters
        ----------
        sequences : Optional[List[Union[Sequence, SequenceData, str]]]
            List of sequences (can be Sequence objects, SequenceData objects, or strings)
        descriptions : Optional[List[str]]
            List of sequence descriptions (used when sequences are strings)
        chain_poly_type : ChainPolyType
            Type of polymer (protein, RNA, DNA)
        metadata : Optional[MSAMetadata]
            MSA metadata object
        """
        self._sequences: List[Sequence] = []
        self._chain_poly_type = chain_poly_type
        self._metadata: Optional[MSAMetadata] = metadata
        
        # Initialize sequences if provided
        if sequences:
            self._initialize_sequences(sequences, descriptions)
        
        # Update metadata after sequence initialization
        self._update_metadata()
    
    def _initialize_sequences(self, 
                            sequences: List[Union[Sequence, SequenceData, str]], 
                            descriptions: Optional[List[str]] = None) -> None:
        """Initialize sequences from various input types."""
        for i, seq in enumerate(sequences):
            if isinstance(seq, Sequence):
                # Copy existing sequence with updated index
                seq._data.index = i
                self._sequences.append(seq)
            elif isinstance(seq, SequenceData):
                # Create sequence from SequenceData
                seq.index = i
                self._sequences.append(Sequence.from_sequence_data(seq))
            elif isinstance(seq, str):
                # Create sequence from string
                description = descriptions[i] if descriptions and i < len(descriptions) else f"seq_{i}"
                self._sequences.append(Sequence(
                    sequence=seq,
                    description=description,
                    index=i,
                    is_query=(i == 0)  # First sequence is query by default
                ))
            else:
                raise TypeError(f"Unsupported sequence type: {type(seq)}")
    
    def _update_metadata(self) -> None:
        """Update metadata based on current sequences."""
        if not self._sequences:
            return
        
        lengths = [seq.length for seq in self._sequences]
        query_length = self._sequences[0].length if self._sequences else 0
        
        self._metadata = MSAMetadata(
            chain_poly_type=self._chain_poly_type,
            total_sequences=len(self._sequences),
            query_sequence_length=query_length,
            max_sequence_length=max(lengths) if lengths else 0,
            min_sequence_length=min(lengths) if lengths else 0,
            deduplicated=False  # Will be updated when deduplication is performed
        )
    
    @property
    def sequences(self) -> List[Sequence]:
        """Get list of sequences."""
        return self._sequences.copy()
    
    @property
    def sequence_strings(self) -> List[str]:
        """Get list of sequence strings."""
        return [seq.sequence for seq in self._sequences]
    
    @property
    def descriptions(self) -> List[str]:
        """Get list of sequence descriptions."""
        return [seq.description for seq in self._sequences]
    
    @property
    def query_sequence(self) -> Optional[Sequence]:
        """Get the query sequence (first sequence)."""
        return self._sequences[0] if self._sequences else None
    
    @property
    def chain_poly_type(self) -> ChainPolyType:
        """Get chain polymer type."""
        return self._chain_poly_type
    
    @property
    def metadata(self) -> Optional[MSAMetadata]:
        """Get MSA metadata."""
        return self._metadata
    
    @property
    def n_sequences(self) -> int:
        """Get number of sequences."""
        return len(self._sequences)
    
    @property
    def max_length(self) -> int:
        """Get maximum sequence length."""
        return max((seq.length for seq in self._sequences), default=0)
    
    @property
    def min_length(self) -> int:
        """Get minimum sequence length."""
        return min((seq.length for seq in self._sequences), default=0)
    
    def add_sequence(self, 
                    sequence: Union[Sequence, str], 
                    description: Optional[str] = None,
                    is_query: bool = False) -> None:
        """
        Add a sequence to the MSA.
        
        Parameters
        ----------
        sequence : Union[Sequence, str]
            Sequence to add
        description : Optional[str]
            Sequence description (required if sequence is string)
        is_query : bool
            Whether this is a query sequence
        """
        if isinstance(sequence, Sequence):
            # Update index
            sequence._data.index = len(self._sequences)
            if is_query:
                sequence._data.is_query = True
            self._sequences.append(sequence)
        elif isinstance(sequence, str):
            if description is None:
                description = f"seq_{len(self._sequences)}"
            
            new_seq = Sequence(
                sequence=sequence,
                description=description,
                index=len(self._sequences),
                is_query=is_query
            )
            self._sequences.append(new_seq)
        else:
            raise TypeError(f"Unsupported sequence type: {type(sequence)}")
        
        # Update metadata
        self._update_metadata()
    
    def remove_sequence(self, index: int) -> None:
        """
        Remove sequence by index.
        
        Parameters
        ----------
        index : int
            Index of sequence to remove
        """
        if not 0 <= index < len(self._sequences):
            raise IndexError(f"Index {index} out of range for {len(self._sequences)} sequences")
        
        self._sequences.pop(index)
        
        # Update indices for remaining sequences
        for i, seq in enumerate(self._sequences):
            seq._data.index = i
        
        # Update metadata
        self._update_metadata()
    
    def filter_by_length(self, 
                        min_length: Optional[int] = None,
                        max_length: Optional[int] = None,
                        keep_query: bool = True) -> 'MSACore':
        """
        Filter sequences by length.
        
        Parameters
        ----------
        min_length : Optional[int]
            Minimum sequence length
        max_length : Optional[int]
            Maximum sequence length  
        keep_query : bool
            Whether to always keep the query sequence
            
        Returns
        -------
        MSACore
            New MSACore with filtered sequences
        """
        filtered_sequences = []
        
        for seq in self._sequences:
            # Always keep query if requested
            if keep_query and seq.is_query:
                filtered_sequences.append(seq)
                continue
            
            # Apply length filters
            if min_length is not None and seq.length < min_length:
                continue
            if max_length is not None and seq.length > max_length:
                continue
            
            filtered_sequences.append(seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=self._chain_poly_type
        )
    
    def filter_by_gap_content(self, 
                             max_gap_content: float,
                             keep_query: bool = True) -> 'MSACore':
        """
        Filter sequences by gap content.
        
        Parameters
        ----------
        max_gap_content : float
            Maximum allowed gap content (0-1)
        keep_query : bool
            Whether to always keep the query sequence
            
        Returns
        -------
        MSACore
            New MSACore with filtered sequences
        """
        filtered_sequences = []
        
        for seq in self._sequences:
            # Always keep query if requested
            if keep_query and seq.is_query:
                filtered_sequences.append(seq)
                continue
            
            # Apply gap content filter
            if seq.gap_content <= max_gap_content:
                filtered_sequences.append(seq)
        
        return MSACore(
            sequences=filtered_sequences,
            chain_poly_type=self._chain_poly_type
        )
    
    def deduplicate(self, identity_threshold: float = 1.0) -> 'MSACore':
        """
        Remove duplicate sequences based on identity threshold.
        
        Parameters
        ----------
        identity_threshold : float
            Identity threshold for considering sequences as duplicates (0-1)
            
        Returns
        -------
        MSACore
            New MSACore with deduplicated sequences
        """
        if not self._sequences:
            return MSACore(chain_poly_type=self._chain_poly_type)
        
        unique_sequences = [self._sequences[0]]  # Always keep first sequence (query)
        
        for seq in self._sequences[1:]:
            is_duplicate = False
            
            for unique_seq in unique_sequences:
                identity = seq.calculate_identity(unique_seq)
                if identity >= identity_threshold:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_sequences.append(seq)
        
        # Create new MSA with updated metadata
        new_msa = MSACore(
            sequences=unique_sequences,
            chain_poly_type=self._chain_poly_type
        )
        
        # Update deduplication status in metadata
        if new_msa._metadata:
            new_msa._metadata.deduplicated = True
            new_msa._metadata.add_processing_note(
                f"Deduplicated with identity threshold {identity_threshold}"
            )
        
        return new_msa
    
    def slice_sequences(self, start: int, end: int) -> 'MSACore':
        """
        Slice all sequences to a specific range.
        
        Parameters
        ----------
        start : int
            Start position (0-indexed)
        end : int
            End position (0-indexed, exclusive)
            
        Returns
        -------
        MSACore
            New MSACore with sliced sequences
        """
        sliced_sequences = [seq.slice_sequence(start, end) for seq in self._sequences]
        
        return MSACore(
            sequences=sliced_sequences,
            chain_poly_type=self._chain_poly_type
        )
    
    def get_sequence_by_index(self, index: int) -> Sequence:
        """
        Get sequence by index.
        
        Parameters
        ----------
        index : int
            Sequence index
            
        Returns
        -------
        Sequence
            Sequence object at the specified index
        """
        if not 0 <= index < len(self._sequences):
            raise IndexError(f"Index {index} out of range for {len(self._sequences)} sequences")
        
        return self._sequences[index]
    
    def get_sequence_by_description(self, description: str) -> Optional[Sequence]:
        """
        Get sequence by description.
        
        Parameters
        ----------
        description : str
            Sequence description to search for
            
        Returns
        -------
        Optional[Sequence]
            Sequence object with matching description, or None if not found
        """
        for seq in self._sequences:
            if seq.description == description:
                return seq
        return None
    
    def to_fasta(self) -> str:
        """
        Convert MSA to FASTA format.
        
        Returns
        -------
        str
            FASTA formatted string
        """
        return '\n'.join(seq.to_fasta() for seq in self._sequences)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert MSA to dictionary representation.
        
        Returns
        -------
        Dict[str, Any]
            Dictionary representation of MSA
        """
        return {
            'sequences': [seq.to_dict() for seq in self._sequences],
            'chain_poly_type': self._chain_poly_type.value,
            'metadata': self._metadata.to_dict() if self._metadata else None,
            'n_sequences': self.n_sequences,
            'max_length': self.max_length,
            'min_length': self.min_length
        }
    
    @classmethod
    def from_fasta(cls, 
                  fasta_content: str,
                  chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN) -> 'MSACore':
        """
        Create MSA from FASTA content.
        
        Parameters
        ----------
        fasta_content : str
            FASTA formatted content
        chain_poly_type : ChainPolyType
            Type of polymer
            
        Returns
        -------
        MSACore
            New MSACore object
        """
        sequences = []
        current_seq = []
        current_desc = None
        
        for line in fasta_content.strip().split('\n'):
            line = line.strip()
            if line.startswith('>'):
                # Save previous sequence if exists
                if current_desc is not None and current_seq:
                    sequences.append(Sequence(
                        sequence=''.join(current_seq),
                        description=current_desc,
                        index=len(sequences),
                        is_query=(len(sequences) == 0)
                    ))
                
                # Start new sequence
                current_desc = line[1:]
                current_seq = []
            else:
                current_seq.append(line)
        
        # Add last sequence
        if current_desc is not None and current_seq:
            sequences.append(Sequence(
                sequence=''.join(current_seq),
                description=current_desc,
                index=len(sequences),
                is_query=(len(sequences) == 0)
            ))
        
        return cls(sequences=sequences, chain_poly_type=chain_poly_type)
    
    @classmethod
    def from_file(cls, 
                 file_path: Union[str, Path],
                 chain_poly_type: ChainPolyType = ChainPolyType.PROTEIN) -> 'MSACore':
        """
        Create MSA from file.
        
        Parameters
        ----------
        file_path : Union[str, Path]
            Path to FASTA file
        chain_poly_type : ChainPolyType
            Type of polymer
            
        Returns
        -------
        MSACore
            New MSACore object
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        with open(file_path, 'r') as f:
            content = f.read()
        
        msa = cls.from_fasta(content, chain_poly_type)
        
        # Update metadata with source file
        if msa._metadata:
            msa._metadata.source_file = str(file_path)
        
        return msa
    
    def __len__(self) -> int:
        """Get number of sequences."""
        return len(self._sequences)
    
    def __iter__(self):
        """Iterate over sequences."""
        return iter(self._sequences)
    
    def __getitem__(self, index: int) -> Sequence:
        """Get sequence by index."""
        return self._sequences[index]
    
    def __str__(self) -> str:
        """String representation."""
        return f"MSACore({self.n_sequences} sequences, max_length={self.max_length})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return (f"MSACore(n_sequences={self.n_sequences}, "
                f"max_length={self.max_length}, "
                f"chain_poly_type={self.chain_poly_type.value})") 