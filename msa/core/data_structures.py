"""
Common data structures for MSA processing.

This module defines shared data structures used throughout the MSA package,
improving consistency and reducing duplication.
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Literal
from enum import Enum


class ChainPolyType(Enum):
    """Enumeration of chain polymer types."""
    PROTEIN = "protein"
    RNA = "rna"
    DNA = "dna"


@dataclass
class SequenceData:
    """
    Data structure for storing sequence information.
    
    This replaces the scattered sequence handling throughout the codebase.
    """
    sequence: str
    description: str
    index: Optional[int] = None
    is_query: bool = False
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Post-initialization validation."""
        if not self.sequence:
            raise ValueError("Sequence cannot be empty")
        if not self.description:
            raise ValueError("Description cannot be empty")
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def length(self) -> int:
        """Get sequence length."""
        return len(self.sequence)
    
    @property
    def gap_content(self) -> float:
        """Calculate gap content as a fraction."""
        gap_chars = '-.'
        gap_count = sum(1 for char in self.sequence if char in gap_chars)
        return gap_count / len(self.sequence) if len(self.sequence) > 0 else 0.0
    
    def has_lowercase(self) -> bool:
        """Check if sequence contains lowercase characters."""
        return any(char.islower() for char in self.sequence)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'sequence': self.sequence,
            'description': self.description,
            'index': self.index,
            'is_query': self.is_query,
            'length': self.length,
            'gap_content': self.gap_content,
            'has_lowercase': self.has_lowercase(),
            'metadata': self.metadata
        }


@dataclass
class MSAMetadata:
    """
    Metadata for MSA objects.
    
    This consolidates metadata handling that was previously scattered.
    """
    chain_poly_type: ChainPolyType
    total_sequences: int
    query_sequence_length: int
    max_sequence_length: int
    min_sequence_length: int
    deduplicated: bool = True
    creation_timestamp: Optional[str] = None
    source_file: Optional[str] = None
    processing_notes: Optional[List[str]] = None
    custom_metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Post-initialization setup."""
        if self.processing_notes is None:
            self.processing_notes = []
        if self.custom_metadata is None:
            self.custom_metadata = {}
        
        # Validation
        if self.total_sequences < 1:
            raise ValueError("Total sequences must be at least 1")
        if self.query_sequence_length < 1:
            raise ValueError("Query sequence length must be at least 1")
    
    def add_processing_note(self, note: str) -> None:
        """Add a processing note."""
        self.processing_notes.append(note)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'chain_poly_type': self.chain_poly_type.value,
            'total_sequences': self.total_sequences,
            'query_sequence_length': self.query_sequence_length,
            'max_sequence_length': self.max_sequence_length,
            'min_sequence_length': self.min_sequence_length,
            'deduplicated': self.deduplicated,
            'creation_timestamp': self.creation_timestamp,
            'source_file': self.source_file,
            'processing_notes': self.processing_notes,
            'custom_metadata': self.custom_metadata
        }


@dataclass
class ClusteringResultData:
    """
    Standardized clustering result data structure.
    
    This replaces the multiple ClusteringResult classes scattered throughout.
    """
    cluster_labels: List[int]
    algorithm_name: str
    algorithm_params: Dict[str, Any]
    n_clusters: int
    n_noise: int
    metadata: Dict[str, Any]
    
    # Optional additional data
    cluster_centers: Optional[List[Any]] = None
    cluster_probabilities: Optional[List[float]] = None
    silhouette_score: Optional[float] = None
    
    def __post_init__(self):
        """Post-initialization validation."""
        if len(self.cluster_labels) == 0:
            raise ValueError("Cluster labels cannot be empty")
        if self.n_clusters < 0:
            raise ValueError("Number of clusters cannot be negative")
        if self.n_noise < 0:
            raise ValueError("Number of noise points cannot be negative")
    
    @property
    def total_points(self) -> int:
        """Get total number of points."""
        return len(self.cluster_labels)
    
    @property
    def clustered_points(self) -> int:
        """Get number of points assigned to clusters."""
        return self.total_points - self.n_noise
    
    @property
    def noise_percentage(self) -> float:
        """Get percentage of noise points."""
        return (self.n_noise / self.total_points * 100) if self.total_points > 0 else 0.0
    
    def get_cluster_sizes(self) -> Dict[int, int]:
        """Get sizes of each cluster."""
        cluster_sizes = {}
        for label in self.cluster_labels:
            if label >= 0:  # Exclude noise points (-1)
                cluster_sizes[label] = cluster_sizes.get(label, 0) + 1
        return cluster_sizes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'cluster_labels': self.cluster_labels,
            'algorithm_name': self.algorithm_name,
            'algorithm_params': self.algorithm_params,
            'n_clusters': self.n_clusters,
            'n_noise': self.n_noise,
            'total_points': self.total_points,
            'noise_percentage': self.noise_percentage,
            'cluster_sizes': self.get_cluster_sizes(),
            'metadata': self.metadata
        } 