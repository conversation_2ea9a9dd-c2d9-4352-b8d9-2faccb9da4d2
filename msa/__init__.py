"""
MSA (Multiple Sequence Alignment) Analysis Package.

This package provides comprehensive tools for MSA analysis including:
- Core data structures and sequence handling
- File I/O supporting multiple formats  
- Advanced encoding methods
- Statistical analysis and metrics
- Clustering algorithms
- Visualization tools
- Processing utilities

The package is designed with modular architecture for maintainability
while providing both low-level components and high-level convenience functions.
"""

# Core functionality
from .core.msa_core import <PERSON><PERSON><PERSON>
from .core.sequence import Sequence
from .core.data_structures import ChainPolyType

# I/O functionality
from .io.readers import MSAReader, read_msa_file, read_sequences_from_file
from .io.writers import MSAWriter, write_msa_file, write_sequences_to_file
from .io.converters import FormatConverter, convert_msa_file
from .io.formats import SupportedFormat, FormatDetector

# Encoding functionality
from .encoding.onehot_encoder import OneHotEncoder
from .encoding.esm_embedding import ESMEmbedder
from .encoding.sequence_encoder import SequenceEncoder, EncodingMethod, EncodingConfig

# Analysis functionality
from .analysis.statistics import MSAStatistics
from .analysis.metrics import MSAMetrics
from .analysis.comparative import ComparativeAnalyzer
from .analysis.diversity import DiversityA<PERSON>yzer

# Visualization functionality
from .visualization.plots import MS<PERSON>lotter
from .visualization.charts import MS<PERSON>harts
from .visualization.clustering import ClusteringVisualizer
from .visualization.config import VisualizationConfig, PresetConfigs

# Clustering functionality
from .clustering.manager import ClusteringManager
from .clustering.interfaces import ClusteringResult

# Processing functionality
from .processing.filters import MSAFilter
from .processing.preprocessor import MSAPreprocessor
from .processing.postprocessor import MSAPostprocessor

# Legacy compatibility
from .msa_pipeline import MSA

__version__ = "2.0.0"

__all__ = [
    # Core
    'MSACore',
    'Sequence', 
    'ChainPolyType',
    
    # I/O
    'MSAReader',
    'MSAWriter',
    'FormatConverter',
    'SupportedFormat',
    'FormatDetector',
    'read_msa_file',
    'write_msa_file',
    'convert_msa_file',
    'read_sequences_from_file',
    'write_sequences_to_file',
    
    # Encoding
    'OneHotEncoder',
    'ESMEmbedder',
    'SequenceEncoder',
    'EncodingMethod',
    'EncodingConfig',
    
    # Analysis
    'MSAStatistics',
    'MSAMetrics', 
    'ComparativeAnalyzer',
    'DiversityAnalyzer',
    
    # Visualization
    'MSAPlotter',
    'MSACharts',
    'ClusteringVisualizer',
    'VisualizationConfig',
    'PresetConfigs',
    
    # Clustering
    'ClusteringManager',
    'ClusteringResult',
    
    # Processing
    'MSAFilter',
    'MSAPreprocessor',
    'MSAPostprocessor',
    
    # Legacy
    'MSA'
]


# Convenience functions for common workflows
def load_msa(file_path: str, **kwargs) -> MSACore:
    """
    Load MSA from file with automatic format detection.
    
    Parameters
    ----------
    file_path : str
        Path to MSA file
    **kwargs
        Additional arguments passed to MSAReader.read_file()
        Common kwargs: remove_lowercase=False (preserve lowercase insertions)
        
    Returns
    -------
    MSACore
        Loaded MSA object
    """
    reader = MSAReader()
    return reader.read_file(file_path, **kwargs)


def save_msa(msa: MSACore, file_path: str, **kwargs) -> None:
    """
    Save MSA to file.
    
    Parameters
    ----------
    msa : MSACore
        MSA object to save
    file_path : str
        Output file path
    **kwargs
        Additional arguments passed to MSAWriter.write_file()
    """
    writer = MSAWriter()
    writer.write_file(msa, file_path, **kwargs)


def analyze_msa(msa: MSACore, include_diversity: bool = True) -> dict:
    """
    Perform comprehensive MSA analysis.
    
    Parameters
    ----------
    msa : MSACore
        MSA to analyze
    include_diversity : bool
        Whether to include diversity analysis
        
    Returns
    -------
    dict
        Analysis results including statistics, metrics, and optionally diversity
    """
    results = {}
    
    # Basic statistics
    stats_analyzer = MSAStatistics()
    results['statistics'] = stats_analyzer.calculate_comprehensive_stats(msa)
    
    # Quality metrics
    metrics_analyzer = MSAMetrics()
    results['metrics'] = metrics_analyzer.calculate_comprehensive_metrics(msa)
    
    # Diversity analysis
    if include_diversity:
        diversity_analyzer = DiversityAnalyzer()
        results['diversity'] = diversity_analyzer.calculate_comprehensive_diversity(msa)
    
    return results


def visualize_msa(msa: MSACore, output_dir: str, prefix: str = "", config: VisualizationConfig = None) -> None:
    """
    Create comprehensive MSA visualizations.
    
    Parameters
    ----------
    msa : MSACore
        MSA to visualize
    output_dir : str
        Output directory for plots
    prefix : str
        Filename prefix
    config : VisualizationConfig
        Visualization configuration
    """
    # Create all basic plots
    plotter = MSAPlotter(config)
    plotter.plot_all_basic(msa, output_dir, prefix)
    
    # Create statistical charts
    charts = MSACharts(config)
    charts.plot_all_charts(msa, output_dir, prefix)


def cluster_msa(msa: MSACore, algorithm: str = 'dbscan', **kwargs) -> ClusteringResult:
    """
    Perform MSA clustering.
    
    Parameters
    ----------
    msa : MSACore
        MSA to cluster
    algorithm : str
        Clustering algorithm ('dbscan' or 'hierarchical')
    **kwargs
        Algorithm-specific parameters
        
    Returns
    -------
    ClusteringResult
        Clustering results
    """
    manager = ClusteringManager(msa)
    return manager.cluster(algorithm, **kwargs) 