#!/bin/bash

# Batch MSA Contamination Analysis Script
# This script performs contamination analysis for all modeling systems
# defined in MODELING_SYSTEMS.config

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="/home/<USER>/PROject/APP-complex/Initial_Input"
CONFIG_FILE="${SCRIPT_DIR}/MODELING_SYSTEMS.config"
ANALYSIS_SCRIPT="${SCRIPT_DIR}/analyze_msa_contamination.py"
LOG_DIR="${SCRIPT_DIR}/contamination_logs"
REPORT_DIR="${SCRIPT_DIR}/contamination_reports"

# Default options
DRY_RUN=true
N_WORKERS=0
VERBOSE=false

# Create output directories
mkdir -p "${LOG_DIR}" "${REPORT_DIR}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Help function
show_help() {
    cat << EOF
Batch MSA Contamination Analysis Script

USAGE:
    $0 [OPTIONS]

DESCRIPTION:
    This script performs MSA contamination analysis for all modeling systems
    defined in MODELING_SYSTEMS.config. It analyzes both Cluster_wo_tmpl and
    Cluster_w_tmpl directories for each system.

OPTIONS:
    --dry-run           Perform dry run without actual cleanup (default: true)
    --no-dry-run        Perform actual cleanup of contaminated directories
    --n-workers N       Number of parallel workers (0 for auto-detection, default: 0)
    --verbose           Enable verbose logging
    --help, -h          Show this help message

EXAMPLES:
    # Dry run analysis (safe, no cleanup)
    $0 --dry-run

    # Actual analysis with cleanup
    $0 --no-dry-run

    # Use 8 workers for parallel processing
    $0 --no-dry-run --n-workers 8

    # Verbose dry run
    $0 --dry-run --verbose

OUTPUT:
    - Individual reports: contamination_reports/
    - Log files: contamination_logs/
    - Summary report: contamination_reports/contamination_summary.txt

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --no-dry-run)
                DRY_RUN=false
                shift
                ;;
            --n-workers)
                N_WORKERS="$2"
                shift 2
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Function to check if directory exists
check_directory() {
    local dir_path="$1"
    local dir_type="$2"
    
    if [[ -d "$dir_path" ]]; then
        if $VERBOSE; then
            log_info "Found $dir_type: $dir_path"
        fi
        return 0
    else
        log_warning "$dir_type not found: $dir_path"
        return 1
    fi
}

# Function to run contamination analysis
run_contamination_analysis() {
    local target_dir="$1"
    local original_msa_dir="$2"
    local system_name="$3"
    local analysis_type="$4"  # wo_tmpl or w_tmpl
    
    local output_report="${REPORT_DIR}/${system_name//\//_}_${analysis_type}_contamination.tsv"
    local log_file="${LOG_DIR}/${system_name//\//_}_${analysis_type}.log"
    
    log_info "Starting contamination analysis for $system_name ($analysis_type)"
    if $VERBOSE; then
        log_info "Target: $target_dir"
        log_info "Original MSA: $original_msa_dir"
        log_info "Output report: $output_report"
        log_info "Log file: $log_file"
    fi
    
    # Prepare analysis command
    local cmd_args=(
        "$ANALYSIS_SCRIPT"
        "$target_dir"
        "--original-msa-dir" "$original_msa_dir"
        "--output-report" "$output_report"
        "--n-workers" "$N_WORKERS"
        "--log-level" "MAIN_INFO"
    )
    
    # Add dry-run flag if enabled
    if $DRY_RUN; then
        cmd_args+=("--dry-run")
        log_info "Running in DRY-RUN mode (no actual cleanup)"
    else
        log_warning "Running in CLEANUP mode (contaminated directories will be removed)"
    fi
    
    # Run the analysis with parallel processing
    if python "${cmd_args[@]}" > "$log_file" 2>&1; then
        log_success "Completed analysis for $system_name ($analysis_type)"
        if $VERBOSE; then
            log_info "Report saved: $output_report"
            log_info "Log saved: $log_file"
        fi
        return 0
    else
        log_error "Failed analysis for $system_name ($analysis_type)"
        log_error "Check log file: $log_file"
        return 1
    fi
}

# Function to process a single modeling system
process_system() {
    local system="$1"
    local system_path="${BASE_DIR}/${system}"
    
    log_info "Processing system: $system"
    
    if $VERBOSE; then
        log_info "System path: $system_path"
    fi
    
    # Check if system directory exists
    if ! check_directory "$system_path" "System directory"; then
        log_error "Skipping system: $system"
        return 1
    fi
    
    if $VERBOSE; then
        log_info "System directory exists, defining paths..."
    fi
    
    # Define paths
    local af3_cluster_wo_tmpl="${system_path}/AF3/Cluster_wo_tmpl"
    local af3_cluster_w_tmpl="${system_path}/AF3/Cluster_w_tmpl"
    local msa_jkhmmer="${system_path}/MSA_JKHmmer"
    local msa_jkhmmer_tmpl="${system_path}/MSA_JKHmmer_tmpl"
    
    if $VERBOSE; then
        log_info "Checking MSA reference directories..."
    fi
    
    # Check MSA directories (these are used as reference)
    local msa_ref_found=false
    if check_directory "$msa_jkhmmer" "MSA_JKHmmer"; then
        msa_ref_found=true
    fi
    
    if ! $msa_ref_found; then
        log_error "No reference MSA directory found for system: $system"
        return 1
    fi
    
    if $VERBOSE; then
        log_info "MSA reference found, starting analysis..."
    fi
    
    local success_count=0
    local total_count=0
    
    # Process Cluster_wo_tmpl
    if check_directory "$af3_cluster_wo_tmpl" "AF3/Cluster_wo_tmpl"; then
        ((total_count++))
        if $VERBOSE; then
            log_info "Starting analysis for Cluster_wo_tmpl..."
        fi
        if run_contamination_analysis "$af3_cluster_wo_tmpl" "$msa_jkhmmer" "$system" "wo_tmpl"; then
            ((success_count++))
        fi
    fi
    
    # Process Cluster_w_tmpl
    if check_directory "$af3_cluster_w_tmpl" "AF3/Cluster_w_tmpl"; then
        ((total_count++))
        # Use MSA_JKHmmer_tmpl if available, otherwise fall back to MSA_JKHmmer
        local msa_ref="$msa_jkhmmer"
        if check_directory "$msa_jkhmmer_tmpl" "MSA_JKHmmer_tmpl"; then
            msa_ref="$msa_jkhmmer_tmpl"
        fi
        
        if $VERBOSE; then
            log_info "Starting analysis for Cluster_w_tmpl..."
        fi
        if run_contamination_analysis "$af3_cluster_w_tmpl" "$msa_ref" "$system" "w_tmpl"; then
            ((success_count++))
        fi
    fi
    
    log_info "System $system completed: $success_count/$total_count analyses successful"
    return 0
}

# Function to generate summary report
generate_summary_report() {
    local summary_file="${REPORT_DIR}/contamination_summary.txt"
    
    log_info "Generating summary report: $summary_file"
    
    {
        echo "MSA Contamination Analysis Summary"
        echo "=================================="
        echo "Generated: $(date)"
        echo "Base Directory: $BASE_DIR"
        echo ""
        
        echo "Analysis Results:"
        echo "----------------"
        
        local total_reports=0
        local total_contaminated=0
        
        for report in "${REPORT_DIR}"/*.tsv; do
            if [[ -f "$report" ]]; then
                ((total_reports++))
                local report_name=$(basename "$report" .tsv)
                local contaminated_count=$(tail -n +2 "$report" | awk -F'\t' '$2=="True" {count++} END {print count+0}')
                local total_dirs=$(tail -n +2 "$report" | wc -l)
                
                echo "  $report_name: $contaminated_count/$total_dirs contaminated directories"
                ((total_contaminated += contaminated_count))
            fi
        done
        
        echo ""
        echo "Overall Statistics:"
        echo "  Total analysis reports: $total_reports"
        echo "  Total contaminated directories found: $total_contaminated"
        echo ""
        
        echo "Log Files Location: $LOG_DIR"
        echo "Report Files Location: $REPORT_DIR"
        
    } > "$summary_file"
    
    log_success "Summary report generated: $summary_file"
}

# Main execution
main() {
    # Parse command line arguments first
    parse_arguments "$@"
    
    log_info "Starting batch MSA contamination analysis"
    log_info "Script directory: $SCRIPT_DIR"
    log_info "Base directory: $BASE_DIR"
    log_info "Config file: $CONFIG_FILE"
    log_info "Mode: $(if $DRY_RUN; then echo "DRY-RUN (safe)"; else echo "CLEANUP (removes contaminated dirs)"; fi)"
    log_info "Workers: $(if [[ $N_WORKERS -eq 0 ]]; then echo "auto-detect"; else echo "$N_WORKERS"; fi)"
    log_info "Verbose: $VERBOSE"
    
    # Check if analysis script exists
    if [[ ! -f "$ANALYSIS_SCRIPT" ]]; then
        log_error "Analysis script not found: $ANALYSIS_SCRIPT"
        exit 1
    fi
    
    # Extract systems from config
    log_info "Extracting systems from config file"
    
    # Source the config file to get systems array directly
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "Config file not found: $CONFIG_FILE"
        exit 1
    fi
    
    source "$CONFIG_FILE"
    
    if [[ ${#systems[@]} -eq 0 ]]; then
        log_error "No systems found in config file"
        exit 1
    fi
    
    local system_count=${#systems[@]}
    log_info "Found $system_count systems to process"
    
    # Debug: Show first few systems
    if $VERBOSE; then
        log_info "First 3 systems to process:"
        for i in {0..2}; do
            if [[ $i -lt ${#systems[@]} ]]; then
                log_info "  - ${systems[$i]}"
            fi
        done
    fi
    
    # Show warning for cleanup mode
    if ! $DRY_RUN; then
        log_warning "CLEANUP MODE ENABLED - Contaminated directories will be removed!"
        log_warning "Press Ctrl+C within 5 seconds to cancel..."
        sleep 5
    fi
    
    # Process each system
    local processed=0
    local successful=0
    local start_time=$(date +%s)
    
    for system in "${systems[@]}"; do
        processed=$((processed + 1))
        log_info "Processing system $processed/$system_count: $system"
        
        if $VERBOSE; then
            log_info "About to call process_system for: $system"
        fi
        
        if process_system "$system"; then
            successful=$((successful + 1))
            if $VERBOSE; then
                log_info "Successfully processed system: $system"
            fi
        else
            if $VERBOSE; then
                log_info "Failed to process system: $system"
            fi
        fi
        
        if $VERBOSE; then
            echo "----------------------------------------"
        fi
        
        if $VERBOSE; then
            log_info "Completed iteration for system: $system"
            log_info "Moving to next system..."
        fi
    done
    
    if $VERBOSE; then
        log_info "Finished processing all systems"
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Generate summary report
    generate_summary_report
    
    # Final summary
    log_info "Batch analysis completed"
    log_success "Processed: $processed systems"
    log_success "Successful: $successful systems"
    log_info "Duration: ${duration} seconds"
    log_info "Reports location: $REPORT_DIR"
    log_info "Logs location: $LOG_DIR"
    
    if [[ $successful -eq $processed ]]; then
        log_success "All systems processed successfully!"
        exit 0
    else
        log_warning "Some systems failed. Check individual log files for details."
        exit 1
    fi
}

# Run main function
main "$@" 