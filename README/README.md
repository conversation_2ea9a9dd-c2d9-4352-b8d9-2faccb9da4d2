# AF3-MSA Optimizer

## Project Overview

**Goal:** To provide an integrated platform that enhances AlphaFold3 (AF3) protein structure prediction by using Multiple Sequence Alignment (MSA) clustering to generate more diverse structures and improve prediction quality.

**Background:** Standard AlphaFold3 is optimized for predicting a single protein structure. This can be a limitation when dealing with proteins that exist in multiple conformational states. By clustering MSAs, we can reduce redundancy and create diverse subsets of sequences. Using these subsets with AF3 can lead to a broader range of predicted structures and potentially higher quality results for specific states.

The AF3-MSA Optimizer project implements this approach by combining MSA clustering techniques with AlphaFold3.

**Key Capabilities:**
- Generation and processing of MSAs, including DBSCAN clustering.
- Flexible execution of AlphaFold3 predictions using various MSA inputs (full, clustered, sampled) and template options.
- Comprehensive analysis and evaluation of prediction results, including standard quality metrics (RMSD, TM-score, pLDDT, PAE) and visualization support.

## Features

This project offers a suite of features organized into modules:

**1. MSA Clustering Module:**
- **MSA Generation:** Generates MSAs using tools like JackHmmer.
- **MSA Clustering:** Implements DBSCAN to cluster MSAs, creating representative subsets for more focused predictions.
- **Visualization & Metrics:** Provides tools to visualize clustering results and assess associated quality metrics.

**2. AF3 Prediction Module:**
- **Versatile Predictions:** Supports AlphaFold3 predictions with various MSA inputs:
    - Full (unmodified) MSA
    - Subsets derived from MSA clusters
    - Uniformly sampled MSA subsets
- **Template Control:** Allows users to include or exclude templates during AlphaFold3 predictions.
- **Efficient Processing:** Designed for batch processing and supports GPU acceleration for AlphaFold3 runs.

**3. Evaluation and Analysis Module:**
- **Automated Quality Assessment:** Automatically calculates key metrics for structure diversity and quality:
    - RMSD (Root Mean Square Deviation)
    - TM-score (Template Modeling score)
    - pLDDT (predicted Local Distance Difference Test)
    - PAE (Predicted Aligned Error)
- **Performance Analysis:** Enables analysis of the relationship between MSA cluster characteristics (e.g., size) and the quality of resulting models.
- **Visualization Integration:** Facilitates quality analysis through integration with tools like ChimeraX.

## Project Structure / Main Components

The project is organized into the following key files and directories:

-   `AF_Cluster/`: Contains scripts for MSA clustering (e.g., `ClusterMSA.py`).
-   `bash_scripts/`: Provides shell scripts for executing AlphaFold3 predictions, such as `AF3C_w-tmpl.sh` (with templates) and `AF3W_wo-tmpl.sh` (without templates).
-   `config/`: Stores configuration files in JSON format for different protein systems or modeling scenarios (e.g., `Asec_695E1E2_clvg.json`).
-   `msa/`: Contains tools for MSA processing.
    -   `msa_pipeline.py`: Script for orchestrating MSA processing steps.
    -   `MSA.md`: A detailed guide on the internal MSA class usage (originally named `MSA_README.md`). **Note: This document is currently in Korean.**
    -   **MSA Class Overview (from `MSA.md`):** The MSA class is a container designed for efficiently processing and managing Multiple Sequence Alignment (MSA) data. It is inspired by AlphaFold3's MSA processing methods and provides functionalities to load, process, and transform MSA data from various formats.
    -   **Main Features of MSA Class (from `MSA.md`):**
        -   Load MSA data from various sources (A3M files, multiple MSA objects, etc.)
        -   Remove duplicate sequences
        -   Convert MSA data to A3M format
        -   Sequence encoding (one-hot encoding, etc.)
        -   Feature extraction
        -   Sequence comparison and similarity checking
-   `utils/`: A collection of utility modules:
    -   `AF3metric_utils.py`: For calculating AlphaFold3 specific metrics.
    -   `chimerax_utils.py`: For interacting with ChimeraX.
    -   `msa_utils.py`: For MSA manipulation and processing.
    -   `structure_utils.py`: For protein structure manipulations.
-   `combinate_a3m_json.py`: Script to combine A3M formatted MSA files into the JSON format required by AlphaFold3.
-   `struct2seq.py`: Utility to map protein structures back to their amino acid sequences.
-   `analyze_af3_model_quality.py`: Script for evaluating the quality of models predicted by AlphaFold3.
-   `run_rankAF3.py`: Script to rank AlphaFold3 models based on quality metrics.
-   `gen_input_json.py`: Script to generate input JSON files for AlphaFold3.
-   `logger_config.py`: Configuration file for application logging.

## Installation / Setup

**[Detailed installation instructions and a full list of dependencies will be provided in this section. Ensure you have Python and access to an AlphaFold3 environment.]**

## Usage / Workflow (High-Level)

This outlines a general workflow for using the AF3-MSA Optimizer:

1.  **Prepare/Generate MSA:** Utilize tools like JackHmmer or scripts within the `msa/` directory (e.g., `msa_pipeline.py` leveraging the MSA class) to generate or prepare your Multiple Sequence Alignment.
2.  **Cluster MSA (Optional):** Employ scripts from the `AF_Cluster/` directory (e.g., `ClusterMSA.py`) to cluster the MSA. This step is key for generating diverse structure predictions.
3.  **Prepare AlphaFold3 Input:** Use `combinate_a3m_json.py` or `gen_input_json.py` to convert your MSA data (full, clustered, or sampled) into the JSON format required by AlphaFold3.
4.  **Run AlphaFold3 Predictions:** Execute AlphaFold3 predictions using the shell scripts provided in `bash_scripts/`. These allow choosing options like template usage.
5.  **Analyze Results:** Evaluate the generated protein structures using `analyze_af3_model_quality.py` for quality metrics and `run_rankAF3.py` to rank the models.
6.  **MSA Details:** For in-depth information on MSA class usage and processing, refer to `msa/MSA.md`. (Reminder: this document is currently in Korean).

## Configuration

The `config/` directory contains JSON files (e.g., `Asec_695E1E2_clvg.json`) that define parameters for different protein systems or specific modeling runs. These files allow for customization of the prediction and analysis pipeline.

## Examples

**[Example scripts, commands, and sample use cases will be detailed in this section. For now, refer to the scripts in `bash_scripts/` for basic command examples.]**

## Contribution Guidelines

**[Guidelines for contributing to the project, including coding standards, pull request procedures, and issue reporting, will be provided in a `CONTRIBUTING.md` file. We welcome contributions!]**

## License

**[This project will be licensed under the [NAME_OF_LICENSE_HERE]. See the `LICENSE` file (to be created) for full details.]**
