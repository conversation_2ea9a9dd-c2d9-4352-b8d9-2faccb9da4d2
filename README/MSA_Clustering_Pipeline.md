# MSA Clustering Pipeline Usage Guide

## Overview
The MSA Clustering Pipeline provides a comprehensive, automated workflow for MSA (Multiple Sequence Alignment) clustering analysis with advanced visualization capabilities.

## Features
- YAML-based configuration management
- Multiple encoding methods (onehot, MSAtransformer)
- Multiple clustering algorithms (DBSCAN, Hierarchical, HDBSCAN)
- Automatic parameter optimization
- Comprehensive visualizations using enhanced MSAVisualizer
- Detailed analysis reports
- Cluster-specific A3M file generation

## Quick Start

### 1. Basic Usage
```bash
# Run with default settings
python msa_clustering_pipeline.py input_file.a3m

# Run with custom configuration
python msa_clustering_pipeline.py input_file.a3m --config my_config.yaml

# Run with verbose output
python msa_clustering_pipeline.py input_file.a3m --verbose
```

### 2. Configuration File Setup

Create a YAML configuration file (e.g., `clustering_config.yaml`):

```yaml
general:
  output_base_dir: './clustering_results'
  chain_poly_type: 'protein'
  max_depth: null
  verbose: false

encoding_methods:
  default: ['onehot']

clustering_methods:
  default: ['dbscan']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 6.0
      min_samples: 3
    optimization:
      min_eps: 3.0
      max_eps: 20.0
      eps_step: 0.5

output_settings:
  save_cluster_a3m_files: true
  save_analysis_results: true
  create_summary_report: true
  subdirectories:
    clusters: 'clusters'
    analysis: 'analysis'
    visualizations: 'visualizations'

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'
  cluster_sequences: true

logging:
  level: 'INFO'
  format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
  date_format: '%Y-%m-%d %H:%M:%S'
```

## Example Usage Scenarios

### Scenario 1: Single Chain Analysis (like KaiB)
```bash
# Create simple config for single chain
cat > kaib_config.yaml << EOF
general:
  output_base_dir: './kaib_clustering_results'
  chain_poly_type: 'protein'

encoding_methods:
  default: ['onehot']

clustering_methods:
  default: ['dbscan']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 10.0
      min_samples: 5

output_settings:
  save_cluster_a3m_files: true
  create_summary_report: true

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'
EOF

# Run analysis
python msa_clustering_pipeline.py /path/to/KaiB_101.a3m --config kaib_config.yaml
```

### Scenario 2: Multi-Chain Analysis (like Asec)
```bash
# Create config for multi-chain analysis
cat > asec_config.yaml << EOF
general:
  output_base_dir: './asec_clustering_results'
  chain_poly_type: 'protein'

encoding_methods:
  default: ['onehot']

clustering_methods:
  default: ['dbscan']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 10.0
      min_samples: 3

output_settings:
  save_cluster_a3m_files: true
  cluster_filename_format: '{filename_base}_{cluster_id:03d}.a3m'

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'
EOF

# Run for multiple chains
for chain in 101 102 103; do
    python msa_clustering_pipeline.py /path/to/Asec_clvg_${chain}.a3m --config asec_config.yaml
done
```

### Scenario 3: Comprehensive Method Comparison
```bash
# Create config testing multiple methods
cat > comprehensive_config.yaml << EOF
general:
  output_base_dir: './comprehensive_clustering_results'

encoding_methods:
  default: ['onehot', 'MSAtransformer']

clustering_methods:
  default: ['dbscan', 'hierarchical', 'hdbscan']

algorithm_parameters:
  dbscan:
    defaults:
      eps: 8.0
      min_samples: 5
  hierarchical:
    defaults:
      n_clusters: null
  hdbscan:
    defaults:
      min_cluster_size: 5

output_settings:
  save_cluster_a3m_files: true
  save_analysis_results: true
  create_summary_report: true

visualization_settings:
  create_msa_plots: true
  similarity_method: 'identity'
EOF

python msa_clustering_pipeline.py input_file.a3m --config comprehensive_config.yaml
```

## Output Structure

The pipeline generates a comprehensive directory structure:

```
clustering_results/
├── {filename}_{encoding}_{clustering}/
│   ├── clusters/
│   │   ├── {filename}_000.a3m
│   │   ├── {filename}_001.a3m
│   │   └── ...
│   ├── analysis/
│   │   ├── clustering_summary.json
│   │   ├── cluster_composition.json
│   │   └── quality_metrics.json
│   └── visualizations/
│       ├── {combination}_msa_conservation_heatmap.pdf
│       ├── {combination}_msa_sequence_similarity.pdf
│       ├── {combination}_cluster_distribution.pdf
│       ├── {combination}_cluster_summary.pdf
│       ├── {combination}_cluster_comparison.pdf
│       └── clusters/
│           ├── {combination}_cluster_000_conservation.pdf
│           ├── {combination}_cluster_000_similarity.pdf
│           └── ...
├── comprehensive_method_comparison.pdf
└── comprehensive_summary.json
```

## Key Advantages over Test Scripts

1. **Configuration-driven**: Easy to modify parameters without code changes
2. **Automated**: Handles multiple encoding/clustering combinations automatically
3. **Comprehensive**: Generates complete analysis with visualizations and reports
4. **Reusable**: Works with any A3M file without hardcoded paths
5. **Scalable**: Easy to add new methods or parameters
6. **Production-ready**: Robust error handling and logging

## Migration from Test Scripts

Instead of running test scripts like:
```python
# Old way - test_enhanced_msa_visualizer.py
python test_enhanced_msa_visualizer.py --output_dir results
```

Use the pipeline:
```bash
# New way - production pipeline
python msa_clustering_pipeline.py input_file.a3m --config config.yaml
```

## Advanced Configuration Options

### Custom Algorithm Parameters
```yaml
algorithm_parameters:
  dbscan:
    defaults:
      eps: 8.5
      min_samples: 5
    optimization:
      min_eps: 3.0
      max_eps: 20.0
      eps_step: 0.5
      min_samples: [3, 5, 7]
  hierarchical:
    defaults:
      linkage: 'ward'
      criterion: 'maxclust'
    optimization:
      min_cluster_size: 19
  hdbscan:
    defaults:
      min_cluster_size: 5
      min_samples: 3
      cluster_selection_epsilon: 0.0
```

### Visualization Customization
```yaml
visualization_settings:
  create_msa_plots: true
  similarity_method: 'blosum62'  # or 'identity'
  cluster_sequences: true
  fallback_to_text_summary: true
```

### Output Customization
```yaml
output_settings:
  save_cluster_a3m_files: true
  save_analysis_results: true
  create_summary_report: true
  cluster_filename_format: '{filename_base}_{cluster_id:03d}.a3m'
  subdirectories:
    clusters: 'cluster_msas'
    analysis: 'analysis_results'
    visualizations: 'plots'
```

## Troubleshooting

### Common Issues

1. **Memory Issues**: Reduce `max_depth` or use sampling
2. **No Clusters Found**: Adjust clustering parameters
3. **Visualization Failures**: Check MSAVisualizer installation

### Debug Mode
```bash
python msa_clustering_pipeline.py input_file.a3m --config config.yaml --verbose
```

## Performance Considerations

- For large MSAs (>10k sequences), consider setting `max_depth`
- MSAtransformer encoding is slower but may give better results
- DBSCAN is generally fastest, Hierarchical most memory-intensive
- Visualization generation can be disabled for faster processing 