# MSA Contamination 종합 해결 가이드

## 📋 목차
1. [빠른 시작 가이드](#빠른-시작-가이드)
2. [문제 상황 분석](#문제-상황-분석)
3. [해결 방안 개요](#해결-방안-개요)
4. [배치 자동 분석](#배치-자동-분석)
5. [개별 수동 분석](#개별-수동-분석)
6. [고급 분석 도구](#고급-분석-도구)
7. [실전 적용 시나리오](#실전-적용-시나리오)
8. [근본적 해결방안](#근본적-해결방안)
9. [성능 최적화](#성능-최적화)
10. [문제 해결 FAQ](#문제-해결-faq)
11. [참고 자료](#참고-자료)

---

## 🚀 빠른 시작 가이드

### 사용자별 요약

#### 🔄 배치 처리 사용자 (22개 시스템 일괄 분석)
```bash
# 1. 안전한 전체 분석 (권장)
./batch_contamination_analysis.sh --dry-run --verbose

# 2. 결과 확인 후 실제 정리
./batch_contamination_analysis.sh --no-dry-run --n-workers 8
```
**대상**: `MODELING_SYSTEMS.config`에 정의된 모든 시스템을 한 번에 처리하고 싶은 사용자

#### 🔍 개별 분석 사용자 (특정 시스템 상세 분석)
```bash
# 1. 현재 상태 점검
python analyze_msa_contamination.py /path/to/AF3/Cluster_wo_tmpl \
    --original-msa-dir /path/to/MSA_JKHmmer \
    --dry-run

# 2. 백업과 함께 정리
python analyze_msa_contamination.py /path/to/AF3/Cluster_wo_tmpl \
    --original-msa-dir /path/to/MSA_JKHmmer \
    --backup-dir /backup
```
**대상**: 특정 시스템을 세밀하게 분석하거나 커스터마이징이 필요한 사용자

#### 🛠️ 문제 해결 사용자 (근본 원인 분석 및 예방)
- [문제 상황 분석](#문제-상황-분석) 섹션부터 시작
- [근본적 해결방안](#근본적-해결방안)으로 스크립트 개선
- [고급 분석 도구](#고급-분석-도구)로 상세 진단

---

## 🔍 문제 상황 분석

### 기존 문제점
AlphaFold3 MSA clustering 과정에서 다음과 같은 문제들이 발생:

1. **Forward Contamination**: DBSCAN clustering 실패 시 원본 full MSA가 clustered MSA 디렉토리에 복사됨
2. **Reverse Detection 어려움**: 완료된 AF3 결과에서 어떤 `MSAsub_{XXX}`가 full MSA를 사용했는지 식별 곤란
3. **검증 부족**: Clustering 결과의 품질 검증 로직 부재
4. **Fallback 전략 미흡**: Clustering 실패 시 적절한 대안 전략 없음

### 문제 발생 위치
```bash
# AF3C_wo-tmpl.sh 및 AF3C_w-tmpl.sh의 문제 부분
if [[ ${?} -ne 0 ]]; then
    echo "   > Error: ${a3m_f} failed to clustering for subsampling"
    echo "   > copying ${a3m_f} to ${proj_p}/${msa_dir}/chain${chain_num}"
    cp ${a3m_f} ${chain_dir}  # <- 여기서 full MSA가 복사됨
fi
```

### 분석 대상 구조
```
/home/<USER>/PROject/APP-complex/Initial_Input/{system}/
├── AF3/
│   ├── Cluster_wo_tmpl/     # 템플릿 없는 클러스터링 결과 분석
│   └── Cluster_w_tmpl/      # 템플릿 있는 클러스터링 결과 분석
├── MSA_JKHmmer/            # 원본 MSA (비교 기준)
└── MSA_JKHmmer_tmpl/       # 템플릿용 MSA (비교 기준)
```

---

## 🛠️ 해결 방안 개요

### 단계별 해결 전략
1. **즉시 해결**: 기존 contaminated MSAsub 디렉토리 정리
2. **고급 분석**: 완료된 AF3 결과에서 contamination 역추적
3. **근본 해결**: Bash 스크립트 및 clustering 로직 개선
4. **예방 조치**: 정기 점검 및 모니터링 시스템 구축

### 사용 가능한 도구
- **batch_contamination_analysis.sh**: 22개 시스템 자동 일괄 처리
- **analyze_msa_contamination.py**: 종합 분석 도구 (Forward + Reverse) - **병렬 처리 지원**
- **improve_clustering_logic.py**: 개선된 clustering 로직

### 병렬 처리 지원
두 도구 모두 병렬 처리 기능이 추가되어 대량의 MSAsub 디렉토리를 효율적으로 처리할 수 있습니다:

- **자동 워커 수 감지**: CPU 코어 수에 따라 자동으로 최적 워커 수 결정
- **사용자 지정 워커 수**: `--n-workers` 옵션으로 워커 수 수동 설정
- **스레드 기반 병렬화**: I/O 바운드 작업에 최적화된 스레드 풀 사용
- **진행 상황 표시**: 실시간 진행 상황 모니터링
- **스레드 안전 로깅**: 멀티스레드 환경에서 안전한 로깅 시스템

---

## 🔄 배치 자동 분석

### 개요
`batch_contamination_analysis.sh`는 `MODELING_SYSTEMS.config`에 정의된 모든 모델링 시스템에 대해 MSA contamination 분석을 자동으로 수행하는 스크립트입니다.

### 주요 기능
- **자동화된 분석**: 22개 모델링 시스템에 대한 일괄 처리
- **병렬 처리**: 각 분석에서 멀티스레드 처리로 성능 향상
- **안전한 실행**: 기본 dry-run 모드로 안전한 테스트
- **상세한 로깅**: 시스템별 개별 로그 파일 생성
- **종합 보고서**: 전체 분석 결과 요약

### 기본 사용법

#### 안전한 dry-run 분석
```bash
# 모든 시스템에 대해 dry-run 분석 (권장)
./batch_contamination_analysis.sh --dry-run

# 상세한 로그와 함께 dry-run
./batch_contamination_analysis.sh --dry-run --verbose
```

#### 실제 정리 작업
```bash
# 실제 contamination 정리 (주의!)
./batch_contamination_analysis.sh --no-dry-run

# 8개 워커로 병렬 처리하며 정리
./batch_contamination_analysis.sh --no-dry-run --n-workers 8
```

### 명령행 옵션

| 옵션 | 설명 | 기본값 |
|------|------|--------|
| `--dry-run` | 안전한 시뮬레이션 모드 | `true` |
| `--no-dry-run` | 실제 정리 작업 수행 | - |
| `--n-workers N` | 병렬 워커 수 (0: 자동 감지) | `0` |
| `--verbose` | 상세한 로깅 활성화 | `false` |
| `--help, -h` | 도움말 표시 | - |

### 출력 파일

#### 디렉토리 구조
```
src/
├── contamination_reports/          # 분석 보고서
│   ├── a-secretase_Asec_clvg_wo_tmpl_contamination.tsv
│   ├── a-secretase_Asec_clvg_w_tmpl_contamination.tsv
│   ├── ...
│   └── contamination_summary.txt   # 종합 요약 보고서
└── contamination_logs/             # 상세 로그
    ├── a-secretase_Asec_clvg_wo_tmpl.log
    ├── a-secretase_Asec_clvg_w_tmpl.log
    └── ...
```

#### 보고서 내용
- `msasub_directory`: MSAsub 디렉토리 이름
- `is_contaminated`: contamination 여부 (True/False)
- `contamination_details`: 상세 contamination 정보
- `recommendation`: 권장 조치 (remove/keep)

### 실행 예시

#### 1. 안전한 전체 분석
```bash
./batch_contamination_analysis.sh --dry-run --verbose
```

**예상 출력:**
```
[INFO] 2025-05-30 14:19:11 - Starting batch MSA contamination analysis
[INFO] 2025-05-30 14:19:11 - Mode: DRY-RUN (safe)
[INFO] 2025-05-30 14:19:11 - Workers: auto-detect
[INFO] 2025-05-30 14:19:11 - Found 22 systems to process
[INFO] 2025-05-30 14:19:11 - Processing system 1/22: a-secretase/Asec_clvg
[SUCCESS] 2025-05-30 14:19:15 - Completed analysis for a-secretase/Asec_clvg (wo_tmpl)
...
[SUCCESS] 2025-05-30 14:25:30 - All systems processed successfully!
```

#### 2. 실제 정리 작업
```bash
# 주의: 실제로 contaminated 디렉토리가 삭제됩니다!
./batch_contamination_analysis.sh --no-dry-run --n-workers 4
```

**안전 확인:**
```
[WARNING] CLEANUP MODE ENABLED - Contaminated directories will be removed!
[WARNING] Press Ctrl+C within 5 seconds to cancel...
```

---

## 🔍 개별 수동 분석

### analyze_msa_contamination.py (권장)

#### 기본 사용법
```bash
# 1. 현재 상태 점검 (Dry run)
python analyze_msa_contamination.py /path/to/AF3/Cluster_wo_tmpl \
    --original-msa-dir /path/to/MSA_JKHmmer \
    --dry-run \
    --output-report contamination_analysis.tsv

# 2. 백업과 함께 정리 수행
python analyze_msa_contamination.py /path/to/AF3/Cluster_wo_tmpl \
    --original-msa-dir /path/to/MSA_JKHmmer \
    --backup-dir /path/to/backup \
    --output-report cleanup_report.tsv

# 3. 실제 예시 (사용자 제공 경로 기준)
python analyze_msa_contamination.py \
    ~/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/AF3/Cluster_wo_tmpl \
    --original-msa-dir ~/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/MSA_JKHmmer \
    --dry-run

# 백업과 함께 정리
python analyze_msa_contamination.py \
    ~/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/AF3/Cluster_wo_tmpl \
    --original-msa-dir ~/PROject/APP-complex/Initial_Input/a-secretase/Asec_clvg/MSA_JKHmmer \
    --backup-dir ~/backup
```

#### 주요 파라미터
- `target_directory`: MSAsub_XXX 디렉토리들이 포함된 AF3 결과 디렉토리 (필수)
- `--original-msa-dir`: 원본 full MSA 파일들이 있는 디렉토리 (필수)
- `--similarity-threshold`: 시퀀스 유사도 임계값 (기본값: 0.95)
- `--backup-dir`: 제거 전 백업 디렉토리
- `--dry-run`: 시뮬레이션만 수행 (실제 삭제 안함)
- `--n-workers`: 병렬 워커 수 (0: 자동 감지, 기본값: 0)

#### 성능 향상
- **병렬 처리**: 다중 MSAsub 디렉토리를 동시에 분석하여 처리 시간 대폭 단축
- **자동 최적화**: CPU 코어 수에 따라 최적 워커 수 자동 결정
- **예상 성능**: 100개 MSAsub 디렉토리 기준 약 2-4배 속도 향상 (시스템 사양에 따라 차이)

#### 시퀀스 기반 분석 방법
- **정확한 탐지**: 시퀀스 크기가 아닌 실제 시퀀스 내용 비교
- **95% 임계값**: full MSA 시퀀스의 95% 이상이 clustered MSA에 포함되면 contamination으로 판정
- **Set 기반 효율성**: Python set 자료형을 활용한 빠른 교집합 연산

---

## 🔬 고급 분석 도구

### Reverse Analysis 작동 원리

#### Chain of Thought 분석 로직:
1. **JSON 파일 분석**:
   ```
   MSAsub_XXX/*.json → unpairedMsa/pairedMsa 크기 추출 → 임계값 비교
   ```

2. **Contamination Score 계산**:
   - Large MSA (>1000 sequences): +10-15점
   - High average MSA size (>100): +5점
   - Multiple large MSAs: 점수 누적

3. **구조 품질 지표**:
   - 모델 파일 크기 (>10MB): 대용량 MSA 사용 가능성
   - 완전한 모델 세트 (25개): 정상적인 AF3 실행 확인

4. **위험도 분류**:
   - **Critical** (20+점): 즉시 제거 권장
   - **High** (15-19점): 제거 권장
   - **Medium** (10-14점): 추가 조사 필요
   - **Low** (5-9점): 모니터링
   - **Minimal** (<5점): 유지

### 출력 보고서 해석

#### 주요 컬럼:
- `reverse_contamination_score`: 역추적 분석 위험도 점수
- `reverse_indicators`: 발견된 suspicious patterns
- `forward_full_msa_files`: Forward 분석에서 발견된 full MSA 파일 수
- `combined_risk_level`: 종합 위험도
- `final_recommendation`: 최종 권장사항

### 고급 파라미터 설정

#### 임계값 조정
```bash
# 민감도 높임 (더 많은 contamination 탐지)
--threshold-ratio 3.0 --min-full-msa-size 500

# 민감도 낮춤 (확실한 것만 탐지)
--threshold-ratio 10.0 --min-full-msa-size 2000
```

#### 정리 전략
```bash
# 보수적 정리 (critical만)
--risk-threshold critical

# 적극적 정리 (medium 이상)
--risk-threshold medium
```

---

## 💼 실전 적용 시나리오

### 시나리오 1: 이미 완료된 AF3 결과 분석
```bash
# Step 1: 현재 상태 파악
python analyze_msa_contamination.py /home/<USER>/AF3/Cluster_wo_tmpl \
    --analysis-mode reverse \
    --dry-run \
    --output-report reverse_analysis.tsv

# Step 2: 결과 검토 후 정리
python analyze_msa_contamination.py /home/<USER>/AF3/Cluster_wo_tmpl \
    --analysis-mode reverse \
    --backup-dir /backup/contaminated \
    --risk-threshold high
```

### 시나리오 2: 종합 검증 및 정리
```bash
# Step 1: 전체 분석
python analyze_msa_contamination.py /home/<USER>/AF3/Cluster_wo_tmpl \
    --analysis-mode both \
    --dry-run \
    --output-report comprehensive_analysis.tsv

# Step 2: 보고서 검토 후 선별적 정리
python analyze_msa_contamination.py /home/<USER>/AF3/Cluster_wo_tmpl \
    --backup-dir /backup/contaminated \
    --risk-threshold critical  # 가장 위험한 것만 먼저 정리
```

### 시나리오 3: 정기 점검
```bash
# 여러 디렉토리 일괄 점검
for dir in Cluster_wo_tmpl Cluster_w_tmpl; do
    python analyze_msa_contamination.py /path/to/AF3/${dir} \
        --analysis-mode both \
        --dry-run \
        --output-report ${dir}_check.tsv
done
```

### 시나리오 4: 배치 vs 개별 분석 선택
```bash
# 모든 시스템을 빠르게 확인하고 싶은 경우
./batch_contamination_analysis.sh --dry-run --verbose

# 특정 시스템만 상세히 분석하고 싶은 경우
python analyze_msa_contamination.py /specific/system/path \
    --original-msa-dir /specific/msa/path \
    --analysis-mode both \
    --dry-run
```

---

## 🔧 근본적 해결방안

### 1. Bash 스크립트 수정

#### AS-IS (현재 문제 코드):
```bash
if [[ ${?} -ne 0 ]]; then
    echo "   > Error: ${a3m_f} failed to clustering for subsampling"
    echo "   > copying ${a3m_f} to ${proj_p}/${msa_dir}/chain${chain_num}"
    cp ${a3m_f} ${chain_dir}  # 문제: full MSA 복사
fi
```

#### TO-BE (개선된 코드):
```bash
if [[ ${?} -ne 0 ]]; then
    echo "   > Error: ${a3m_f} failed to clustering for subsampling"
    echo "   > Creating uniform clusters as fallback"
    
    # Enhanced clustering with validation
    python ${src}/improve_clustering_logic.py ${proj_p} \
        --system-name ${system_name} \
        --cluster-script ${MSA_sub}/scripts/ClusterMSA.py \
        --msa-dir ${msa_dir} \
        --resid-ranges "${resid_ranges[@]}"
        
    if [[ ${?} -ne 0 ]]; then
        echo "   > ERROR: Enhanced clustering also failed for ${a3m_f}"
        echo "   > Skipping this MSA file to prevent full MSA contamination"
        continue  # full MSA 복사 대신 건너뛰기
    fi
fi
```

### 2. 개선된 Clustering 로직

#### improve_clustering_logic.py 사용법:
```bash
# Enhanced clustering 실행
python improve_clustering_logic.py /path/to/project \
    --system-name "Asec_clvg_TMD" \
    --cluster-script "/path/to/AF_Cluster/scripts/ClusterMSA.py" \
    --resid-ranges "1,100" "101,200" \
    --use-paired-cluster
```

#### 주요 개선사항:
1. **Clustering 결과 검증**: 클러스터 크기, 개수 등을 검증하여 품질 확인
2. **Smart Fallback**: Full MSA 대신 uniform random sampling 사용
3. **재시도 로직**: 기존 clustering이 invalid한 경우 재수행

---

## ⚡ 성능 최적화

### 권장 워커 수

| 시스템 사양 | 권장 워커 수 | 배치 분석 | 개별 분석 |
|-------------|--------------|-----------|-----------|
| 4-8 코어 | 자동 감지 | `--n-workers 0` | `--n-workers 0` |
| 8-16 코어 | 6-8 워커 | `--n-workers 8` | `--n-workers 8` |
| 16+ 코어 | 10-12 워커 | `--n-workers 12` | `--n-workers 12` |

### 예상 처리 시간

#### 배치 분석
- **Dry-run 모드**: 시스템당 약 30-60초
- **전체 22 시스템**: 약 20-40분 (병렬 처리 시)
- **실제 정리 모드**: 추가 5-10분 (백업 및 삭제)

#### 개별 분석
- **단일 시스템**: 약 2-5분 (MSAsub 개수에 따라)
- **병렬 처리 효과**: 100개 MSAsub 기준 약 2-4배 속도 향상

### 고급 사용법

#### 배치 분석에서 특정 시스템만 분석
```bash
# MODELING_SYSTEMS.config를 임시로 수정
cp MODELING_SYSTEMS.config MODELING_SYSTEMS.config.backup
echo 'systems=("a-secretase/Asec_clvg")' > MODELING_SYSTEMS.config

# 분석 실행
./batch_contamination_analysis.sh --dry-run

# 원본 복구
mv MODELING_SYSTEMS.config.backup MODELING_SYSTEMS.config
```

#### 결과 통계 분석
```bash
# 전체 contamination 통계
awk -F'\t' 'NR>1 {if($2=="True") cont++; total++} END {print "Contaminated: " cont "/" total " (" cont/total*100 "%)"}' contamination_reports/*.tsv

# 시스템별 contamination 비율
for file in contamination_reports/*_contamination.tsv; do
    system=$(basename "$file" _contamination.tsv)
    ratio=$(awk -F'\t' 'NR>1 {if($2=="True") cont++; total++} END {print cont/total*100}' "$file")
    echo "$system: ${ratio}%"
done
```

---

## ❓ 문제 해결 FAQ

### 배치 분석 관련

#### Q: "Analysis script not found" 오류
**A**: analyze_msa_contamination.py가 같은 디렉토리에 있는지 확인
```bash
ls -la analyze_msa_contamination.py
```

#### Q: "No systems found in config file" 오류
**A**: MODELING_SYSTEMS.config 파일 확인
```bash
head -10 MODELING_SYSTEMS.config
```

#### Q: "System directory not found" 오류
**A**: 시스템 경로 확인
```bash
ls -la /home/<USER>/PROject/APP-complex/Initial_Input/a-secretase/
```

### 개별 분석 관련

#### Q: Reverse analysis에서 JSON 파일을 찾을 수 없다는 오류
**A**: MSAsub 디렉토리에 `*.json` 파일이 있는지 확인. `combinate_a3m_json.py` 실행 결과물이어야 함.

#### Q: 모든 MSAsub가 high risk로 분류됨
**A**: `--threshold-ratio`와 `--min-full-msa-size` 파라미터를 조정하여 임계값 재설정.

#### Q: Forward와 Reverse 결과가 다름
**A**: 정상적인 현상. Forward는 cluster 파일을, Reverse는 JSON 파일을 분석하므로 결과가 다를 수 있음.

#### Q: 백업 없이 실수로 삭제함
**A**: 항상 `--backup-dir` 옵션 사용 권장. 삭제된 경우 원본 clustering 재수행 필요.

### 일반적인 문제

#### 로그 확인
```bash
# 배치 분석 로그 확인
tail -f contamination_logs/a-secretase_Asec_clvg_wo_tmpl.log

# 모든 에러 로그 검색
grep -r "ERROR" contamination_logs/

# 개별 분석 디버깅
python analyze_msa_contamination.py /path --log-level DEBUG
```

### 결과 해석

#### Contamination 판정 기준
- **95% 유사도**: 원본 MSA 시퀀스의 95% 이상이 클러스터링된 MSA에 포함
- **시퀀스 기반 비교**: 파일 크기가 아닌 실제 시퀀스 내용 비교
- **체인별 분석**: 각 체인의 MSA 파일을 개별적으로 분석

#### 권장 조치
| Contamination 상태 | 권장 조치 | 설명 |
|-------------------|-----------|------|
| `True` | `remove` | 해당 MSAsub 디렉토리 삭제 권장 |
| `False` | `keep` | 정상적인 클러스터링 결과로 유지 |

---

## 🔍 검증 방법

### 정리 후 확인
```bash
# MSAsub 디렉토리 개수 확인
ls -la /path/to/AF3/Cluster_wo_tmpl/MSAsub_* | wc -l

# 남은 디렉토리들의 상태 재점검
python analyze_msa_contamination.py /path/to/AF3/Cluster_wo_tmpl --dry-run
```

### Clustering 품질 확인
```bash
# 각 chain 디렉토리의 클러스터 파일들 확인
find /path/to/MSA_JKHmmer/chain* -name "*.a3m" -exec wc -l {} \; | sort -n
```

---

## ⚠️ 주의사항

### 중요한 안전 수칙

1. **항상 dry-run 먼저**: 실제 정리 전에 반드시 dry-run으로 확인
2. **백업 확인**: 중요한 데이터는 별도 백업 후 진행
3. **실행 중 모니터링**: 로그 파일을 통해 진행 상황 확인
4. **디스크 공간**: 보고서 및 로그 파일을 위한 충분한 공간 확보
5. **AF3 예측 중단**: MSAsub 디렉토리 정리 시 해당 AF3 예측 작업이 실행 중이지 않은지 확인
6. **로그 보관**: 모든 작업의 로그를 보관하여 추후 참조

---

## 📚 참고 자료

### 지원 및 문의
스크립트 사용 중 문제 발생 시:

1. **로그 파일 확인**: `contamination_logs/` 디렉토리의 해당 로그 파일
2. **Verbose 모드 실행**: `--verbose` 옵션으로 상세 정보 확인
3. **개별 시스템 테스트**: 문제가 있는 시스템만 별도로 테스트
4. **Debug 모드**: `--log-level DEBUG`로 상세 로그 확인
5. **로그 파일 저장**: `--log-file`로 로그 파일 저장

### 향후 개선 계획

1. **구조 신뢰도 분석**: CIF 파일에서 pLDDT, PAE 추출하여 더 정확한 contamination 탐지
2. **자동화된 재실행**: Contaminated MSAsub 제거 후 자동으로 올바른 clustering으로 재실행
3. **실시간 모니터링**: AF3 실행 중 contamination 실시간 탐지 및 경고

### 변경 이력

- **2024-01**: 초기 문제 분석 및 기본 해결 방안 수립
- **2024-01**: analyze_msa_contamination.py 종합 분석 도구 개발
- **2024-01**: Reverse analysis 기능 추가
- **2025-05-30**: batch_contamination_analysis.sh 배치 처리 도구 추가
- **2025-01**: 종합 가이드 문서 통합 완료 